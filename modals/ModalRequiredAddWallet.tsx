"use client";

import { BaseModal } from "@/modals/BaseModal";
import React, { useState } from "react";
import { AppButton } from "@/components";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import { ModalImportWallet } from "@/modals/ModalImportWallet";
import { setIsShowModalAddWallet } from "@/store/metadata.store";
import { TWallet } from "@/types";
import rf from "@/services/RequestFactory";
import { getWalletsUser } from "@/store/user.store";
import { toastError } from "@/libs/toast";
import { ModalGenerateNewWallet } from "@/modals/ModalGenerateNewWallet";
import { BgAddWallet } from "@/public/images";
import Image from "next/image";
import { NETWORKS } from "@/utils/contants";
import { useNetwork } from "@/context";

const ButtonGenerateWallet = () => {
  const [isOpenGenerateWalletModal, setIsOpenGenerateWalletModal] =
    useState<boolean>(false);
  const [newWallets, setNewWallets] = useState<TWallet[]>([]);
  const { currentNetwork } = useNetwork();

  const dispatch = useDispatch<AppDispatch>();
  const onGenerateWallet = async (numberWallets: number) => {
    try {
      const res = await rf
        .getRequest("WalletRequest")
        .getGenerateWallets(currentNetwork, {
          numberWallets,
        });
      setNewWallets(res);
      dispatch(getWalletsUser({ network: currentNetwork }));
      setIsOpenGenerateWalletModal(true);
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };
  return (
    <>
      <AppButton
        variant="buy"
        className="mb-2 flex w-full flex-col"
        size="large"
        onClick={() => onGenerateWallet(1)}
      >
        <div>Generate new wallet</div>
        <div className="body-sm-regular-12 text-brand-600">
          Do not have a wallet
        </div>
      </AppButton>

      {isOpenGenerateWalletModal && (
        <ModalGenerateNewWallet
          wallets={newWallets}
          isOpen={isOpenGenerateWalletModal}
          onClose={() => {
            setIsOpenGenerateWalletModal(false);
            dispatch(setIsShowModalAddWallet({ isShow: false }));
          }}
        />
      )}
    </>
  );
};

export const ModalRequiredAddWallet = () => {
  const [isShowModalImport, setIsShowModalImport] = useState<boolean>(false);
  const isOpen = useSelector(
    (state: RootState) => state.metadata.isShowModalAddWallet
  );
  const dispatch = useDispatch<AppDispatch>();

  return (
    <>
      <BaseModal
        closable={false}
        className="w-[calc(100vw-16px)] max-w-[420px]"
        isOpen={isOpen}
        headerClassName="mb-0"
      >
        <div>
          <Image src={BgAddWallet} className="w-full" alt="BgAddWallet" />
          <div className="body-md-semibold-14 mt-4 text-center">
            You must have a wallet to continue
          </div>
          <div className="body-sm-regular-12 text-white-500 mb-4 mt-1 text-center">
            Click Import wallet or Generate to obtain your RaidenX trading
            wallet
          </div>
          <ButtonGenerateWallet />
          <AppButton
            onClick={() => setIsShowModalImport(true)}
            variant="outline"
            className="flex w-full flex-col"
            size="large"
          >
            <div>Import Wallet</div>
            <div className="body-sm-regular-12 text-white-500">
              Import your private keys
            </div>
          </AppButton>
        </div>
      </BaseModal>

      {isShowModalImport && (
        <ModalImportWallet
          onSuccess={() => dispatch(setIsShowModalAddWallet({ isShow: false }))}
          isOpen={isShowModalImport}
          onClose={() => setIsShowModalImport(false)}
        />
      )}
    </>
  );
};

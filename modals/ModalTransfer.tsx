"use client";

import { BaseModal } from "@/modals/BaseModal";
import React, { useEffect, useMemo, useState } from "react";
import {
  AppButton,
  AppSelectWallet,
  AppNumber,
  ButtonScanQR,
  AppSelectToken,
} from "@/components";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { NumericFormat } from "react-number-format";
import BigNumber from "bignumber.js";
import {
  dividedBN,
  getSymbolTokenNative,
  isValidSuiAddress,
} from "@/utils/helper";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess } from "@/libs/toast";
import { useMediaQuery } from "react-responsive";
import { NETWORKS } from "@/utils/contants";
import {
  SUI_TOKEN_ADDRESS_FULL,
  SUI_TOKEN_ADDRESS_SHORT,
} from "@/utils/contants";
import { useNetwork } from "@/context/network";

const EST_FEE = 0.01;

export const ModalTransfer = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [fromWallet, setFromWallet] = useState<string>("");
  const [toWallet, setToWallet] = useState<string>("");
  const [amount, setAmount] = useState<any>("");
  const [percent, setPercent] = useState<any>("");
  const [tokenAddress, setTokenAddress] = useState<any>("");
  const [isSubmitting, setIsSubmitting] = useState<any>("");
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const { currentNetwork } = useNetwork();

  const dataFromWallet = useMemo(
    () => wallets.find((item) => item.address === fromWallet),
    [fromWallet]
  );

  // const listToWallet = wallets.filter((item) => item.address !== fromWallet);

  const listFromWallet = wallets.filter((item) => item.address !== toWallet);

  const balances = useSelector((state: RootState) => state.user.balances);

  const tokenBalances = useMemo(() => {
    if (!fromWallet) return [];

    const tokens = balances?.filter((balance: any) => {
      return balance.walletAddress === fromWallet;
    });

    return tokens.map((item: any) => {
      return {
        ...item.token,
        balance: item.balance,
      };
    });
  }, [fromWallet, balances]);

  const dataTokenSelected = useMemo(
    () => tokenBalances?.find((item: any) => item.address === tokenAddress),
    [tokenAddress]
  );

  const balanceAvailable = useMemo(() => {
    if (!dataTokenSelected) return 0;

    if (dataTokenSelected?.symbol !== "SUI") {
      return dataTokenSelected?.balance || 0;
    }

    // with token SUI must minus fee
    if (new BigNumber(dataTokenSelected?.balance || 0).comparedTo(EST_FEE) < 0)
      return 0;

    return new BigNumber(dataTokenSelected?.balance || 0)
      .minus(EST_FEE)
      .toString();
  }, [dataTokenSelected?.balance]) as string;

  useEffect(() => {
    if (!+balanceAvailable) {
      setPercent("0");
      return;
    }

    const newPercent = new BigNumber(dividedBN(amount, balanceAvailable))
      .multipliedBy(100)
      .decimalPlaces(4, BigNumber.ROUND_DOWN)
      .toString();
    setPercent(newPercent);
  }, [balanceAvailable]);

  const handleAmountChange = (amount: string) => {
    if (!amount || !+balanceAvailable) {
      setPercent("0");
      setAmount(amount);
      return;
    }

    const newPercent = new BigNumber(dividedBN(amount, balanceAvailable))
      .multipliedBy(100)
      .decimalPlaces(4, BigNumber.ROUND_DOWN)
      .toString();
    setPercent(newPercent);
    setAmount(amount);
  };

  const handlePercentChange = (percent: string) => {
    if (!percent || !+balanceAvailable) {
      setAmount("0");
      setPercent(percent);
      return;
    }

    const newAmount = new BigNumber(dividedBN(percent, 100))
      .multipliedBy(balanceAvailable)
      .toString();

    setAmount(newAmount);
    setPercent(percent);
  };

  const onTransfer = async () => {
    try {
      setIsSubmitting(true);

      if (
        tokenAddress === SUI_TOKEN_ADDRESS_SHORT ||
        tokenAddress === SUI_TOKEN_ADDRESS_FULL
      ) {
        await rf.getRequest("WithdrawRequest").transfer(currentNetwork, {
          fromWallet,
          toWallet,
          amountIn: amount,
        });
      } else {
        await rf
          .getRequest("WithdrawRequest")
          .transferToken(currentNetwork, tokenAddress, {
            fromWallet,
            toWallet,
            amountIn: amount,
          });
      }

      toastSuccess("Success", "The transfer has been completed successfully!");
      setIsSubmitting(false);
      // dispatch(getWalletsUser({ network: currentNetwork }));
      onClose();
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      setIsSubmitting(false);
      console.error(e);
    }
  };

  const handlePaste = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();
      setToWallet(clipboardText);
    } catch (err) {
      console.error("Failed to read clipboard:", err);
    }
  };

  const isInsufficientBalance = useMemo(() => {
    if (dataTokenSelected?.symbol === "SUI") {
      return (
        new BigNumber(amount).comparedTo(0) > 0 &&
        new BigNumber(amount).comparedTo(balanceAvailable) > 0
      );
    }

    return (
      new BigNumber(amount).comparedTo(0) > 0 &&
      (new BigNumber(amount).comparedTo(balanceAvailable) > 0 ||
        new BigNumber(dataFromWallet?.balance || 0).comparedTo(EST_FEE) < 0)
    );
  }, [amount, balanceAvailable, dataFromWallet?.balance]);

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      title={"Withdraw"}
      onClose={onClose}
      description={
        "Transfer funds from a wallet to an external one or wallet within your account."
      }
      descClassName="max-w-[388px]"
    >
      <div className="mb-[16px]">
        <div className="flex items-center justify-between">
          <div className="action-xs-medium-12 text-white-700 mb-[8px]">
            From Wallet
          </div>
          {!!fromWallet && (
            <div className="body-xs-regular-10 text-white-500 flex gap-1">
              Balance: <AppNumber value={dataFromWallet?.balance} />{" "}
              {getSymbolTokenNative(currentNetwork)}
            </div>
          )}
        </div>
        <AppSelectWallet
          walletAddressSelected={fromWallet}
          wallets={listFromWallet}
          onSelect={setFromWallet}
          isOnlySelect
        />
      </div>

      <div className="mb-[16px]">
        <div className="flex items-center justify-between">
          <div className="action-xs-medium-12 text-white-700 mb-[8px]">
            Select Withdraw Token
          </div>
          {!!tokenAddress && (
            <div className="body-xs-regular-10 text-white-500 flex gap-1">
              Balance: <AppNumber value={dataTokenSelected?.balance} />{" "}
              {dataTokenSelected?.symbol}
            </div>
          )}
        </div>
        <AppSelectToken
          tokenAddressSelected={tokenAddress}
          tokens={tokenBalances}
          onSelect={setTokenAddress}
        />
      </div>

      <div className="mb-[16px]">
        <div className="flex items-center justify-between">
          <div className="action-xs-medium-12 text-white-700 mb-[8px]">
            To Wallet
          </div>
        </div>

        <>
          <div className="border-white-100 bg-white-50 flex items-center justify-between gap-[8px] rounded-[6px] border px-[8px] py-[7px]">
            <div className="flex max-w-[300px] flex-1 flex-wrap gap-2">
              <input
                placeholder={
                  isMobile ? "Long press to paste" : "Enter wallet to withdraw"
                }
                value={toWallet}
                onChange={(e) => {
                  setToWallet(e.target.value.trim());
                }}
                className="body-sm-regular-12 placeholder:text-white-300 w-full flex-1 truncate bg-transparent outline-none md:w-[300px]"
              />
            </div>
            {isMobile ? (
              <ButtonScanQR setData={setToWallet} />
            ) : (
              <div
                onClick={() => {
                  handlePaste().then();
                }}
                className={`action-xs-medium-12 text-brand-500 cursor-pointer`}
              >
                Paste
              </div>
            )}
          </div>

          {toWallet && !isValidSuiAddress(toWallet) && (
            <div className="body-xs-regular-10 text-red-600">
              Invalid wallet
            </div>
          )}
        </>
      </div>

      <div className="grid grid-cols-5 gap-2">
        <div className="col-span-3 w-full">
          <div className="action-xs-medium-12 text-white-700 mb-[8px]">
            Amount
          </div>
          <div>
            <NumericFormat
              placeholder={`Enter amount`}
              value={amount}
              onChange={(e) =>
                handleAmountChange(e.target.value.replace(/,/g, ""))
              }
              thousandSeparator=","
              className="body-sm-regular-12 border-white-100 placeholder:text-white-300 bg-white-50 w-full rounded-[6px] border p-[8px] outline-none"
            />
            {isInsufficientBalance && (
              <div className="body-xs-regular-10 text-red-600">
                Insufficient balance
              </div>
            )}
          </div>
        </div>
        <div className="col-span-2 flex flex-col items-end">
          <div className="body-xs-regular-10 text-white-700 mb-[8px]">
            Est Fee: {EST_FEE} {getSymbolTokenNative(currentNetwork)}
          </div>
          <div className="body-sm-regular-12 border-white-100 bg-white-50 flex gap-2 rounded-[6px] border p-[8px]">
            <NumericFormat
              value={percent}
              onChange={(e) =>
                handlePercentChange(e.target.value.replace(/,/g, ""))
              }
              thousandSeparator=","
              decimalScale={4}
              className="body-sm-regular-12 placeholder:text-white-300 text-neutral-0 w-full bg-transparent outline-none"
            />
            <div className="text-white-500">%</div>
          </div>
        </div>
      </div>

      <div className="mt-[20px] flex justify-center">
        <AppButton
          disabled={
            !isValidSuiAddress(toWallet) ||
            isSubmitting ||
            !fromWallet ||
            !toWallet ||
            !tokenAddress ||
            !amount ||
            new BigNumber(amount).comparedTo(dataTokenSelected?.balance) > 0
          }
          size="large"
          onClick={onTransfer}
          className="w-[160px]"
        >
          {isSubmitting ? "Submitting" : "Confirm"}
        </AppButton>
      </div>
    </BaseModal>
  );
};

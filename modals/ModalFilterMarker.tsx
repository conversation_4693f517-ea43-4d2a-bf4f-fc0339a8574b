"use client";

import React, { useEffect, useState } from "react";
import { CloseIcon } from "@/assets/icons";
import { AppButton, AppUserAddress } from "@/components";
import { AppBroadcast } from "@/libs/broadcast";
import { BROADCAST_EVENTS } from "@/libs/broadcast";
import { BaseModal } from "@/modals/BaseModal";
import { getLinkTxExplorer } from "@/utils/helper";
import { useNetwork } from "@/context";

export const ModalFilterMarker = ({
  isOpen,
  onClose,
  params,
  setParams,
}: {
  isOpen: boolean;
  onClose: () => void;
  params: any;
  setParams: (data: any) => void;
}) => {
  const [address, setAddress] = useState<string>("");
  const [tags, setTags] = useState<string[]>([]);
  const { currentNetwork } = useNetwork();

  useEffect(() => {
    setAddress("");
    setTags(params.makerAddress ? params.makerAddress.split(",") : []);
  }, [params.makerAddress]);

  const handleClearFilter = () => {
    setAddress("");
    setParams({
      ...params,
      makerAddress: "",
    });
    setTags([]);
  };

  const handleApplyFilter = () => {
    setParams({
      ...params,
      makerAddress: tags.join(","),
    });
    AppBroadcast.dispatch(BROADCAST_EVENTS.FILTER_MAKER_IN_CHART, {
      makerAddress: tags.join(","),
    });
    if (!tags.length) {
      AppBroadcast.dispatch(BROADCAST_EVENTS.CLOSE_FILTER_MARKER, {});
    }
    onClose();
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");
    if (pastedText.trim() !== "") {
      setTags([...tags, pastedText]);
    }
  };

  const handleChangeAddress = (value: string) => {
    setAddress(value.trim());
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === " ") {
      if (address.trim() !== "") {
        setTags([...tags, address.trim()]);
        setAddress("");
      }
    }
  };

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      title={"Filter Marker"}
      onClose={onClose}
    >
      <div>
        <input
          placeholder="Enter Wallet Address"
          value={address}
          onPaste={handlePaste}
          onChange={(e) => handleChangeAddress(e.target.value)}
          onKeyDown={(e) => handleKeyDown(e)}
          className="border-white-50 placeholder:text-white-300 bg-black-900 body-sm-regular-12 flex w-full items-center rounded-[6px] border p-[8px] outline-none"
        />
        <div className="mt-4 flex flex-wrap gap-[12px]">
          {tags.map((tag, index) => (
            <div
              key={index}
              className="border-brand-500 flex items-center justify-center rounded-full border px-2 py-1"
            >
              <a
                href={getLinkTxExplorer(currentNetwork, tag)}
                target="_blank"
                className="text-neutral-alpha-800 hover:text-neutral-0"
              >
                <AppUserAddress network={currentNetwork} address={tag} />
              </a>
              <CloseIcon
                onClick={() => setTags(tags.filter((_, i) => i !== index))}
                className="text-white-500 hover:text-white-1000 ml-1 h-2 w-3 cursor-pointer"
              />
            </div>
          ))}
        </div>
      </div>

      <div className="mt-[20px] flex justify-center gap-2">
        <AppButton
          onClick={handleClearFilter}
          variant="outline"
          className="min-w-[100px]"
        >
          Clear
        </AppButton>
        <AppButton className="min-w-[100px]" onClick={handleApplyFilter}>
          Apply
        </AppButton>
      </div>
    </BaseModal>
  );
};

"use client";

import { BaseModal } from "@/modals/BaseModal";
import React, { useEffect, useMemo, useState } from "react";
import clsx from "clsx";
import { NumericFormat } from "react-number-format";
import { TPair, TWallet } from "@/types";
import {
  AppAvatarToken,
  AppButton,
  AppLogoNetwork,
  AppNumber,
  AppSelectWallet,
} from "@/components";
import { MOONBAGS_API_URL, NETWORKS } from "@/utils/contants";
import { useTradingWallet } from "@/hooks/useTradingWallet";
import BigNumber from "bignumber.js";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess } from "@/libs/toast";
import moment from "moment";
import { sleep } from "@/utils/helper";
import _ from "lodash";

const TABS = [
  { value: "STAKE", label: "Stake" },
  { value: "UNSTAKE", label: "Unstake" },
  { value: "CLAIM", label: "Claim" },
];

const FromUnStake = ({
  onClose,
  walletStakingData,
  pair,
}: {
  onClose: () => void;
  walletStakingData: TWalletStaking[];
  pair: TPair;
}) => {
  const [amount, setAmount] = useState("");
  const [wallet, setWallet] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const dataWalletSelected = useMemo(
    () => walletStakingData.find((item) => item.address === wallet),
    [wallet]
  );

  const dataWallet = useMemo(() => {
    let data: TWalletStaking[] = walletStakingData;
    data = _.orderBy(
      data,
      [(wallet: any) => Number(wallet?.stakedAmount)],
      ["desc"]
    );
    return data.map((wallet: TWalletStaking) => {
      return {
        ...wallet,
        balance: wallet?.stakedAmount || "0",
      };
    });
  }, [walletStakingData]);

  useEffect(() => {
    if (!dataWallet.length) return;
    setWallet(dataWallet[0]?.address);
  }, [dataWallet]);

  const invalidBalance = useMemo(() => {
    return BigNumber(dataWalletSelected?.stakedAmount || 0).isLessThan(amount);
  }, [dataWalletSelected, amount]);

  const onUnStake = async () => {
    try {
      if (
        moment(dataWalletSelected?.lastStake, "x")
          .add(1, "hour")
          .isAfter(moment.now())
      ) {
        toastError(
          "Error",
          "You can unstake only 1 hour after your last stake."
        );
        return;
      }
      setIsLoading(true);
      await rf.getRequest("NewOrderRequest").unstakingMoonBags(NETWORKS.SUI, {
        tokenAddress: pair?.tokenBase?.address,
        walletsWithAmount: [
          {
            amount: +amount,
            wallet,
          },
        ],
      });
      await sleep(3000);
      setIsLoading(false);
      toastSuccess("Success", "Unstake successfully!");
      onClose();
    } catch (e: any) {
      setIsLoading(false);
      toastError("Error", e?.message || "Something went wrong!");
      console.error(`onUnStake throw error: ${e?.message} with data`, {
        tokenAddress: pair?.tokenBase?.address,
        walletsWithAmount: [
          {
            amount: +amount,
            wallet,
          },
        ],
      });
    }
  };

  return (
    <div>
      <div>
        <div className="mb-[16px]">
          <div className="flex items-center justify-between">
            <div className="action-xs-medium-12 text-white-700 mb-[8px]">
              Wallet
            </div>
            {!!wallet && (
              <div className="body-xs-regular-10 text-white-500 flex gap-1">
                Staked:{" "}
                <AppNumber value={dataWalletSelected?.stakedAmount || "0"} />{" "}
                {pair?.tokenBase?.symbol}
              </div>
            )}
          </div>
          <AppSelectWallet
            walletAddressSelected={wallet}
            wallets={dataWallet}
            onSelect={(value: string) => {
              setWallet(value);
              setAmount("");
            }}
            isOnlySelect
            symbol={pair?.tokenBase?.symbol}
          />
        </div>
        <div className="body-sm-medium-12 text-white-700 mb-2">
          Unstake Amount
        </div>
        <div
          className={clsx(
            "border-white-100 bg-white-100 flex items-center gap-1 rounded-[6px] border p-2"
          )}
        >
          <NumericFormat
            value={amount}
            onValueChange={(values) => setAmount(values.value)}
            thousandSeparator=","
            decimalSeparator="."
            allowNegative={false}
            placeholder="0.00"
            className="body-md-regular-14 ml-auto flex-1 bg-transparent focus:outline-none"
            allowLeadingZeros={false}
            decimalScale={pair?.tokenBase?.decimals || 9}
            inputMode="numeric"
          />
          <div className="flex items-center gap-2">
            <div
              onClick={() => {
                if (!wallet || !Number(dataWalletSelected?.stakedAmount))
                  return;
                setAmount(dataWalletSelected?.stakedAmount || "0");
              }}
              className="body-sm-regular-12 text-brand-500 cursor-pointer"
            >
              Max
            </div>
            <AppAvatarToken
              size={16}
              image={
                pair?.tokenBase?.logoImageUrl || pair?.tokenBase?.iconUrl || ""
              }
              className="h-4 w-4 rounded-full"
            />
          </div>
        </div>
        <p className="text-white-700 mt-2 text-[10px]">
          Minimum stake duration is one hour before you can unstake.
        </p>

        <AppButton
          disabled={isLoading || !wallet || !+amount || invalidBalance}
          onClick={onUnStake}
          className="mt-6"
          size="large"
        >
          {isLoading ? "Loading..." : "Unstake"}
        </AppButton>
      </div>
    </div>
  );
};

const FromStake = ({ onClose, pair }: { onClose: () => void; pair: TPair }) => {
  const [amount, setAmount] = useState<string>("");
  const [wallet, setWallet] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const { tradingWallets } = useTradingWallet(
    pair?.tokenBase?.address,
    pair?.tokenQuote?.address
  );

  const dataWallet = useMemo(
    () => tradingWallets.find((item: any) => item.address === wallet),
    [wallet, tradingWallets]
  );

  const wallets = useMemo(() => {
    let dataWallet = tradingWallets;
    dataWallet = _.orderBy(
      dataWallet,
      [(wallet: any) => Number(wallet?.baseBalance)],
      ["desc"]
    );
    return dataWallet.map((item: any) => {
      return {
        ...item,
        balance: item.baseBalance,
      };
    });
  }, [tradingWallets]);

  useEffect(() => {
    if (!wallets.length) return;
    setWallet(wallets[0]?.address);
  }, [wallets]);

  const invalidBalance = useMemo(() => {
    return BigNumber(dataWallet?.baseBalance || 0).isLessThan(amount);
  }, [dataWallet, amount]);

  const onStake = async () => {
    try {
      setIsLoading(true);
      await rf.getRequest("NewOrderRequest").stakingMoonBags(NETWORKS.SUI, {
        tokenAddress: pair?.tokenBase?.address,
        walletsWithAmount: [
          {
            amount: +amount,
            wallet,
          },
        ],
      });
      await sleep(3000);
      setIsLoading(false);
      toastSuccess("Success", "Stake successfully!");
      onClose();
    } catch (e: any) {
      setIsLoading(false);
      toastError("Error", e?.message || "Something went wrong!");
      console.error(e);
    }
  };

  return (
    <div>
      <div>
        <div className="mb-[16px]">
          <div className="flex items-center justify-between">
            <div className="action-xs-medium-12 text-white-700 mb-[8px]">
              Wallet
            </div>
            {!!wallet && (
              <div className="body-xs-regular-10 text-white-500 flex gap-1">
                Balance: <AppNumber value={dataWallet?.baseBalance} />{" "}
                {pair?.tokenBase?.symbol}
              </div>
            )}
          </div>
          <AppSelectWallet
            walletAddressSelected={wallet}
            wallets={wallets}
            onSelect={(value: string) => {
              setWallet(value);
              setAmount("");
            }}
            isOnlySelect
            symbol={pair?.tokenBase?.symbol}
          />
        </div>

        <div className="action-xs-medium-12 text-white-700 mb-[8px]">
          Stake Amount
        </div>
        <div
          className={clsx(
            "border-white-100 bg-white-100 flex items-center gap-1 rounded-[6px] border p-2"
          )}
        >
          <NumericFormat
            value={amount}
            onValueChange={(values) => setAmount(values.value)}
            thousandSeparator=","
            decimalSeparator="."
            allowNegative={false}
            placeholder="0.00"
            className="body-md-regular-14 ml-auto flex-1 bg-transparent focus:outline-none"
            allowLeadingZeros={false}
            decimalScale={pair?.tokenBase?.decimals || 9}
            inputMode="numeric"
          />
          <div className="flex items-center gap-2">
            <div
              onClick={() => {
                if (!wallet || !Number(dataWallet?.baseBalance)) return;
                setAmount(dataWallet?.baseBalance || "0");
              }}
              className="body-sm-regular-12 text-brand-500 cursor-pointer"
            >
              Max
            </div>

            <AppAvatarToken
              size={16}
              image={
                pair?.tokenBase?.logoImageUrl || pair?.tokenBase?.iconUrl || ""
              }
              className="h-4 w-4 rounded-full"
            />
          </div>
        </div>
        <p className="text-white-700 mt-2 text-[10px]">
          Minimum stake duration is one hour before you can unstake.
        </p>

        <AppButton
          disabled={isLoading || !wallet || !+amount || invalidBalance}
          onClick={onStake}
          className="mt-6"
          size="large"
        >
          {invalidBalance
            ? "Insufficient funds"
            : isLoading
            ? "Loading..."
            : "Stake"}
        </AppButton>
      </div>
    </div>
  );
};

const TabClaim = ({
  onClose,
  walletStakingData,
  pair,
}: {
  onClose: () => void;
  walletStakingData: TWalletStaking[];
  pair: TPair;
}) => {
  const [wallet, setWallet] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const dataWalletSelected = useMemo(
    () => walletStakingData.find((item) => item.address === wallet),
    [wallet]
  );

  const dataWallet = useMemo(() => {
    let data = walletStakingData;
    data = _.orderBy(
      data,
      [(wallet: any) => Number(wallet?.distributedAmount)],
      ["desc"]
    );
    return data.map((wallet: TWalletStaking) => {
      return {
        ...wallet,
        balance: wallet?.distributedAmount || "0",
      };
    });
  }, [walletStakingData]);

  useEffect(() => {
    if (!dataWallet.length) return;
    setWallet(dataWallet[0]?.address);
  }, [dataWallet]);

  const onClaim = async () => {
    try {
      setIsLoading(true);
      await rf.getRequest("NewOrderRequest").claimMoonBags(NETWORKS.SUI, {
        tokenAddress: pair?.tokenBase?.address,
        wallets: [wallet],
      });
      sleep(3000);
      setIsLoading(false);
      toastSuccess("Success", "Claimed successfully!");
      onClose();
    } catch (e: any) {
      setIsLoading(false);
      toastError("Error", e?.message || "Something went wrong!");
      console.error(e);
    }
  };

  const isInvalidClaim = BigNumber(
    dataWalletSelected?.distributedAmount || 0
  ).isLessThanOrEqualTo(0);

  return (
    <div>
      <div className="mb-[16px]">
        <div className="flex items-center justify-between">
          <div className="action-xs-medium-12 text-white-700 mb-[8px]">
            Wallet
          </div>
        </div>
        <AppSelectWallet
          walletAddressSelected={wallet}
          wallets={dataWallet}
          onSelect={(value: string) => {
            setWallet(value);
          }}
          isOnlySelect
        />
      </div>

      <div className="border-white-100 mt-2 grid grid-cols-2 rounded-[8px] border p-2">
        <div className="border-white-50 flex flex-col items-center border-r">
          <div className="body-sm-regular-12 mb-1">Total claimed</div>
          <div className="body-md-semibold-14 flex items-center gap-1">
            <AppNumber value={dataWalletSelected?.rewardClaimed || "0"} />
            <AppLogoNetwork network={NETWORKS.SUI} isBase className="h-4 w-4" />
          </div>
        </div>
        <div className="flex flex-col items-center">
          <div className="body-sm-regular-12 mb-1">Available to claim</div>
          <div className="body-md-semibold-14 flex items-center gap-1">
            <AppNumber value={dataWalletSelected?.distributedAmount || "0"} />
            <AppLogoNetwork network={NETWORKS.SUI} isBase className="h-4 w-4" />
          </div>
        </div>
      </div>
      <AppButton
        disabled={isLoading || !wallet || isInvalidClaim}
        onClick={onClaim}
        className="mt-6"
        size="large"
      >
        {isLoading ? "Loading..." : "Claim"}
      </AppButton>
    </div>
  );
};

type TWalletStaking = TWallet & {
  stakedAmount: string;
  rewardClaimed: string;
  distributedAmount: string;
  lastStake: number;
};

export const ModalStake = ({
  isOpen,
  onClose,
  pair,
}: {
  isOpen: boolean;
  onClose: () => void;
  pair: TPair;
}) => {
  const [activeTab, setActiveTab] = useState<string>("STAKE");
  const [walletStakingData, setWalletStakingData] = useState<TWalletStaking[]>(
    []
  );

  const wallets = useSelector((state: RootState) => state.user.wallets);

  const _renderTab = () => {
    if (activeTab === "STAKE") {
      return <FromStake onClose={onClose} pair={pair} />;
    }
    if (activeTab === "UNSTAKE") {
      return (
        <FromUnStake
          onClose={onClose}
          walletStakingData={walletStakingData}
          pair={pair}
        />
      );
    }
    return (
      <TabClaim
        onClose={onClose}
        walletStakingData={walletStakingData}
        pair={pair}
      />
    );
  };

  const getUserStakingData = async () => {
    try {
      const data = await Promise.all(
        await wallets.map(async (wallet: TWallet) => {
          try {
            const result = (await fetch(
              `${MOONBAGS_API_URL}/staking-user/${wallet?.address}/coin/${pair?.tokenBase?.address}`
            )) as any;
            const res = await result?.json();
            return {
              ...wallet,
              stakedAmount: res?.stakedAmount || "0",
              rewardClaimed: res?.rewardClaimed || "0",
              distributedAmount: res?.distributedAmount || "0",
              lastStake: res?.lastStake || "0",
            };
          } catch (e) {
            return {
              ...wallet,
              stakedAmount: "0",
              rewardClaimed: "0",
              distributedAmount: "0",
              lastStake: 0,
            };
          }
        })
      );
      setWalletStakingData(data);
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    if (!pair?.tokenBase?.address || !wallets.length) return;
    getUserStakingData().then();
  }, [pair?.tokenBase?.address, wallets]);

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      onClose={onClose}
      closable={false}
    >
      <div className="-mt-4 flex flex-col gap-6">
        <div className="bg-white-100 grid grid-cols-3 rounded-[4px] p-1">
          {TABS.map((item) => {
            const isActive = item?.value === activeTab;
            return (
              <div
                key={item?.value}
                className={clsx(
                  "body-md-medium-14 flex cursor-pointer items-center justify-center rounded-[4px] px-4 py-1.5 text-center",
                  isActive ? "bg-white-100" : "text-white-500"
                )}
                onClick={() => {
                  setActiveTab(item?.value);
                }}
              >
                {item?.label}
              </div>
            );
          })}
        </div>

        {_renderTab()}
      </div>
    </BaseModal>
  );
};

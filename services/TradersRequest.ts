import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class TradersRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.insightApi;
  }
  async getTopTraders(network: string, pairId: string, params: any) {
    const url = `/${network}/api/v1/traders/${pairId}`;
    return this.get(url, params);
  }

  async getInfoTraders(network: string, pairId: string, params: any) {
    const url = `/${network}/api/v1/traders/${pairId}/addresses`;
    return this.get(url, params);
  }
}

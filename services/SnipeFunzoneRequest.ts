import config from "@/config";
import BaseRequest from "./BaseRequest";

interface SnipeFunzoneRequestPropTypes {
  network: string;
  userWalletAddress: string;
  targetDex?: string;
  targetWalletAddress?: string;
  buyByToken: string;
  data?: any;
}

export default class SnipeFunzoneRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.userApi;
  }

  getListSettings(network: string) {
    const url = `/${network}/snipe/funzone-listing/list-settings`;
    return this.get(url);
  }
  getSettings(network: string) {
    const url = `/${network}/snipe/funzone-listing/settings`;
    return this.get(url);
  }
  createUrl = ({
    network,
    userWalletAddress,
    targetDex,
    targetWalletAddress,
    buyByToken,
  }: SnipeFunzoneRequestPropTypes) => {
    const params = new URLSearchParams();
    params.append("userWalletAddress", userWalletAddress);
    params.append("buyByToken", buyByToken);
    if (targetDex) params.append("targetDex", targetDex);
    if (targetWalletAddress)
      params.append("targetWalletAddress", targetWalletAddress);
    return `/${network}/snipe/funzone-listing/settings?${params.toString()}`;
  };

  updateSettings({
    network,
    targetDex,
    targetWalletAddress,
    userWalletAddress,
    data,
    buyByToken,
  }: SnipeFunzoneRequestPropTypes) {
    const url = this.createUrl({
      network,
      userWalletAddress,
      targetDex,
      targetWalletAddress,
      buyByToken,
    });
    return this.put(url, data);
  }

  createSnipeDex(network: string, data: any) {
    const url = `/${network}/snipe/funzone-listing/settings`;
    return this.post(url, data);
  }

  deleteSettings({
    network,
    userWalletAddress,
    targetDex,
    targetWalletAddress,
    buyByToken,
  }: SnipeFunzoneRequestPropTypes) {
    const url = this.createUrl({
      network,
      userWalletAddress,
      targetDex,
      targetWalletAddress,
      buyByToken,
    });

    return this.delete(url);
  }

  getHistoryList(network: string) {
    const url = `/${network}/snipe/funzone-listing/histories`;
    return this.get(url);
  }
}

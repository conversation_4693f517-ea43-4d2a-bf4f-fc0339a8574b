import config from "@/config";
import BaseRequest from "./BaseRequest";

const getWithdrawApiEndpoint = (network: string) => {
  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network]?.endpoints?.withdrawApi
  ) {
    throw new Error("withdrawApi endpoint not found for this network");
  }
  return (config.networks as any)[network].endpoints.withdrawApi;
};

export default class WithdrawRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  async transfer(
    network: string,
    params: {
      amountIn: string;
      fromWallet: string;
      toWallet: string;
      metadata?: any;
      paymentCategory?: string;
      withdrawalType?: string;
    }
  ) {
    const url = `${getWithdrawApiEndpoint(
      network
    )}/${network}/withdraw/transfer`;
    return this.post(url, params);
  }

  async transferToken(
    network: string,
    tokenAddress: string,
    params: {
      amountIn: string;
      fromWallet: string;
      toWallet: string;
      metadata?: any;
      paymentCategory?: string;
      withdrawalType?: string;
    }
  ) {
    const url = `${getWithdrawApiEndpoint(
      network
    )}/${network}/withdraw/transfer/${tokenAddress}`;
    return this.post(url, params);
  }

  async consolidate(
    network: string,
    params: {
      fromWallets: string[];
      consolidatePercent: string | number;
      toWallet: string;
    }
  ) {
    const url = `${getWithdrawApiEndpoint(
      network
    )}/${network}/withdraw/consolidate`;
    return this.post(url, params);
  }

  async distribute(
    network: string,
    params: {
      fromWallet: string;
      amountIn: string;
      toWallets: string[];
    }
  ) {
    const url = `${getWithdrawApiEndpoint(
      network
    )}/${network}/withdraw/distribute`;
    return this.post(url, params);
  }
}

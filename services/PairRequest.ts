import config from "@/config";
import BaseRequest from "./BaseRequest";

const getMarketApiEndpoint = (network: string) => {
  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network]?.endpoints?.marketApi
  ) {
    throw new Error(`marketApi endpoint not found for this ${network}`);
  }
  return (config.networks as any)[network].endpoints.marketApi;
};

export default class PairRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  async getPairRecently(network: string, params: any) {
    const url = `${getMarketApiEndpoint(network)}/${network}/pairs/recently`;
    return this.get(url, params);
  }

  async getPairTrending(network: string, params: any) {
    const url = `${getMarketApiEndpoint(network)}/${network}/pairs/trending`;
    return this.get(url, params);
  }

  async getPair(network: string, pairId: string) {
    const url = `${getMarketApiEndpoint(network)}/${network}/pairs/${pairId}`;
    return this.get(url);
  }

  async getPairsRelated(network: string, pairId: string) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/pairs/${pairId}/related`;
    return this.get(url);
  }

  async getPairStats(network: string, pairId: string) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/pairs/${pairId}/stats`;
    return this.get(url);
  }

  async getPairAudit(network: string, pairId: string) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/pairs/${pairId}/audit`;
    return this.get(url);
  }
}

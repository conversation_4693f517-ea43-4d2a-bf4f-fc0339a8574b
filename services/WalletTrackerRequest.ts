import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class WalletTrackerRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.commonApi;
  }

  async getGroups() {
    const url = `/wallet-tracker/groups`;
    return this.get(url);
  }

  async addGroup(params: any) {
    const url = `/wallet-tracker/groups`;
    return this.post(url, params);
  }

  async deleteGroup(groupId: string) {
    const url = `/wallet-tracker/groups/${groupId}`;
    return this.delete(url);
  }

  async getWallets(groupId: string) {
    const url = `/wallet-tracker/wallets?groupId=${groupId}`;
    return this.get(url);
  }

  async addWallet(params: any) {
    const url = `/wallet-tracker/wallets`;
    return this.post(url, params);
  }

  async editWallet(walletId: string, params: any) {
    const url = `/wallet-tracker/wallets/${walletId}`;
    return this.put(url, params);
  }

  async deleteWallet(walletId: string) {
    const url = `/wallet-tracker/wallets/${walletId}`;
    return this.delete(url);
  }

  async deleteAllWallet(groupId: string) {
    const url = `/wallet-tracker/wallets/group/${groupId}`;
    return this.delete(url);
  }

  async getTransactions(params?: any) {
    const url = `/wallet-tracker/transactions/groups`;
    return this.get(url, params);
  }
}

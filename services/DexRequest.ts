import config from "@/config";
import BaseRequest from "./BaseRequest";

const getDexApiEndpoint = (network: string) => {
  if (!network || typeof network !== "string") {
    throw new Error("Network parameter is required and must be a string");
  }

  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network as any]?.endpoints?.marketApi
  ) {
    throw new Error(`marketApi endpoint not found for network: ${network}`);
  }
  return (config.networks as any)[network as any]?.endpoints?.marketApi;
};

export default class DexRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  // Get all DEXes for a network
  async getDexes(network: string) {
    const url = `${getDexApiEndpoint(network)}/${network}/dexes`;
    return this.get(url);
  }
}

import config from "@/config";
import BaseRequest from "./BaseRequest";
import { getMarketApiEndpoint } from "@/utils/network";

export default class CandleRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  async getCandles(
    network: string,
    params: {
      limit: number;
      to: number;
      pair: string;
      queryBy: string;
      resolution: number | string;
    }
  ) {
    const url = `${getMarketApiEndpoint(network)}/${network}/candles`;
    return this.get(url, params);
  }
}

import config from "@/config";
import BaseRequest from "./BaseRequest";

const getWalletApiEndpoint = (network: string) => {
  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network]?.endpoints?.walletApi
  ) {
    throw new Error("walletApi endpoint not found for this network");
  }
  return (config.networks as any)[network]?.endpoints?.walletApi;
};

export default class WalletRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  async getWallets(network: string) {
    const url = `${getWalletApiEndpoint(network)}/${network}/user-wallets`;
    return this.get(url);
  }

  async getGenerateWallets(network: string, params: { numberWallets: number }) {
    const url = `${getWalletApiEndpoint(
      network
    )}/${network}/user-wallets/generate`;
    return this.post(url, params);
  }

  async editNameWallet(
    network: string,
    walletAddress: string,
    params: { aliasName: string }
  ) {
    const url = `${getWalletApiEndpoint(
      network
    )}/${network}/user-wallets/${walletAddress}`;
    return this.put(url, params);
  }

  async getImportWallet(network: string, params: { privateKey: string }) {
    const url = `${getWalletApiEndpoint(
      network
    )}/${network}/user-wallets/import`;
    return this.post(url, params);
  }

  async inactiveAllWallets(network: string) {
    const url = `${getWalletApiEndpoint(
      network
    )}/${network}/user-wallets/deactivate-all`;
    return this.put(url);
  }

  async inactiveWallet(network: string, walletAddresses: string[]) {
    const url = `${getWalletApiEndpoint(
      network
    )}/${network}/user-wallets/deactivate`;
    return this.put(url, { walletAddresses });
  }
}

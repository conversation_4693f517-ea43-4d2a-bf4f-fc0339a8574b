import config from "@/config";
import BaseRequest from "./BaseRequest";

const getMarketApiEndpoint = (network: string) => {
  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network]?.endpoints?.marketApi
  ) {
    throw new Error(`marketApi endpoint not found for this ${network}`);
  }
  return (config.networks as any)[network].endpoints.marketApi;
};

export default class TokenAdvertisementRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  async getTokenAdvertisement(network: string) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/tokens/advertisements`;
    return this.get(url);
  }

  async checkActiveTokenAdvertisement(network: string, tokenAddress: string) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/tokens/advertisements/check-active?tokenAddress=${tokenAddress}`;
    return this.get(url);
  }
}

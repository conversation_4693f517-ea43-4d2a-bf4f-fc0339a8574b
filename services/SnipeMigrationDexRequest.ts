import config from "@/config";
import BaseRequest from "./BaseRequest";

interface SnipeDexRequestPropTypes {
  network: string;
  userWalletAddress: string;
  buyByToken: string;
  targetTokenAddress?: string;
  data?: any;
}

export default class SnipeMigrationDexRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.userApi;
  }

  getListSettings(network: string) {
    const url = `/${network}/snipe/migration-dex/list-settings`;
    return this.get(url);
  }

  getListSettingsFilter(network: string, data: any) {
    const url = `/${network}/snipe/migration-dex/list-settings-filter`;
    return this.get(url, data);
  }

  getSettings(network: string) {
    const url = `/${network}/snipe/migration-dex/settings`;
    return this.get(url);
  }

  createUrl = ({
    network,
    userWalletAddress,
    targetTokenAddress,
    buyByToken,
  }: SnipeDexRequestPropTypes) => {
    const params = new URLSearchParams();
    params.append("userWalletAddress", userWalletAddress);
    params.append("buyByToken", buyByToken);
    if (targetTokenAddress)
      params.append("targetTokenAddress", targetTokenAddress);
    return `/${network}/snipe/migration-dex/settings?${params.toString()}`;
  };

  updateSettings({
    network,
    targetTokenAddress,
    userWalletAddress,
    data,
    buyByToken,
  }: SnipeDexRequestPropTypes) {
    const url = this.createUrl({
      network,
      userWalletAddress,
      targetTokenAddress,
      buyByToken,
    });
    return this.put(url, data);
  }

  createSnipeDex(network: string, data: any) {
    const url = `/${network}/snipe/migration-dex/settings`;
    return this.post(url, data);
  }

  deleteSettings({
    network,
    userWalletAddress,
    targetTokenAddress,
    buyByToken,
  }: SnipeDexRequestPropTypes) {
    const url = this.createUrl({
      network,
      userWalletAddress,
      targetTokenAddress,
      buyByToken,
    });

    return this.delete(url);
  }

  getHistoryList(network: string) {
    const url = `/${network}/snipe/migration-dex/histories`;
    return this.get(url);
  }

  getHistoryListFilter(network: string, data: any) {
    const url = `/${network}/snipe/migration-dex/histories-filter`;
    return this.get(url, data);
  }
}

import config from "@/config";
import BaseRequest from "./BaseRequest";

const getMarketApiEndpoint = (network: string) => {
  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network]?.endpoints?.marketApi
  ) {
    throw new Error(`marketApi endpoint not found for this ${network}`);
  }
  return (config.networks as any)[network].endpoints.marketApi;
};

export default class TokenRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }
  async getTopPair(network: string, tokenAddress: string) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/tokens/${tokenAddress}/top-pair`;
    return this.get(url);
  }
  async getBalanceOf(network: string, tokenAddress: string) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/tokens/get-balance-of`;
    return this.get(url, { tokenAddress });
  }
  async getTokenInfo(network: string, tokenAddress: string) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/tokens/${tokenAddress}/infos`;
    return this.get(url);
  }

  async checkHoneypot(network: string, tokenAddress: string) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/tokens/${tokenAddress}/honey-pot`;
    return this.get(url);
  }
}

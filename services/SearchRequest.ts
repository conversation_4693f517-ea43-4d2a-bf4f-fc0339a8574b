import config from "@/config";
import BaseRequest from "./BaseRequest";

const getSearchApiEndpoint = (network: string) => {
  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network]?.endpoints?.marketApi
  ) {
    throw new Error(`marketApi endpoint not found for this ${network}`);
  }
  return (config.networks as any)[network].endpoints.marketApi;
};

export default class SearchRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  async search(
    network: string,
    params: { search: string; page?: number; limit?: number }
  ) {
    const url = `${getSearchApiEndpoint(network)}/${network}/search`;
    return this.get(url, params);
  }
}

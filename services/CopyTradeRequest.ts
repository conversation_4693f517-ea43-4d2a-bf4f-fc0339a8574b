import config from "@/config";
import BaseRequest from "./BaseRequest";
import { IRequestCreateTarget } from "@/types/copytrade.type";

export default class CopyTradeRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.userApi;
  }

  getListTargets(network: string) {
    const url = `/${network}/copy-trade/list-targets`;
    return this.get(url);
  }

  updateTarget({
    network,
    walletAddress,
    targetWalletAddress,
    data,
  }: {
    network: string;
    walletAddress: string;
    targetWalletAddress: string;
    data: any;
  }) {
    const params = new URLSearchParams();
    params.append("walletAddress", walletAddress);
    params.append("targetWalletAddress", targetWalletAddress);
    const url = `/${network}/copy-trade/targets?${params.toString()}`;
    return this.put(url, data);
  }

  createTarget(network: string, data: IRequestCreateTarget) {
    const url = `/${network}/copy-trade/targets`;
    return this.post(url, data);
  }

  deleteTarget({
    network,
    walletAddress,
    targetWalletAddress,
  }: {
    network: string;
    walletAddress: string;
    targetWalletAddress: string;
  }) {
    const params = new URLSearchParams();
    params.append("walletAddress", walletAddress);
    params.append("targetWalletAddress", targetWalletAddress);
    const url = `/${network}/copy-trade/targets?${params.toString()}`;
    return this.delete(url);
  }

  getSettings(network: string) {
    const url = `/${network}/copy-trade/settings`;
    return this.get(url);
  }
  deleteSettings(network: string) {
    const url = `/${network}/copy-trade/settings`;
    return this.delete(url);
  }

  updateSettings(network: string, data: any) {
    const url = `/${network}/copy-trade/settings`;
    return this.put(url, data);
  }

  createSettings(network: string, data: any) {
    const url = `/${network}/copy-trade/settings`;
    return this.post(url, data);
  }
}

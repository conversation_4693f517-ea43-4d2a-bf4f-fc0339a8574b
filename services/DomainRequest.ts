import axios from "axios";
import config from "@/config";

export default class DomainRequest {
  async getDomainNamesByAddresses(addresses: string[]) {
    if (
      !config ||
      !config.networks ||
      !config.networks.sui ||
      !config.networks.sui.endpoints ||
      !config.networks.sui.endpoints.domainNameApi
    ) {
      return [];
    }

    const url = `${config.networks.sui.endpoints.domainNameApi}/sui/domain/names`;
    try {
      const response: any = await axios.post(url, {
        addresses,
      });
      if (response && response.data && Array.isArray(response.data.names)) {
        return response.data.names;
      }
      return [];
    } catch (error) {
      return [];
    }
  }
}

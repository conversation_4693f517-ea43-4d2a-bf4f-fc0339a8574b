import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class MemeRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.commonApi;
  }

  async getMemeNewCreated(network: string, params: any) {
    const url = `/${network}/meme-zones/new-created`;
    return this.get(url, params);
  }

  async getMemeGraduating(network: string, params: any) {
    const url = `/${network}/meme-zones/graduating`;
    return this.get(url, params);
  }

  async getMemeGraduated(network: string, params: any) {
    const url = `/${network}/meme-zones/graduated`;
    return this.get(url, params);
  }
}

import config from "@/config";
import BaseRequest from "./BaseRequest";

const getMarketApiEndpoint = (network: string) => {
  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network]?.endpoints?.marketApi
  ) {
    throw new Error(`marketApi endpoint not found for this ${network}`);
  }
  return (config.networks as any)[network].endpoints.marketApi;
};

export default class TrendingRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  async getTrendingMeme(network: string) {
    const url = `${getMarketApiEndpoint(network)}/${network}/trending/memes`;
    return this.get(url);
  }
}

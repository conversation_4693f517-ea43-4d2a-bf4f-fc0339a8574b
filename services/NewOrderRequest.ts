import config from "@/config";
import BaseRequest from "./BaseRequest";

const getOrderApiEndpoint = (network: string) => {
  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network as any]?.endpoints?.orderApi
  ) {
    throw new Error("orderApi endpoint not found for this network");
  }
  return (config.networks as any)[network as any]?.endpoints?.orderApi;
};

export default class NewOrderRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  async createOrderQuickBuy(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/quick-buy`;
    return this.post(url, params);
  }

  async createOrderQuickSell(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/quick-sell`;
    return this.post(url, params);
  }

  async createOrderSellLimit(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/sell-limit`;
    return this.post(url, params);
  }

  async createOrderBuyLimit(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/buy-limit`;
    return this.post(url, params);
  }

  async createOrderSellDCA(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/sell-dca`;
    return this.post(url, params);
  }

  async createOrderBuyDCA(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/buy-dca`;
    return this.post(url, params);
  }

  async closePosition(network: string, params: any) {
    const url = `${getOrderApiEndpoint(
      network
    )}/${network}/orders/close-position`;
    return this.post(url, params);
  }

  async claimMyLockedAmount(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/unlock-coin`;
    return this.post(url, params);
  }

  async getOrders(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders`;
    return this.get(url, params);
  }

  async getOrderHistories(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/order-histories`;
    return this.get(url, params);
  }

  async getOpenOrders(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/open-limit-orders`;
    return this.get(url, params);
  }

  async cancelOrder(network: string, orderId: string) {
    const url = `${getOrderApiEndpoint(
      network
    )}/${network}/orders/${orderId}/revoke`;
    return this.put(url);
  }

  async cancelAllOrder(network: string) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/revoke-all`;
    return this.put(url);
  }

  async claimMoonBags(network: string, params: any) {
    const url = `${getOrderApiEndpoint(
      network
    )}/${network}/orders/moonbags/claim`;
    return this.post(url, params);
  }

  async stakingMoonBags(network: string, params: any) {
    const url = `${getOrderApiEndpoint(
      network
    )}/${network}/orders/moonbags/staking`;
    return this.post(url, params);
  }

  async unstakingMoonBags(network: string, params: any) {
    const url = `${getOrderApiEndpoint(
      network
    )}/${network}/orders/moonbags/unstaking`;
    return this.post(url, params);
  }

  async createOrderQuickOrder(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/quick`;
    return this.post(url, params);
  }

  async createOrderLimitOrder(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/limit`;
    return this.post(url, params);
  }

  async createOrderDCAOrder(network: string, params: any) {
    const url = `${getOrderApiEndpoint(network)}/${network}/orders/dca`;
    return this.post(url, params);
  }
}

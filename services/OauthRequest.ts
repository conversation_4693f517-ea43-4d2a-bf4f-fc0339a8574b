import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class OauthRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.authorizeApi;
  }
  getClients(data: any) {
    const url = "/clients";
    return this.get(url, data);
  }
  createUserClient(data: any) {
    const url = "/clients";
    return this.post(url, data);
  }
  getClientById(clientId: string) {
    const url = `/clients/${clientId}`;
    return this.get(url);
  }
  updateClient(clientId: string, data: any) {
    const url = `/clients/${clientId}`;
    return this.put(url, data);
  }
  generateKey(clientId: string) {
    const url = `/clients/${clientId}/regenerate-secret`;
    return this.post(url);
  }
  getPermissions() {
    const url = "/clients/permissions";
    return this.get(url);
  }
}

import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class UserRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.userApi;
  }

  async getPairs(network: string, params?: any) {
    const url = `/${network}/favourites`;
    return this.get(url, params);
  }

  async isFavouritePair(network: string, pairId: string) {
    const url = `/${network}/favourites/${pairId}/check`;
    return this.get(url);
  }

  async favourite(network: string, poolId: string) {
    const url = `/${network}/favourites`;
    return this.post(url, { poolId });
  }

  async unFavourite(network: string, poolId: string) {
    const url = `/${network}/favourites/${poolId}`;
    return this.delete(url);
  }
}

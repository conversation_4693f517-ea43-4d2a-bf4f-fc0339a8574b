import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class UserRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.referralApi;
  }

  async getReferralCode() {
    const url = `/v1/users/code`;
    return this.get("getReferralCode", url);
  }

  async registerReferralCode(params: { code: string }) {
    const url = `/v1/users/code`;
    return this.post(url, params);
  }

  async getReferralInfo() {
    const url = `/v1/users/referrals`;
    return this.get(url);
  }

  async getCommissions(network: string) {
    const url = `/referrals/commissions/${network}`;
    return this.get(url);
  }

  async getClaimRequests(
    network: string,
    params?: { page?: number; limit?: number }
  ) {
    const url = `/referrals/claim-requests/${network}`;
    return this.get(url, params);
  }

  async createClaimRequest(network: string, params: { receiver: string }) {
    const url = `/referrals/claim-requests/${network}`;
    return this.post(url, params);
  }

  async getReferralLayers() {
    const url = `/referrals/config`;
    return this.get(url);
  }
}

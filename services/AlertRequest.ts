import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class AlertRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.alertApi;
  }

  async getAllAlerts(params: any) {
    const url = `/pair-alert-settings`;
    return this.get(url, params);
  }

  async createAlert(params: any) {
    const url = `/pair-alert-settings`;
    return this.post(url, params);
  }

  async editAlert(hash: string, params: any) {
    const url = `/pair-alert-settings/${hash}`;
    return this.put(url, params);
  }

  async deleteAlert(hash: string) {
    const url = `/pair-alert-settings/${hash}`;
    return this.delete(url);
  }

  async deleteAllAlert() {
    const url = `/pair-alert-settings/delete-all`;
    return this.delete(url);
  }

  async deleteAllAlertActive() {
    const url = `/pair-alert-settings/delete-all-active`;
    return this.delete(url);
  }

  async deleteAllAlertExpired() {
    const url = `/pair-alert-settings/delete-all-expired`;
    return this.delete(url);
  }
}

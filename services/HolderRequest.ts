import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class HolderRequest extends BaseRequest {
  getUrlPrefix() {
    return config.endpoints.insightApi;
  }

  async getHolders(network: string, params: any) {
    const url = `/${network}/api/v1/holders`;
    return this.get(url, params);
  }

  async getMyBalances(network: string, params: any) {
    const url = `/${network}/api/v1/my/balances`;
    return this.get(url, params);
  }

  async getTokenHolders(network: string, params: any) {
    const url = `/${network}/api/v1/tokens/${params?.tokenAddress}/holders`;
    return this.get(url, params);
  }

  getBalanceCharts = async (
    network: string,
    resolution: string
  ): Promise<any> => {
    const url = `/${network}/api/v1/my/balance-histories?resolution=${resolution}`;
    return this.get(url);
  };

  getMyBalance = async (network: string): Promise<any> => {
    const url = `/${network}/api/v1/my/sui-balance`;
    return this.get(url);
  };
}

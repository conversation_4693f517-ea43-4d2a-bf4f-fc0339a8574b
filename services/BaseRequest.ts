import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import config from "@/config";
// import { load } from 'recaptcha-v3';
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { setAuthorizationToRequest } from "@/utils/authenticate";
// import retry from 'async-retry';

export default class BaseRequest {
  protected accessToken = "";
  constructor(accessToken?: string | undefined) {
    if (accessToken) {
      this.accessToken = accessToken;
      setAuthorizationToRequest(accessToken);
    }
  }

  getUrlPrefix() {
    return config.endpoints.commonApi;
  }

  async buildCustomHeaders() {
    return {
      "x-request-id": uuidv4(),
    };
    // return retry(
    //   async () => {
    //     try {
    //       const recaptcha = await load(config.reCaptchaSiteKey);
    //       const token = await recaptcha.execute('SUBMIT');
    //       return {
    //         recaptcha: token,
    //       };
    //     } catch (error) {
    //       throw new Error('The RaidenX is busy, please try again later!');
    //     }
    //   },
    //   {
    //     retries: 1,
    //     minTimeout: 1000,
    //     maxTimeout: 2000,
    //   },
    // );
  }

  async get(url: string, params?: any) {
    try {
      const config = {
        params,
        headers: await this.buildCustomHeaders(),
      };
      const response = await axios.get(this.getUrlPrefix() + url, config);
      return this._responseHandler(response);
    } catch (error) {
      return this._errorHandler(error);
    }
  }

  async getWithoutEncode(url: string, params?: any) {
    try {
      const config = {
        params,
        headers: await this.buildCustomHeaders(),
        paramsSerializer: (params: any) => {
          return Object.entries(params)
            .map(([key, value]) => `${key}=${value}`)
            .join("&");
        },
      };
      const response = await axios.get(this.getUrlPrefix() + url, config);
      return this._responseHandler(response);
    } catch (error) {
      return this._errorHandler(error);
    }
  }

  async put(url: any, data?: any) {
    try {
      const config = {
        headers: await this.buildCustomHeaders(),
      };
      const response = await axios.put(this.getUrlPrefix() + url, data, config);
      return this._responseHandler(response);
    } catch (error) {
      return this._errorHandler(error);
    }
  }

  async patch(url: any, data?: any) {
    try {
      const config = {
        headers: await this.buildCustomHeaders(),
      };
      const response = await axios.patch(
        this.getUrlPrefix() + url,
        data,
        config
      );
      return this._responseHandler(response);
    } catch (error) {
      return this._errorHandler(error);
    }
  }

  async post(url: any, data = {}) {
    try {
      const config = {
        headers: await this.buildCustomHeaders(),
      };
      const response = await axios.post(
        this.getUrlPrefix() + url,
        data,
        config
      );
      return this._responseHandler(response);
    } catch (error) {
      return this._errorHandler(error);
    }
  }

  async delete(url: any, data?: any) {
    try {
      const config = {
        data,
        headers: await this.buildCustomHeaders(),
      };
      const response = await axios.delete(this.getUrlPrefix() + url, config);
      return this._responseHandler(response);
    } catch (error) {
      return this._errorHandler(error);
    }
  }

  async download(url: any, data?: any) {
    try {
      const config = {
        ...data,
        headers: await this.buildCustomHeaders(),
        responseType: "blob",
      };
      const response = await axios.get(this.getUrlPrefix() + url, config);
      return this._responseHandler(response);
    } catch (error) {
      return this._errorHandler(error);
    }
  }

  async _responseHandler(response: any) {
    return response.data;
  }

  _error401Handler() {
    AppBroadcast.dispatch(BROADCAST_EVENTS.LOGOUT, {});
    throw Error("Unauthorized");
  }

  _error403Handler() {
    // TODO: make broadcast event
    AppBroadcast.dispatch(BROADCAST_EVENTS.LOGOUT, {});
  }

  async _errorHandler(err: any) {
    if (err.response?.status === 401) {
      return this._error401Handler();
    }

    if (err.response?.status === 403) {
      return this._error403Handler();
    }

    if (err.response && err.response.data && err.response.data.data) {
      if (typeof err.response.data.data.message === "string") {
        throw new Error(err.response.data.data.message);
      }
    }

    if (err.response && err.response.data && err.response.data.message) {
      if (typeof err.response.data.message === "string") {
        throw new Error(err.response.data.message);
      }
      throw new Error(err.response.data.message[0]);
    }

    if (err.response && err.response.data && err.response.data.error) {
      throw new Error(err.response.data.error);
    }

    if (
      err.response &&
      err.response.data &&
      typeof err.response.data === "string"
    ) {
      throw new Error(err.response.data);
    }

    throw err;
  }
}

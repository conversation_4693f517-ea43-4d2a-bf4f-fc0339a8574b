import config from "@/config";
import BaseRequest from "./BaseRequest";

const getMarketApiEndpoint = (network: string) => {
  if (
    !config.networks ||
    !(config.networks as any)[network] ||
    !(config.networks as any)[network]?.endpoints?.marketApi
  ) {
    throw new Error(`marketApi endpoint not found for this ${network}`);
  }
  return (config.networks as any)[network].endpoints.marketApi;
};

export default class TransactionRequest extends BaseRequest {
  getUrlPrefix() {
    return "";
  }

  async getTransactions(network: string, params: any) {
    const url = `${getMarketApiEndpoint(network)}/${network}/transactions`;
    return this.get(url, params);
  }

  async getTransactionsByWalletType(network: string, params: any) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/transactions/filter?`;
    return this.getWithoutEncode(url, params);
  }

  async getDevTrades(network: string, pairId: string, params: any) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/pairs/${pairId}/transactions/dev`;
    return this.get(url, params);
  }

  async getMyTransactions(network: string, params: any) {
    const url = `${getMarketApiEndpoint(
      network
    )}/${network}/transactions/user-txs`;
    return this.get(url, params);
  }
}

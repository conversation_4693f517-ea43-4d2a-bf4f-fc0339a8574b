import { TP<PERSON>, TWallet } from "@/types";
import { default as React, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useRaidenxWallet } from "@/hooks";
import Storage from "@/libs/storage";
import _ from "lodash";
import { Close, SearchIcon, SettingsIcon } from "@/assets/icons";
import {
  AppAvatarToken,
  AppButtonSort,
  AppCopy,
  AppNumber,
} from "@/components";
import { toastError } from "@/libs/toast";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { TTradingWallet } from "@/types/balance.type";
import { multipliedBN } from "@/utils/helper";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { useRouter } from "next/navigation";
import { ROUTE_PATH } from "@/routes";
import { usePairPrice } from "@/hooks/usePairPrice";
import { ICheckboxIcon, ICheckedIcon } from "@/public/images";

export const WalletSelection = ({
  onCloseWalletSettings,
  type,
  tradingWallets,
  tokenQuoteSelected,
  pair,
}: {
  onCloseWalletSettings: () => void;
  tradingWallets: TTradingWallet[];
  type: TRADE_TYPE;
  tokenQuoteSelected: any;
  pair: TPair;
}) => {
  const { pairPrice } = usePairPrice(pair);
  const [search, setSearch] = useState<string>("");
  const [walletsUser, setWalletsUser] = useState<TTradingWallet[]>([]);
  const [walletsSelected, setWalletsSelected] = useState<string[]>([]);

  const [sortBy, setSortBy] = useState<string>("");
  const [sortType, setSortType] = useState<string>("desc");

  const { wallets, network } = useSelector((state: RootState) => state.user);
  const { walletAddresses, activeWalletAddresses } = useRaidenxWallet();

  const router = useRouter();

  useEffect(() => {
    setWalletsSelected(activeWalletAddresses);
  }, []);

  useEffect(() => {
    if (type === TRADE_TYPE.SELL) {
      setSortBy("balanceToken");
      return;
    }

    setSortBy("balance");
  }, [type]);

  useEffect(() => {
    let dataWallet = tradingWallets;
    if (search) {
      dataWallet = dataWallet.filter((item) =>
        item.address?.toLowerCase().includes(search?.toLowerCase())
      );
    }

    if (sortType === "desc") {
      dataWallet = _.orderBy(
        dataWallet,
        [(wallet: any) => Number(wallet[sortBy])],
        ["desc"]
      );
    }

    if (sortType === "asc") {
      dataWallet = _.orderBy(
        dataWallet,
        [(wallet: any) => Number(wallet[sortBy])],
        ["asc"]
      );
    }

    setWalletsUser(dataWallet);
  }, [search, tradingWallets, sortType, sortBy]);

  const totalQuoteBalance = useMemo(
    () =>
      tradingWallets.reduce(
        (sum, obj) => sum + Number(obj?.quoteBalance || 0),
        0
      ),
    [tradingWallets]
  );

  const totalBaseBalance = useMemo(
    () =>
      tradingWallets.reduce(
        (sum, obj) => sum + Number(obj?.baseBalance || 0),
        0
      ),
    [tradingWallets]
  );

  const onSelectWallet = (wallet: TWallet) => {
    let data = walletsSelected;
    if (walletsSelected.includes(wallet.address)) {
      data = data.filter((address) => address !== wallet.address);
    } else {
      data = data.concat([wallet.address]);
    }

    setWalletsSelected(data);
  };

  const onSelectAllWallet = () => {
    if (walletAddresses.every((item) => walletsSelected.includes(item))) {
      setWalletsSelected([]);
      return;
    }

    setWalletsSelected(walletAddresses);
  };

  const onSelect = () => {
    if (!walletsSelected.length) {
      toastError("Error", "Please select wallet!");
      return;
    }
    AppBroadcast.dispatch(BROADCAST_EVENTS.WALLETS_SELECTED, {
      walletsSelected: walletsSelected,
    });

    Storage.setWalletAddresses(walletsSelected || "");
    onCloseWalletSettings();
  };

  return (
    <div className="w-full">
      <div className="flex justify-between">
        <div className="body-md-medium-14 flex gap-2">
          <div>Wallet Selection</div>
        </div>

        <div className="body-md-medium-14 flex items-center gap-2">
          <div className="border-white-100 flex items-center gap-1 border-b pb-[2px]">
            <SearchIcon />
            <input
              className="body-sm-regular-12 w-[70px] truncate bg-transparent outline-none"
              placeholder="Search"
              value={search}
              onChange={(e) => {
                setSearch(e.target.value);
              }}
            />
          </div>
          <div
            className="text-neutral-alpha-500 hover:text-neutral-alpha-1000 cursor-pointer"
            onClick={onCloseWalletSettings}
          >
            <Close />
          </div>
        </div>
      </div>

      <div className="mt-[12px]">
        <div className="body-xs-medium-10 text-white-500 mx-[8px] grid grid-cols-3 gap-2">
          <div className="flex items-center gap-1">
            <div onClick={onSelectAllWallet} className="cursor-pointer">
              {walletAddresses.every((item) =>
                walletsSelected.includes(item)
              ) ? (
                <img src={ICheckedIcon.src} alt="Checked" />
              ) : (
                <img src={ICheckboxIcon.src} alt="Checkbox" />
              )}
            </div>
            <div>Wallets ({wallets?.length})</div>
          </div>
          <div className="flex items-center justify-center gap-2">
            {tokenQuoteSelected?.symbol}
            <AppButtonSort
              value="quoteBalance"
              setSortBy={setSortBy}
              sortBy={sortBy}
              setSortType={setSortType}
              sortType={sortType}
            />
          </div>
          <div className="flex items-center justify-center gap-2">
            {pair?.tokenBase?.symbol}{" "}
            <AppButtonSort
              value="baseBalance"
              setSortBy={setSortBy}
              sortBy={sortBy}
              setSortType={setSortType}
              sortType={sortType}
            />
          </div>
        </div>

        <div className="customer-scroll max-h-[200px] overflow-auto">
          {walletsUser.map((item, index) => {
            return (
              <div
                key={index}
                className="text-white-1000 body-sm-regular-12 border-white-50 grid grid-cols-3 gap-2 border-b border-dashed px-[8px] py-[12px]"
              >
                <div className="flex items-center gap-1">
                  <div
                    onClick={() => onSelectWallet(item)}
                    className="cursor-pointer"
                  >
                    {walletsSelected.includes(item.address) ? (
                      <img src={ICheckedIcon.src} alt="Checked" />
                    ) : (
                      <img src={ICheckboxIcon.src} alt="Checkbox" />
                    )}
                  </div>

                  <div>
                    {item.address.slice(
                      item.address.length - 6,
                      item.address.length
                    )}
                  </div>
                  <AppCopy
                    message={item.address}
                    className="text-white-600 hover:text-white-1000 h-[12px] w-[12px]"
                  />
                </div>
                <div className="text-center">
                  <AppNumber value={item?.quoteBalance} />
                </div>
                <div className="text-center">
                  <AppNumber value={item?.baseBalance || 0} />
                </div>
              </div>
            );
          })}
        </div>

        <div className="border-white-50 mt-[12px] border-t pt-[16px]">
          <div className="mb-[8px] flex justify-between">
            <div className="body-xs-regular-10">
              Total {tokenQuoteSelected?.symbol}
            </div>
            <div className="body-sm-medium-12 tex-white-1000 flex items-center gap-1">
              <AppNumber value={totalQuoteBalance} />
              {tokenQuoteSelected?.icon}
            </div>
          </div>
          <div className="flex justify-between">
            <div className="body-xs-regular-10">
              Total {pair?.tokenBase?.symbol}
            </div>
            <div className="body-sm-medium-12 tex-white-1000 flex items-center gap-1">
              {!!+totalBaseBalance && (
                <div className="body-xs-regular-10 text-white-500 flex">
                  (
                  <AppNumber
                    value={multipliedBN(totalBaseBalance, pairPrice?.priceUsd)}
                    isForUSD
                    className="body-xs-regular-10"
                  />
                  )
                </div>
              )}

              <AppNumber value={totalBaseBalance} />
              <AppAvatarToken
                className="h-[14px] w-[14px]"
                size={14}
                image={
                  pair?.tokenBase?.logoImageUrl ||
                  pair?.tokenBase?.iconUrl ||
                  ""
                }
              />
            </div>
          </div>
        </div>

        <div
          onClick={onSelect}
          className="text-neutral-beta-900 action-sm-medium-14 bg-neutral-alpha-1000 mt-2 flex cursor-pointer items-center justify-center gap-1 rounded-[6px] px-2 py-[10px]"
        >
          Select Wallet ({walletsSelected.length})
        </div>

        <div
          onClick={() => router.push(ROUTE_PATH.WALLET_MANAGER)}
          className="body-sm-regular-12 mt-2 flex cursor-pointer justify-center gap-1"
        >
          <SettingsIcon />
          Manage Wallets
        </div>
      </div>
    </div>
  );
};

import {
  ChevronDownIcon,
  CoinTip,
  GasIcon,
  SettingsIcon,
  SlippageIcon,
} from "@/assets/icons";
import * as React from "react";
import BigNumber from "bignumber.js";
import { AppToggle } from "@/components";
import { useMediaQuery } from "react-responsive";
import { OrderFormType } from "@/enums/order.enum";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";

export const SwapSettingsBar = ({
  autoSell,
  toggleSetAutoSell,
  amount,
  onShowSettings,
  onShowSettingAutoSell,
  activeTotalQuoteBalance,
  orderType,
}: {
  orderType: string;
  autoSell?: boolean;
  toggleSetAutoSell?: () => void;
  onShowSettingAutoSell?: () => void;
  amount: any;
  onShowSettings: () => void;
  activeTotalQuoteBalance: any;
}) => {
  const settingsQuickOrder = useSelector(
    (state: RootState) => state.user.settingsQuickOrder
  );

  const settingsLimitOrder = useSelector(
    (state: RootState) => state.user.settingsLimitOrder
  );

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const isExceedBalance = () => {
    return new BigNumber(amount).isGreaterThan(activeTotalQuoteBalance);
  };

  const getTipAmount = () => {
    if (orderType === OrderFormType.MARKET) {
      return settingsQuickOrder.tipAmount || "--";
    }

    return settingsLimitOrder.tipAmount || "--";
  };

  const getSlippage = () => {
    if (orderType === OrderFormType.MARKET) {
      return settingsQuickOrder.slippage || "--";
    }

    return settingsLimitOrder.slippage || "--";
  };

  const getGasPrice = () => {
    if (orderType === OrderFormType.MARKET) {
      return settingsQuickOrder.gasPrice || "--";
    }

    return settingsLimitOrder.gasPrice || "--";
  };

  if (isMobile) {
    return (
      <>
        <div className="mt-6">
          {isExceedBalance() && (
            <div className="body-xs-regular-10 mt-2 text-center text-yellow-500">
              Exceeds wallet balance
            </div>
          )}

          <div
            className={`mb-3 flex items-center gap-2 ${
              orderType !== OrderFormType.DCA
                ? "justify-between"
                : "justify-end"
            }`}
          >
            {orderType !== OrderFormType.DCA && (
              <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center gap-1">
                <AppToggle
                  value={autoSell ?? false}
                  onChange={toggleSetAutoSell}
                />
                <div
                  className="flex cursor-pointer items-center gap-1"
                  onClick={onShowSettingAutoSell}
                >
                  Auto Sell
                  <ChevronDownIcon className="w-4 rotate-[-90deg]" />
                </div>
              </div>
            )}

            <div className="item flex gap-2">
              <div className="text-neutral-alpha-500 body-sm-medium-12 flex w-max items-center gap-[4px]">
                <GasIcon />
                <div>{getGasPrice()}</div>
              </div>
              <div className="text-neutral-alpha-500 body-sm-medium-12 border-white-50 flex w-max items-center gap-[4px] border-l pl-2">
                <CoinTip />
                <div>{getTipAmount()}</div>
              </div>

              <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center justify-center gap-1">
                <SlippageIcon />
                <div>{getSlippage()}%</div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="mb-3 mt-9">
        <div
          className={`flex items-center gap-2 ${
            orderType !== OrderFormType.DCA ? "justify-between" : "justify-end"
          }`}
        >
          {orderType !== OrderFormType.DCA && (
            <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center gap-1">
              <AppToggle
                value={autoSell ?? false}
                onChange={toggleSetAutoSell}
              />
              <div
                className="flex cursor-pointer items-center gap-1"
                onClick={onShowSettingAutoSell}
              >
                Auto Sell
                <ChevronDownIcon className="rotate-[-90deg] cursor-pointer" />
              </div>
            </div>
          )}

          <div className="flex items-center gap-2">
            <div className="text-neutral-alpha-500 body-sm-medium-12 flex w-max items-center gap-[4px]">
              <GasIcon />
              <div>{getGasPrice()}</div>
            </div>
            <div className="text-neutral-alpha-500 body-sm-medium-12 border-white-50 flex w-max items-center gap-[4px] border-l pl-2">
              <CoinTip />
              <div>{getTipAmount()}</div>
            </div>

            <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center justify-center gap-1">
              <SlippageIcon />
              <div>{getSlippage()}%</div>
            </div>

            <div onClick={onShowSettings}>
              <SettingsIcon className="text-white-500 h-5 w-5 cursor-pointer" />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

import { SettingsIcon, Close } from "@/assets/icons";
import * as React from "react";
import { useEffect, useState } from "react";
import { TPair } from "@/types";
import { SettingMarketOrder } from "./settings/SettingMarketOrder";
import { SettingLimitOrder } from "./settings/SettingLimitOrder";

const TABS_SETTING = [
  { name: "Market ", value: "MARKET" },
  { name: "Limit", value: "LIMIT" },
  { name: "DCA", value: "DCA" },
];

export const SettingsForm = ({
  onCloseSettings,
  orderType,
  pair,
}: {
  onCloseSettings: () => void;
  orderType: string;
  pair: TPair;
}) => {
  const [tabActive, setTabActive] = useState<any>("MARKET");

  useEffect(() => {
    setTabActive(orderType);
  }, [orderType]);

  return (
    <div>
      <div className="mb-3 flex items-center justify-between">
        <div className="body-md-regular-14 flex gap-1">
          <SettingsIcon className="h-[18px] w-[18px]" />
          Buy/Sell Settings
        </div>
        <div
          className="text-neutral-alpha-500 hover:text-neutral-alpha-1000 cursor-pointer"
          onClick={onCloseSettings}
        >
          <Close />
        </div>
      </div>

      <div className="border-neutral-alpha-50 mb-3 flex flex-1 gap-3 border-b pb-1">
        {TABS_SETTING.map((item, index) => {
          return (
            <div
              key={index}
              onClick={() => {
                setTabActive(item.value);
              }}
              className={`mb-[-4px] cursor-pointer 
                   ${
                     tabActive === item.value
                       ? "text-white-1000 border-neutral-alpha-500 body-md-semibold-14 border-b"
                       : "text-white-500 body-md-medium-14"
                   }`}
            >
              {item.name}
            </div>
          );
        })}
      </div>
      <>
        {tabActive === "MARKET" ? (
          <SettingMarketOrder pair={pair} onCloseSettings={onCloseSettings} />
        ) : (
          <SettingLimitOrder pair={pair} onCloseSettings={onCloseSettings} />
        )}
      </>
    </div>
  );
};

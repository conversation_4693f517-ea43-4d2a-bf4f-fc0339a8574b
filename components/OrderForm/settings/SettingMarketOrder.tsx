import { SlippageIcon, CoinTip, Setting, GasIcon } from "@/assets/icons";
import * as React from "react";
import { useEffect, useState } from "react";
import { NumericFormat } from "react-number-format";
import { AppLogoNetwork, AppToggle } from "@/components";
import { TPair } from "@/types";
import Storage from "@/libs/storage";
import { toastError, toastSuccess } from "@/libs/toast";
import BigNumber from "bignumber.js";
import { formatNumber } from "@/utils/format";
import { calculateMaxGasFee } from "@/utils/helper";
import { NETWORKS } from "@/utils/contants";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { AppDispatch } from "@/store/index";
import { getSettingsQuickOrder } from "@/store/user.store";
import { useSettingsOrder } from "@/hooks";

export const SettingMarketOrder = ({
  pair,
  onCloseSettings,
}: {
  pair: TPair;
  onCloseSettings: () => void;
}) => {
  const [tipAmount, setTipAmount] = useState<any>("0.02");
  const [slippage, setSlippage] = useState<any>("");
  const [gasPrice, setGasPrice] = useState<any>("750");
  const [isEnableTip, setIsEnableTip] = useState<boolean>(true);

  const maxGasFeeBuySell = calculateMaxGasFee(gasPrice);

  const [defaultBuyAmount, setDefaultBuyAmount] = useState<any>([]);
  const [defaultSellPercent, setDefaultSellPercent] = useState<any>([]);

  const orderSettings = Storage.getOrderSettings();
  const settingsQuickOrder = useSelector(
    (state: RootState) => state.user.settingsQuickOrder
  );
  const network = useSelector((state: RootState) => state.user.network);
  const dispatch = useDispatch<AppDispatch>();
  const { updateSettingsQuickOrder } = useSettingsOrder();

  const onSave = async () => {
    if (new BigNumber(gasPrice).lt(750)) {
      toastError("Error", "Minimum gas price is 750 MIST");
      return;
    }

    if (isEnableTip && new BigNumber(tipAmount).lt(0.02)) {
      toastError("Error", "Minimum tip amount is 0.02");
      return;
    }

    try {
      Storage.setOrderSettings({
        ...orderSettings,
        defaultBuyAmount,
        defaultSellPercent,
      });

      await updateSettingsQuickOrder({
        ...settingsQuickOrder,
        tipAmount: +tipAmount,
        slippage: +slippage,
        gasPrice: +gasPrice,
      });

      dispatch(getSettingsQuickOrder({ network }));
      onCloseSettings();

      toastSuccess("Success", "Update successfully!");
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  useEffect(() => {
    setTipAmount(settingsQuickOrder?.tipAmount);
    setSlippage(settingsQuickOrder?.slippage);
    setGasPrice(settingsQuickOrder?.gasPrice);
  }, [settingsQuickOrder]);

  useEffect(() => {
    setDefaultBuyAmount(orderSettings.defaultBuyAmount);
    setDefaultSellPercent(orderSettings.defaultSellPercent);
  }, []);

  useEffect(() => {
    if (new BigNumber(settingsQuickOrder?.tipAmount).gt(0)) {
      setIsEnableTip(true);
      return;
    }

    setIsEnableTip(false);
  }, [settingsQuickOrder]);

  return (
    <div>
      <div className="mb-6 flex flex-col gap-4">
        <div>
          <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
            <SlippageIcon />{" "}
            <span className="text-white-700">Slippage Limit</span>
          </div>

          <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
            <div className="body-sm-regular-12 text-neutral-alpha-500">%</div>
            <NumericFormat
              value={slippage ?? ""}
              allowLeadingZeros
              allowNegative={false}
              thousandSeparator=","
              className="body-md-semibold-14 w-full bg-transparent outline-none"
              decimalScale={2}
              onValueChange={({ floatValue }) => {
                return setSlippage(floatValue);
              }}
            />
          </div>
        </div>

        <div>
          <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
            <GasIcon />
            <span className="text-white-700">Gas Price</span>
          </div>

          <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
            <div className="body-sm-regular-12 text-neutral-alpha-500">
              MIST
            </div>
            <NumericFormat
              value={gasPrice ?? ""}
              allowLeadingZeros
              allowNegative={false}
              thousandSeparator=","
              className="body-md-semibold-14 w-full bg-transparent outline-none"
              decimalScale={2}
              onValueChange={({ floatValue }) => {
                return setGasPrice(floatValue);
              }}
            />
          </div>

          <div className="item-center flex justify-between">
            <div className="body-xs-regular-10 text-white-700 mt-1">
              Est Gas Fee:
            </div>
            <div className="text-white-1000 body-xs-medium-10 mt-1">
              ~{formatNumber(maxGasFeeBuySell)} SUI
            </div>
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between">
            <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
              <CoinTip />
              <span className="text-white-700">Tip Amount</span>
            </div>
            <AppToggle
              value={isEnableTip}
              onChange={() => {
                if (!isEnableTip && !+tipAmount) {
                  setTipAmount("0.02");
                }
                setIsEnableTip(!isEnableTip);
              }}
            />
          </div>

          <div className="body-xs-regular-10 text-white-700">
            The amount you send to validators to pick up your transaction
            faster. Minimum is 0.02 SUI.
          </div>

          {isEnableTip && (
            <div className="border-neutral-alpha-50 mt-4 flex items-center gap-2 rounded-[4px] border p-2">
              <div className="body-sm-regular-12 text-neutral-alpha-500">
                <AppLogoNetwork
                  network={NETWORKS.SUI}
                  isBase
                  className="h-[12px] w-[12px]"
                />
              </div>
              <NumericFormat
                value={tipAmount ?? ""}
                allowLeadingZeros
                allowNegative={false}
                thousandSeparator=","
                className="body-md-semibold-14 w-full bg-transparent outline-none"
                decimalScale={6}
                onValueChange={({ floatValue }) => {
                  return setTipAmount(floatValue);
                }}
              />
            </div>
          )}
        </div>

        <div>
          <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
            <Setting /> Custom Buy Amount ({pair?.tokenQuote?.symbol})
          </div>
          <div className="grid grid-cols-4 gap-2">
            {[...Array(4)].map((_, index: number) => {
              return (
                <div
                  className="border-neutral-alpha-50 gap-2 rounded-[4px] border p-2 text-center"
                  key={index}
                >
                  <NumericFormat
                    value={defaultBuyAmount[index] ?? ""}
                    allowLeadingZeros
                    allowNegative={false}
                    thousandSeparator=","
                    className="body-md-semibold-14 w-full bg-transparent outline-none"
                    decimalScale={0}
                    onValueChange={({ floatValue }) => {
                      return setDefaultBuyAmount((prevState: any) => {
                        prevState[index] = floatValue;
                        return prevState;
                      });
                    }}
                  />
                </div>
              );
            })}
          </div>
        </div>

        <div>
          <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
            <Setting /> Custom Sell Amount (%)
          </div>
          <div className="grid grid-cols-4 gap-2">
            {[...Array(4)].map((_, index: number) => {
              return (
                <div
                  className="border-neutral-alpha-50 gap-2 rounded-[4px] border p-2 text-center"
                  key={index}
                >
                  <NumericFormat
                    value={defaultSellPercent[index] ?? ""}
                    allowLeadingZeros
                    allowNegative={false}
                    thousandSeparator=","
                    className="body-md-semibold-14 w-full bg-transparent outline-none"
                    decimalScale={2}
                    onValueChange={({ floatValue }) => {
                      return setDefaultSellPercent((prevState: any) => {
                        prevState[index] = floatValue;
                        return prevState;
                      });
                    }}
                  />
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <div
        onClick={onSave}
        className="text-neutral-beta-900 action-sm-medium-14 bg-neutral-alpha-1000 my-2 flex cursor-pointer items-center justify-center gap-1 rounded-[6px] px-2 py-[10px]"
      >
        Save Settings
      </div>
    </div>
  );
};

import * as React from "react";
import { CheckboxIcon, CheckedIcon } from "@/assets/icons";
import { isZero, toStringBN } from "@/utils/helper";
import { NumericFormat } from "react-number-format";
import { TPair } from "@/types";
import { AppDropdown } from "@/components";

const OPTIONS_RESOLUTION = [
  {
    name: "D",
    value: "D",
  },
  {
    name: "H",
    value: "H",
  },
  {
    name: "M",
    value: "M",
  },
];

export const DCAForm = ({
  marketCapTo,
  setMarketCapTo,
  marketCapFrom,
  setMarketCapFrom,
  resolution,
  setResolution,
  amountTime,
  setAmountTime,
  amountOrders,
  setAmountOrders,
  isSelectMCRange,
  setIsSelectMCRange,
  sellPercent,
  pair,
}: {
  isSelectMCRange: boolean;
  setIsSelectMCRange: (value: boolean) => void;
  marketCapTo: any;
  setMarketCapTo: (value: any) => void;
  marketCapFrom: any;
  setMarketCapFrom: (value: any) => void;
  resolution: any;
  setResolution: (value: any) => void;
  amountTime: any;
  setAmountTime: (value: any) => void;
  amountOrders: any;
  setAmountOrders: (value: any) => void;
  sellPercent?: any;
  pair: TPair;
}) => {
  return (
    <div>
      <div className="mt-4 grid grid-cols-2 gap-3">
        <div className="border-white-50 bg-black-900 flex items-center gap-2 rounded-[6px] border">
          <NumericFormat
            allowNegative={false}
            defaultValue={amountTime ?? ""}
            value={amountTime ?? ""}
            placeholder="Overs"
            className="body-sm-regular-12 placeholder:text-white-300 w-full bg-transparent pl-2 outline-none"
            decimalScale={0}
            onValueChange={({ floatValue }) => {
              return setAmountTime(floatValue || "");
            }}
          />
          <div className="body-sm-regular-12 tablet:bg-white-50 bg-white-100 rounded-r-[4px]">
            <AppDropdown
              options={OPTIONS_RESOLUTION}
              onSelect={(value) => {
                setResolution(value);
              }}
              value={resolution}
            />
          </div>
        </div>
        <div className="border-white-50 bg-black-900 flex items-center gap-2 rounded-[6px] border px-[8px] py-[6px]">
          <NumericFormat
            allowNegative={false}
            disabled={+sellPercent === 100}
            value={amountOrders ?? ""}
            defaultValue={amountOrders ?? ""}
            placeholder="Overs"
            className="body-sm-regular-12 placeholder:text-white-300 w-full bg-transparent outline-none"
            decimalScale={0}
            onValueChange={({ floatValue }) => {
              return setAmountOrders(floatValue || "");
            }}
          />
          <div className="body-sm-regular-12 text-white-300">Orders</div>
        </div>
      </div>

      <div
        className="tablet:mt-6 body-sm-regular-12 mt-4 flex w-max cursor-pointer items-center gap-2"
        onClick={() => setIsSelectMCRange(!isSelectMCRange)}
      >
        {isSelectMCRange ? <CheckedIcon /> : <CheckboxIcon />}
        Market Cap Range
      </div>

      {isSelectMCRange && (
        <div className="mt-2 grid grid-cols-2 gap-3">
          <div className="border-white-50 bg-black-900 flex items-center gap-2 rounded-[6px] border px-[8px] py-[6px]">
            <NumericFormat
              defaultValue={marketCapFrom ?? ""}
              allowLeadingZeros
              allowNegative={false}
              thousandSeparator=","
              className="body-sm-regular-12 w-full bg-transparent outline-none"
              decimalScale={pair?.tokenBase?.decimals}
              onValueChange={({ floatValue }) => {
                if (!isZero(floatValue) || floatValue === 0) {
                  return setMarketCapFrom(toStringBN(floatValue));
                } else {
                  setMarketCapFrom(null);
                }
              }}
            />
            <div className="body-sm-regular-12 text-white-300">$</div>
          </div>

          <div className="border-white-50 bg-black-900 flex items-center gap-2 rounded-[6px] border px-[8px] py-[6px]">
            <NumericFormat
              defaultValue={marketCapTo ?? ""}
              allowLeadingZeros
              allowNegative={false}
              thousandSeparator=","
              className="body-sm-regular-12 w-full bg-transparent outline-none"
              decimalScale={pair?.tokenBase?.decimals}
              onValueChange={({ floatValue }) => {
                if (!isZero(floatValue) || floatValue === 0) {
                  return setMarketCapTo(toStringBN(floatValue));
                } else {
                  setMarketCapTo(null);
                }
              }}
            />
            <div className="body-sm-regular-12 text-white-300">$</div>
          </div>
        </div>
      )}
    </div>
  );
};

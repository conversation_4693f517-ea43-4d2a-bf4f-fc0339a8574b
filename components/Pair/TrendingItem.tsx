"use client";

import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useOrder } from "@/hooks";
import { RootState, AppDispatch } from "@/store";
import { TPair } from "@/types";
import { dividedBN, getTimeFormatBoots, multipliedBN } from "@/utils/helper";
import { getCirculatingSupply, getClassColor } from "@/utils/pair";
import Link from "next/link";
import { WarningLiquidity } from "../ListPair";
import { PairSocials } from "./Socials";
import { formatNumber, formatToPercent } from "@/utils/format";
import {
  BurntIcon,
  DexscreenerIcon,
  FlashIcon,
  SearchIcon,
} from "@/assets/icons";
import {
  AppCopy,
  AppLogoNetwork,
  AppNumber,
  AppNumberUSDCustomDecimals,
  AppTimeDisplay,
  BaseToken,
  PairFavourite,
  PairIssues,
} from "@/components";
import clsx from "clsx";
import { setIsShowModalAddWallet } from "@/store/metadata.store";
import { useMediaQuery } from "react-responsive";
import { useRouter } from "next/navigation";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import Tooltip from "rc-tooltip";
import moment from "moment";
import { toastSuccess } from "@/libs/toast";
import { getLinkTxExplorer } from "@/utils/helper";
import { useExternalWallet } from "@/hooks";
import BigNumber from "bignumber.js";
import { toastError } from "../../libs/toast";
import { useNetworkBalance } from "@/hooks/useNetworkBalance";
import { useLogin } from "../../hooks/useLogin";
import {
  getNetworkSymbol,
  getNetworkConfig,
} from "@/app/providers/networkChains";
import { useNetwork } from "@/context";

export const PairTrendingItem = React.memo(
  ({
    item,
    index,
    buyAmount,
    selectedResolution,
    isFavorite,
    filterParams,
  }: {
    item: TPair;
    index?: number;
    selectedResolution?: string;
    buyAmount: string;
    isItemInBasicTable?: boolean;
    isFavorite?: boolean;
    filterParams?: any;
  }) => {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const { currentNetwork } = useNetwork();
    const router = useRouter();
    const quotePrices = useSelector(
      (state: RootState) => state.metadata.quotePrices
    );
    const accessToken = useSelector(
      (state: RootState) => state.user.accessToken
    );
    const isExternalWallet = useSelector(
      (state: RootState) => state.user.isExternalWallet
    );
    const wallets = useSelector((state: RootState) => state.user.wallets);
    const { quickBuy } = useOrder();
    const dispatch = useDispatch<AppDispatch>();
    const isTablet = useMediaQuery({ query: "(max-width: 992px)" });
    const { onBuyToken } = useExternalWallet();
    const { balance: networkBalance, isConnected } = useNetworkBalance();
    const { onLogin } = useLogin();

    if (!item) return null;

    const tokenBasePriceUsd = item?.priceUsd || item?.tokenBase?.priceUsd;
    const tokenQuotePriceUsd =
      quotePrices[item.tokenQuote.address]?.priceUsd ||
      item?.tokenQuote?.priceUsd;
    const marketCapUsd = multipliedBN(
      tokenBasePriceUsd,
      getCirculatingSupply(item)
    );

    const onBuySuccess = async (digest?: string) => {
      toastSuccess(
        "Success",
        `You bought ${buyAmount} ${item?.tokenBase?.symbol}`,
        {
          link: getLinkTxExplorer(currentNetwork, digest || ""),
          text: "View Explorer",
        }
      );
      setIsLoading(false);
    };

    const handleQuickBuy = async (e: any) => {
      e.preventDefault();
      e.stopPropagation();
      if (isLoading) return;

      if (isExternalWallet) {
        if (!isConnected) {
          return toastError("Error", "Wallet not connected");
        }

        if (
          !+networkBalance ||
          new BigNumber(buyAmount).gt(new BigNumber(networkBalance))
        ) {
          const networkSymbol = getNetworkSymbol(currentNetwork);
          return toastError("Error", `Insufficient ${networkSymbol} Balance`);
        }

        onBuyToken(item, buyAmount, setIsLoading, onBuySuccess).then();
        return;
      }

      if (!accessToken) {
        onLogin();
        return;
      }
      if (!wallets.length) {
        dispatch(setIsShowModalAddWallet({ isShow: true }));
        return;
      }

      const nativeTokenAddress =
        getNetworkConfig(currentNetwork)?.nativeTokenAddress?.full;
      quickBuy(null, item, buyAmount, nativeTokenAddress, false).then();
    };

    const CellItemWrapper = React.memo(
      ({
        className = "",
        children,
      }: {
        className?: string;
        children: React.ReactNode;
      }) => (
        <td className={className}>
          <Link href={`/${item.network}/${encodeURI(item.slug)}`}>
            {children}
          </Link>
        </td>
      )
    );
    CellItemWrapper.displayName = "CellItemWrapper";

    const getPercentDisplay = (resolution: string) => {
      const percent = item?.stats?.percent[resolution];
      const isPositive = +percent > 0;
      return (
        <div className={isPositive ? "text-green-500" : "text-red-500"}>
          {percent ? `${formatNumber(percent, 2)}%` : "--"}
        </div>
      );
    };

    // if (!!filterParams && !isFilterValidPair(item, filterParams)) return null;
    if (isTablet) {
      return (
        <div className="border-white-50 bg-white-25 body-xs-regular-8 min-h-[50px] rounded-[4px] border p-[8px]">
          <div
            className="flex cursor-pointer items-center gap-[6px]"
            onClick={() =>
              router.push(`/${item.network}/${encodeURI(item.slug)}`)
            }
          >
            <div className="flex gap-1">
              {!isFavorite && (
                <div className="body-xs-regular-10 text-white-500 flex min-w-[18px] flex-col justify-center gap-[2px] text-[10px]">
                  {Number(index) + 1 > 0 && <div>#{Number(index) + 1}</div>}
                  <AppTimeDisplay
                    timestamp={item.timestamp * 1000}
                    isAgo
                    suffix=""
                    classNameWrapper="body-xs-regular-10 text-white-700"
                  />
                </div>
              )}
              <BaseToken
                pair={item}
                size={24}
                classNameAvatarToken="w-[24px] h-[24px]"
              />
              <div className="min-w-[50px]">
                <div className="flex flex-col">
                  <div className="body-sm-medium-12 max-w-[50px] truncate">
                    {item?.tokenBase?.symbol || "Unknown"}
                  </div>

                  <div className="flex items-center gap-1">
                    <PairSocials
                      info={item?.tokenBase?.socials}
                      classNameWrapper={"flex gap-1"}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="body-xs-regular-8 flex flex-1 flex-col gap-[2px]">
              <div className="grid w-full grid-cols-3 gap-[10px]">
                <div className="flex min-w-[40px] items-center gap-[2px]">
                  <div className="text-white-800 body-sm-regular-12">
                    <AppNumber
                      value={tokenBasePriceUsd}
                      isForUSD
                      className=" !text-white-800"
                    />
                  </div>
                </div>
                <div className="flex min-w-[40px] items-center gap-[2px]">
                  <div className="text-white-500">MC</div>
                  <div className="!text-white-800 body-sm-regular-12">
                    <AppNumberUSDCustomDecimals
                      value={marketCapUsd}
                      className={getClassColor(marketCapUsd)}
                      decimals={1}
                    />
                  </div>
                </div>
                <div className="flex min-w-[40px] items-center gap-[2px]">
                  <div className="text-white-500">Liq</div>
                  <div className="!text-white-800 body-sm-regular-12">
                    <AppNumberUSDCustomDecimals
                      value={multipliedBN(tokenQuotePriceUsd, item.liquidity)}
                      className={getClassColor(
                        multipliedBN(tokenQuotePriceUsd, item.liquidity)
                      )}
                      decimals={1}
                    />
                  </div>
                </div>
              </div>
              <div className="grid w-full grid-cols-3 gap-[10px]">
                <div className="flex min-w-[40px] items-center gap-[2px]">
                  <div className="text-white-500">Vol</div>
                  <div className="text-white-800 body-sm-regular-12">
                    <AppNumberUSDCustomDecimals
                      value={item.volumeUsd}
                      className={getClassColor(item.volumeUsd)}
                      decimals={1}
                    />
                  </div>
                </div>
                <div className="flex min-w-[40px] items-center gap-[2px]">
                  <div className="text-white-500">{selectedResolution}</div>
                  <div className="text-white-800 body-sm-regular-12">
                    {getPercentDisplay(selectedResolution || "24h")}
                  </div>
                </div>
                <div className="flex min-w-[40px] items-center gap-[2px]">
                  <div
                    className="text-white-800 w-max"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {item?.slug && (
                      <PairIssues
                        key={item?.slug}
                        isShowFull={false}
                        pair={item}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex h-[24px] justify-center">
              <div
                onClick={handleQuickBuy}
                className={`body-xs-medium-10 text-brand-500 border-brand-800 hover:bg-brand-800 flex w-full cursor-pointer items-center justify-center gap-1 rounded-[6px] border px-[8px] py-[4px] `}
              >
                <span className="max-w-[24px] truncate">
                  {buyAmount || "0"}
                </span>
                <AppLogoNetwork network={currentNetwork} isBase />
              </div>
            </div>
          </div>
        </div>
      );
    }
    return (
      <>
        {Number(index) + 1 > 0 && (
          <CellItemWrapper className="sticky left-0 bg-[#06070e] group-hover:bg-[#13141a] lg:static lg:bg-transparent">
            <div className="td flex w-[26px] justify-center px-[4px] md:w-auto md:px-[8px]">
              <div className="body-xs-regular-10 text-white-500 w-[26px] justify-center md:w-auto">
                #{Number(index) + 1}
              </div>
            </div>
          </CellItemWrapper>
        )}
        <CellItemWrapper
          className={clsx(
            "sticky bg-[#06070e]  group-hover:bg-[#13141a] lg:static lg:bg-transparent",
            isFavorite ? "left-0" : "left-[28px] md:left-[34px]"
          )}
        >
          <div className="td flex justify-center px-[4px] md:px-[8px]">
            <PairFavourite pair={item} />
          </div>
        </CellItemWrapper>
        <CellItemWrapper
          className={clsx(
            "sticky bg-[#06070e] group-hover:bg-[#13141a] lg:static lg:bg-transparent",
            isFavorite
              ? "left-[25px] md:left-[30px]"
              : "left-[54px] md:left-[68px]"
          )}
        >
          <div className="td border-white-50 flex h-[64px] w-[124px] items-center gap-[8px] border-r md:w-auto md:border-0">
            <BaseToken pair={item} />
            <div>
              <div className="flex items-center gap-1">
                <div className="body-sm-medium-12 max-w-[40px] truncate md:max-w-full">
                  {item?.tokenBase?.symbol || "Unknown"}
                </div>
                <div onClick={(e) => e.preventDefault()}>
                  <AppCopy
                    message={item.tokenBase.address}
                    className="text-white-700 hover:text-white-1000"
                  />
                </div>
                <Tooltip
                  overlay={<span>Search on Twitter</span>}
                  placement="bottom"
                  overlayClassName="whitespace-nowrap body-xs-regular-8"
                >
                  <span
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      window.open(
                        `https://x.com/search?q=${item?.tokenBase?.address}`,
                        "_blank",
                        "noopener,noreferrer"
                      );
                    }}
                    className="cursor-pointer"
                  >
                    <SearchIcon className="text-white-500" />
                  </span>
                </Tooltip>

                {!!item?.tokenBase?.isDexscreenerVerified && (
                  <Tooltip
                    overlay="DexScreener Social Updated"
                    placement="bottom"
                    overlayClassName="whitespace-nowrap"
                  >
                    <DexscreenerIcon className="text-white-500 h-[14px] w-[14px]" />
                  </Tooltip>
                )}

                {item?.tokenBase?.boostFactor &&
                  moment(
                    item?.tokenBase?.isBoostedUntil,
                    "YYYY-MM-DD H:mm:ss.S Z"
                  ).valueOf() > moment().valueOf() && (
                    <div className="body-sm-medium-12 flex items-center text-orange-500">
                      <FlashIcon className="h-4 w-4" />
                      {getTimeFormatBoots(item?.tokenBase?.boostFactor || 0)}
                    </div>
                  )}

                <WarningLiquidity pair={item} />
              </div>

              <div className="flex items-center gap-1">
                <PairSocials info={item?.tokenBase?.socials} />
              </div>
            </div>
          </div>
        </CellItemWrapper>

        {!isFavorite && (
          <CellItemWrapper>
            <div className="td body-sm-regular-12 text-white-500 flex min-w-[67px] justify-center md:min-w-[80px]">
              <AppTimeDisplay
                timestamp={item.timestamp * 1000}
                isAgo
                suffix=""
              />
            </div>
          </CellItemWrapper>
        )}
        <CellItemWrapper>
          <div className="td body-sm-regular-12 flex h-full w-full justify-end ">
            <AppNumber
              value={multipliedBN(tokenQuotePriceUsd, item.liquidity)}
              isForUSD
              className={getClassColor(
                multipliedBN(tokenQuotePriceUsd, item.liquidity)
              )}
            />
          </div>
        </CellItemWrapper>

        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center">
            <div>
              <AppNumber
                value={marketCapUsd}
                isForUSD
                className={getClassColor(marketCapUsd)}
              />
            </div>
            <div>
              <AppNumber
                value={tokenBasePriceUsd}
                isForUSD
                className="body-xs-regular-10 text-white-500"
              />
            </div>
          </div>
        </CellItemWrapper>
        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center">
            <div>
              <AppNumber value={item?.tokenBase?.holdersCount} />
            </div>
          </div>
        </CellItemWrapper>

        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center ">
            <div>
              {formatNumber(
                item?.stats?.totalNumTxn[selectedResolution || "24h"],
                2
              )}
            </div>
            <div className="text-white-500 flex gap-[2px]">
              <span className="text-green-500">
                {formatNumber(
                  item?.stats?.buyTxn[selectedResolution || "24h"],
                  2
                )}
              </span>{" "}
              /{" "}
              <span className="text-red-500">
                {formatNumber(
                  item?.stats?.sellTxn[selectedResolution || "24h"],
                  2
                )}
              </span>
            </div>
          </div>
        </CellItemWrapper>

        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center">
            <AppNumber
              value={item?.stats?.volume[selectedResolution || "24h"]}
              isForUSD
            />
          </div>
        </CellItemWrapper>

        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center">
            <div
              className={
                +item?.stats?.percent["5m"] > 0
                  ? "text-green-500"
                  : "text-red-500"
              }
            >
              {item?.stats?.percent["5m"]
                ? `${formatNumber(item?.stats?.percent["5m"], 2)}%`
                : "--"}
            </div>
          </div>
        </CellItemWrapper>

        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center">
            <div
              className={
                +item?.stats?.percent["1h"] > 0
                  ? "text-green-500"
                  : "text-red-500"
              }
            >
              {item?.stats?.percent["1h"]
                ? `${formatNumber(item?.stats?.percent["1h"], 2)}%`
                : "--"}
            </div>
          </div>
        </CellItemWrapper>

        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center">
            <div
              className={
                +item?.stats?.percent["6h"] > 0
                  ? "text-green-500"
                  : "text-red-500"
              }
            >
              {item?.stats?.percent["6h"]
                ? `${formatNumber(item?.stats?.percent["6h"], 2)}%`
                : "--"}
            </div>
          </div>
        </CellItemWrapper>

        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center">
            <div
              className={
                +item?.stats?.percent["24h"] > 0
                  ? "text-green-500"
                  : "text-red-500"
              }
            >
              {item?.stats?.percent["24h"]
                ? `${formatNumber(item?.stats?.percent["24h"], 2)}%`
                : "--"}
            </div>
          </div>
        </CellItemWrapper>

        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center">
            <div className="flex items-center justify-center gap-1">
              <Tooltip
                overlay={
                  <div>
                    <div className="item-center flex gap-1">
                      <div>Token Burnt</div>
                      {formatNumber(item?.tokenBase.amountBurned)}
                    </div>
                    <div className="item-center flex gap-1">
                      <div>Burnt rate</div>
                      {formatToPercent(
                        dividedBN(
                          item?.tokenBase.amountBurned,
                          item?.tokenBase.totalSupply
                        )
                      )}
                    </div>
                  </div>
                }
                placement="top"
              >
                <div className="body-xs-medium-10 md:body-sm-medium-12 flex cursor-pointer items-center gap-1">
                  <BurntIcon />
                  {formatToPercent(
                    dividedBN(
                      item?.tokenBase.amountBurned,
                      item?.tokenBase.totalSupply
                    )
                  )}
                </div>
              </Tooltip>
            </div>

            {item?.recentDevActionStatus?.recentDevAction && (
              <div
                className={`text-[12px] font-normal uppercase leading-[18px] ${
                  item.recentDevActionStatus.recentDevAction.toUpperCase() ===
                  "HOLD"
                    ? "text-white-1000"
                    : ["BUY", "ADD"].includes(
                        item.recentDevActionStatus.recentDevAction.toUpperCase()
                      )
                    ? "text-green-600"
                    : ["SELL", "SELLALL"].includes(
                        item.recentDevActionStatus.recentDevAction.toUpperCase()
                      )
                    ? "text-red-600"
                    : ""
                }`}
              >
                {item?.recentDevActionStatus?.recentDevAction
                  .replace(/SELLALL/gi, "SELL ALL")
                  .toUpperCase()}
              </div>
            )}
          </div>
        </CellItemWrapper>

        <CellItemWrapper>
          <div className="td body-sm-regular-12 h-full w-full flex-col !items-end justify-center">
            <div className={`td w-full justify-center`}>
              <PairIssues pair={item} />
            </div>
          </div>
        </CellItemWrapper>

        <CellItemWrapper className="sticky right-0 bg-[#06070e] group-hover:bg-[#13141a] lg:static lg:bg-transparent">
          <div className="td border-white-50 flex h-[64px] justify-center border-l md:border-0">
            <div
              onClick={handleQuickBuy}
              className={`body-sm-medium-12 text-brand-500 bg-brand-800 border-brand-800 flex min-w-[60px] cursor-pointer items-center justify-center gap-1 rounded-[6px] border p-[2px] md:min-w-[92px] md:p-[8px]`}
            >
              <FlashIcon />
              {buyAmount || "0"}
              <AppLogoNetwork network={currentNetwork} isBase />
            </div>
          </div>
        </CellItemWrapper>
      </>
    );
  }
);

PairTrendingItem.displayName = "PairTrendingItem";

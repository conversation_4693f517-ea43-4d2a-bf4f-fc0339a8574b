import _, { isEmpty } from "lodash";
import Tooltip from "rc-tooltip";
import React, {
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useSelector } from "react-redux";
import {
  ChevronDownIcon,
  CopyIcon,
  ExternalLink,
  InfoIcon,
  IssueIcon,
  PassIcon,
} from "@/assets/icons";
import {
  AppButton,
  AppNumber,
  AppSymbolToken,
  CountdownLock,
  getLockMethod,
} from "@/components";
import { BREAKPOINT } from "@/enums/responsive.enum";
import { useAudit } from "@/hooks";
import useWindowSize from "@/hooks/useWindowSize";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { toastError, toastSuccess } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TPair, TWsOrder } from "@/types";
import { MOONBAGS_API_URL, NETWORKS, SUI_DECIMALS } from "@/utils/contants";
import {
  formatAgeTime,
  formatNumber,
  formatShortAddress,
  formatToPercent,
} from "@/utils/format";
import {
  convertMistToDec,
  convertUnit2Real,
  copyToClipboard,
  dividedBN,
  getLinkAddressExplorer,
  getLinkTokenExplorer,
  isZero,
} from "@/utils/helper";
import { getCirculatingSupply, getRealReserveQuote } from "@/utils/pair";
import Storage from "@/libs/storage";
import { EDex, OrderStatus } from "@/enums";
import { normalizeStructTag } from "@mysten/sui/utils";
import { getCoinBalanceOnchain } from "@/utils/suiClient";
import config from "@/config";
import BigNumber from "bignumber.js";
import { ModalStake } from "@/modals/ModalStake";
import axios from "axios";
import { RootPairContext } from "@/app/[network]/(token_detail)/provider";

const ItemInfo = ({
  children,
  className,
}: {
  children: any;
  className?: string;
}) => {
  return (
    <div
      className={`border-neutral-alpha-50 flex items-center justify-between gap-2 border-b border-dashed p-2.5 ${className}`}
    >
      {children}
    </div>
  );
};

const Label = ({ label, info }: { label: ReactNode; info?: string }) => {
  return (
    <div className="text-neutral-alpha-800 flex items-center gap-1 text-[12px] font-normal leading-[1.5]">
      {label}{" "}
      {info && (
        <Tooltip overlay={info} placement="top">
          <InfoIcon className="h-[12px] w-[12px] cursor-pointer" />
        </Tooltip>
      )}
    </div>
  );
};
const YourLockedAmount = ({
  isCompleteTimestamp,
}: {
  isCompleteTimestamp: boolean;
}) => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const [amountLocked, setAmountLocked] = useState<any>(0);
  const { pair } = useContext(RootPairContext) as { pair: TPair };
  const { tokenBase } = pair;
  const { wallets } = useSelector((state: RootState) => state.user);
  const [walletsSelected, setWalletsSelected] = useState<string[]>(
    Storage.getWalletAddresses() || [wallets[0]?.address]
  );

  const handleClaimMyLockedAmount = async () => {
    try {
      await rf.getRequest("NewOrderRequest").claimMyLockedAmount(NETWORKS.SUI, {
        tokenAddress: pair?.tokenBase?.address,
        wallets: walletsSelected,
      });
      getAmountLocked();
      toastSuccess("Success", "Claim Successfully!");
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };
  const getAmountLocked = async () => {
    try {
      if (!walletsSelected.length) return;
      const res = await rf
        .getRequest("PriceRequest")
        .getLockedAmount(NETWORKS.SUI, {
          walletsSelected: walletsSelected,
        });
      const totalBalance = res?.reduce((sum: number, item: any) => {
        if (
          normalizeStructTag(item?.token?.address) ===
          normalizeStructTag(pair?.tokenBase?.address)
        ) {
          return sum + (parseFloat(item?.balance) || 0);
        }
        return sum;
      }, 0);

      setAmountLocked(totalBalance);
    } catch (e: any) {
      console.error(e);
    }
  };

  const handleWhenWalletsSelected = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    setWalletsSelected(data?.walletsSelected || []);
  };

  const handleWhenTradeSucceeded = (event: TBroadcastEvent) => {
    const data: TWsOrder = event.detail;
    if (data.status !== OrderStatus.SUCCESS || data.pairId !== pair.pairId) {
      return;
    }
    getAmountLocked();
  };

  useEffect(() => {
    if (!accessToken) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.WALLETS_SELECTED,
      handleWhenWalletsSelected
    );
    AppBroadcast.on(BROADCAST_EVENTS.TRADE_SUCCEEDED, handleWhenTradeSucceeded);
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.TRADE_SUCCEEDED,
        handleWhenTradeSucceeded
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.WALLETS_SELECTED,
        handleWhenWalletsSelected
      );
    };
  }, [accessToken]);

  useEffect(() => {
    if (!accessToken) return;
    if (!isEmpty(walletsSelected)) {
      getAmountLocked();
    }
  }, [accessToken, walletsSelected]);

  if (!accessToken || isZero(amountLocked)) return <></>;

  return (
    <ItemInfo>
      <Label label={"Your Locked Amount "} info={""} />
      <div
        className={`flex items-center gap-2 text-[12px] font-normal leading-[1.5]`}
      >
        <div>
          {formatNumber(amountLocked, 2)} ${tokenBase?.symbol || "Token"}
        </div>
        <AppButton
          size="small"
          variant="buy"
          className="px-2"
          disabled={!isCompleteTimestamp}
          onClick={handleClaimMyLockedAmount}
        >
          Claim
        </AppButton>
      </div>
    </ItemInfo>
  );
};

const PoolInfor = () => {
  const [show, setShow] = useState<boolean>(true);
  const { pair, reserveBase, reserveQuote } = useContext(RootPairContext) as {
    pair: TPair;
    reserveBase: string;
    reserveQuote: string;
  };
  const { windowWidth } = useWindowSize();

  useEffect(() => {
    if (windowWidth < BREAKPOINT.DESKTOP) {
      setShow(false);
    } else {
      setShow(true);
    }
  }, [windowWidth]);

  return (
    <div
      className={`px-2  ${
        show ? "mb-10 pb-3" : ""
      } tablet:rounded-[0px] tablet:border-0 tablet:border-b border-neutral-alpha-50 rounded-[6px] border`}
    >
      <div
        onClick={() => setShow(!show)}
        className="flex cursor-pointer justify-between gap-2 py-3"
      >
        <div className="flex items-center gap-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M5.50239 4.5C3.45643 4.5 1.8335 6.08629 1.8335 8C1.8335 9.91371 3.45643 11.5 5.50239 11.5C5.75348 11.5 5.99819 11.476 6.23429 11.4304C6.50542 11.3781 6.76765 11.5555 6.81998 11.8266C6.87232 12.0977 6.69494 12.36 6.4238 12.4123C6.12551 12.4699 5.81734 12.5 5.50239 12.5C2.94352 12.5 0.833496 10.5046 0.833496 8C0.833496 5.49543 2.94352 3.5 5.50239 3.5C5.81734 3.5 6.12551 3.53012 6.4238 3.58769C6.69494 3.64003 6.87232 3.90225 6.81998 4.17339C6.76765 4.44453 6.50542 4.62191 6.23428 4.56957C5.99819 4.524 5.75348 4.5 5.50239 4.5ZM10.4979 4.5C8.45197 4.5 6.82904 6.08629 6.82904 8C6.82904 9.91371 8.45197 11.5 10.4979 11.5C12.5439 11.5 14.1668 9.91371 14.1668 8C14.1668 6.08629 12.5439 4.5 10.4979 4.5ZM5.82904 8C5.82904 5.49543 7.93907 3.5 10.4979 3.5C13.0568 3.5 15.1668 5.49543 15.1668 8C15.1668 10.5046 13.0568 12.5 10.4979 12.5C7.93907 12.5 5.82904 10.5046 5.82904 8Z"
              fill="white"
            />
          </svg>

          <div className="text-[12px] font-medium leading-[1.5]">
            Pool Infor
          </div>
        </div>
        <div className="text-neutral-alpha-500 flex items-center gap-1 text-[12px] font-normal leading-[1.5]">
          {show ? "Hide" : "Show"}
          <ChevronDownIcon
            className={`h-[14px] w-[14px] ${
              show ? "rotate-[-180deg]" : ""
            } duration-500`}
          />
        </div>
      </div>
      {show && (
        <div className="bg-neutral-beta-500 rounded-[4px]">
          <ItemInfo>
            <Label
              label={
                <>
                  Pooled{" "}
                  <AppSymbolToken
                    symbol={pair?.tokenBase?.symbol || "Unknown"}
                  />
                </>
              }
            />
            <div className="text-neutral-alpha-1000 text-[12px] font-normal leading-[1.5]">
              <AppNumber
                value={convertUnit2Real(reserveBase, pair?.tokenBase?.decimals)}
              />
            </div>
          </ItemInfo>

          <ItemInfo>
            <Label
              label={
                <>
                  Pooled{" "}
                  <AppSymbolToken
                    symbol={pair?.tokenQuote?.symbol || "Unknown"}
                  />
                </>
              }
            />
            <div className="text-neutral-alpha-1000 text-[12px] font-normal leading-[1.5]">
              <AppNumber
                value={convertUnit2Real(
                  getRealReserveQuote(pair?.dex?.dex, reserveQuote),
                  pair?.tokenQuote?.decimals
                )}
              />
            </div>
          </ItemInfo>

          <ItemInfo>
            <Label
              label={
                <AppSymbolToken symbol={pair?.tokenBase?.symbol || "Unknown"} />
              }
            />
            <div className="flex items-center gap-2 text-[12px] font-normal leading-[1.5]">
              <div
                onClick={() => copyToClipboard(pair?.tokenBase?.address)}
                className="body-xs-regular-10 hover:bg-neutral-alpha-100 bg-neutral-alpha-50 flex cursor-pointer items-center gap-1 rounded-[4px] px-4 py-[2px]"
              >
                {formatShortAddress(pair?.tokenBase?.address, 4, 3)}
                <CopyIcon className="h-[12px] w-[12px]" />
              </div>

              <a
                className="text-neutral-alpha-1000"
                target="_blank"
                href={getLinkTokenExplorer(
                  pair?.network,
                  pair?.tokenBase?.address
                )}
              >
                <div className="text-neutral-alpha-1000 flex items-center gap-1">
                  <ExternalLink className="h-[14px] w-[14px]" />
                </div>
              </a>
            </div>
          </ItemInfo>
          <ItemInfo>
            <Label label={"Dev"} />
            {pair?.tokenBase?.deployer ? (
              <a
                className="text-neutral-alpha-1000"
                target="_blank"
                href={getLinkAddressExplorer(
                  pair.network,
                  pair?.tokenBase?.deployer
                )}
              >
                <div className="text-[12px] font-normal leading-[1.5]">
                  {formatShortAddress(pair?.tokenBase?.deployer, 6, 6)}
                </div>
              </a>
            ) : (
              <div className="text-[12px] font-normal leading-[1.5]">--</div>
            )}
          </ItemInfo>
          <ItemInfo className="border-b-0">
            <Label label={"Open Trading"} />
            <div className="text-neutral-alpha-1000 text-[12px] font-normal leading-[1.5]">
              {formatAgeTime(pair?.timestamp * 1000, "ago")}
            </div>
          </ItemInfo>
        </div>
      )}
    </div>
  );
};

const TREASURY_ADDRESS = config.treasuryAddress;

export const PairAuditCheck = () => {
  const [show, setShow] = useState<boolean>(true);
  const [auditToken, setAuditToken] = useState<any>({});
  const [isCompleteTimestamp, setIsCompleteTimestamp] = useState(false);
  const [balanceMoonbagsTreasury, setBalanceMoonbagsTreasury] =
    useState<any>(0);
  const [balanceMoonbagsLocked, setBalanceMoonbagsLocked] = useState<any>(0);
  const [isOpenModalStake, setIsOpenModalStake] = useState<boolean>(false);
  const [isHasManageStake, setIsHasManageStake] = useState<boolean>(false);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);

  const { pair } = useContext(RootPairContext) as { pair: TPair };
  const { tokenBase, dex, lpBurned, lpSupply } = pair || {};

  const isMoonbagsDexOrCetusOrTurboDex =
    dex?.dex &&
    [EDex.MOONBAGS, EDex.CETUS, EDex.TURBOSFINANCE].includes(dex?.dex as EDex);

  const getBalanceTreasury = useCallback(async () => {
    const res = await getCoinBalanceOnchain(
      TREASURY_ADDRESS,
      pair?.tokenBase?.address
    );
    setBalanceMoonbagsTreasury(
      convertMistToDec(res, pair?.tokenBase?.decimals)
    );
  }, [pair?.tokenBase?.address]);

  const getBalanceLocked = useCallback(async () => {
    const res = await axios.get(
      `https://api2.moonbags.io/api/v1/coin-lock/address/${pair?.tokenBase?.address}`
    );
    const data = res?.data;
    setBalanceMoonbagsLocked(data?.totalLockedAmount || 0);
  }, [pair?.tokenBase?.address]);

  useEffect(() => {
    if (!isMoonbagsDexOrCetusOrTurboDex) return;
    Promise.all([getBalanceTreasury(), getBalanceLocked()]);
  }, [
    pair?.pairId,
    isMoonbagsDexOrCetusOrTurboDex,
    getBalanceLocked,
    getBalanceTreasury,
  ]);

  const checkTokenCanStake = async () => {
    try {
      let result = (await fetch(
        `${MOONBAGS_API_URL}/coin/${tokenBase?.address}`
      )) as any;
      result = await result.json();
      if (result?.statusCode === 404) {
        setIsHasManageStake(false);
        return;
      }
      setIsHasManageStake(true);
    } catch (e) {
      setIsHasManageStake(false);
      console.error(e);
    }
  };

  useEffect(() => {
    if (!dex?.dex || !isMoonbagsDexOrCetusOrTurboDex || !accessToken) {
      setIsHasManageStake(false);
      return;
    }

    if ([EDex.MOONBAGS].includes(dex?.dex as EDex)) {
      setIsHasManageStake(true);
      return;
    }

    if ([EDex.CETUS, EDex.TURBOSFINANCE].includes(dex?.dex as EDex)) {
      checkTokenCanStake().then();
      return;
    }
  }, [dex?.dex, isMoonbagsDexOrCetusOrTurboDex, accessToken]);

  useEffect(() => {
    setAuditToken(tokenBase);
    setIsCompleteTimestamp(tokenBase?.lockTimestamp <= new Date().valueOf());
  }, [pair?.pairId]);

  const handleWhenAuditCheckedChange = useCallback(
    async (event: TBroadcastEvent) => {
      const data: any = event.detail;
      if (data.pairId !== pair?.pairId) {
        return;
      }

      setAuditToken({
        ...auditToken,
        mintAuthority: data?.mintAuthority,
        freezeAuthority: data?.freezeAuthority,
        deployer: data?.coinDev,
        amountBurned: data?.amountBurned,
      });
    },
    [pair?.pairId, auditToken]
  );

  useEffect(() => {
    if (isEmpty(pair?.pairId)) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.TOKEN_INFO_AUDIT_UPDATED,
      handleWhenAuditCheckedChange
    );

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.TOKEN_INFO_AUDIT_UPDATED,
        handleWhenAuditCheckedChange
      );
    };
  }, [pair?.pairId, handleWhenAuditCheckedChange]);

  const {
    isMemeDex,
    issueLpBurned,
    issueTop10Holder,
    issueLockedAmount,
    isFlowXDexAndSevenKDex,
    issueDevBalance,
    lpBurnedPercent,
  } = useAudit({ dex, tokenBase, lpBurned, lpSupply });

  const totalIssue = useMemo(() => {
    if (!auditToken && !issueLockedAmount) return 0;

    return [
      issueLockedAmount,
      auditToken?.mintAuthority,
      auditToken?.freezeAuthority,
      issueLpBurned,
      issueTop10Holder,
      issueDevBalance,
    ].filter(Boolean).length;
  }, [auditToken, issueLockedAmount]);

  useEffect(() => {
    if (!totalIssue && auditToken) {
      setShow(false);
    }
  }, [totalIssue, auditToken]);

  return (
    <div className="tablet:mx-0 tablet:gap-0 tablet:mt-0 mx-2.5 mt-2 flex flex-col gap-2">
      <div
        className={`tablet:rounded-[0px] tablet:border-0 tablet:border-b border-neutral-alpha-50 rounded-[6px] border px-2 ${
          show ? "pb-3" : ""
        }`}
      >
        <div
          onClick={() => setShow(!show)}
          className="flex cursor-pointer justify-between gap-2 py-3"
        >
          <div className="flex items-center gap-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.8499 2.44223C8.315 2.17478 7.68539 2.17478 7.15049 2.44223L3.7002 4.16737V9.21036C3.7002 9.73762 4.01603 10.3908 4.79933 11.1967C5.52701 11.9453 6.59892 12.7648 8.00098 13.666C9.41955 12.7647 10.4865 12.0375 11.212 11.3358C11.9687 10.6037 12.3002 9.9512 12.3002 9.21036V4.16783L8.8499 2.44223ZM13.3002 3.8584V9.21036C13.3002 10.3175 12.7775 11.2127 11.9072 12.0545C11.0506 12.8831 9.80611 13.7092 8.26718 14.6811C8.10409 14.7841 7.8963 14.7841 7.73321 14.6811C6.17468 13.6968 4.93626 12.7722 4.08227 11.8937C3.2385 11.0256 2.7002 10.1311 2.7002 9.21036V3.85836C2.7002 3.66897 2.8072 3.49584 2.97659 3.41114L6.70328 1.5478C7.51971 1.13958 8.48069 1.13958 9.29711 1.5478L13.0238 3.41114C13.1932 3.49584 13.3002 3.66901 13.3002 3.8584ZM10.3537 5.9048C10.549 6.10007 10.549 6.41665 10.3537 6.61191L7.95375 9.01191C7.75849 9.20717 7.4419 9.20717 7.24664 9.01191L6.04664 7.81191C5.85138 7.61665 5.85138 7.30007 6.04664 7.1048C6.2419 6.90954 6.55849 6.90954 6.75375 7.1048L7.6002 7.95125L9.64664 5.9048C9.8419 5.70954 10.1585 5.70954 10.3537 5.9048Z"
                fill="white"
              />
            </svg>

            <div className="text-[12px] font-medium leading-[1.5]">
              Audit Check
            </div>

            {totalIssue > 0 && (
              <div className="flex items-center gap-1 rounded-[4px] bg-yellow-900 px-1 py-[2px] text-yellow-500">
                <IssueIcon />
                <div className="text-[10px] leading-[14px]">
                  {totalIssue} {totalIssue > 1 ? "issues" : "issue"}
                </div>
              </div>
            )}
          </div>

          <div className="text-neutral-alpha-500 flex items-center gap-1 text-[12px] font-normal leading-[1.5]">
            {show ? "Hide" : "Show"}
            <ChevronDownIcon
              className={`h-[14px] w-[14px] ${
                show ? "rotate-[-180deg]" : ""
              } duration-500`}
            />
          </div>
        </div>

        {show && (
          <div className="bg-neutral-beta-500 rounded-[4px]">
            <ItemInfo>
              <Label
                label={"Mint Authority"}
                info={"Ability to mint new tokens"}
              />
              <div className="text-neutral-alpha-1000 flex items-center gap-1 text-[12px] font-normal leading-[1.5]">
                {auditToken?.mintAuthority ? (
                  <>
                    Enabled
                    <IssueIcon />
                  </>
                ) : (
                  <>
                    Disabled
                    <PassIcon />
                  </>
                )}
              </div>
            </ItemInfo>
            <ItemInfo>
              <Label
                label={"Honeypot"}
                info={
                  auditToken?.freezeAuthority ? "This token is a honneypot" : ""
                }
              />
              <div className="text-neutral-alpha-1000 flex items-center gap-1  text-[12px] font-normal leading-[1.5]">
                {auditToken?.freezeAuthority ? (
                  <>
                    <IssueIcon />
                  </>
                ) : (
                  <>
                    <PassIcon />
                  </>
                )}
              </div>
            </ItemInfo>
            <ItemInfo>
              <Label
                label={
                  <>
                    <AppSymbolToken symbol={tokenBase?.symbol || "Token"} />{" "}
                    Burned
                  </>
                }
                info={"% of amount is burned"}
              />
              <div
                className={`flex items-center gap-1 text-[12px] font-normal leading-[1.5]`}
              >
                {!!+auditToken?.amountBurned
                  ? formatToPercent(
                      dividedBN(
                        auditToken?.amountBurned,
                        pair?.tokenBase.totalSupply
                      )
                    )
                  : "0%"}
              </div>
            </ItemInfo>
            {!isMemeDex && (
              <>
                <ItemInfo>
                  <Label
                    label={"LP Burned"}
                    info={`${formatNumber(
                      lpBurnedPercent || 0,
                      2,
                      "0"
                    )}% of LP is burned. Highlighted if LP is less than 65% burned`}
                  />
                  <div
                    className={`flex items-center gap-1 text-[12px] font-normal leading-[1.5] ${
                      issueLpBurned ? "text-yellow-500" : ""
                    }`}
                  >
                    {!!+lpBurnedPercent
                      ? `${formatNumber(lpBurnedPercent, 2)}%`
                      : "0%"}
                    {issueLpBurned && <IssueIcon />}
                  </div>
                </ItemInfo>
              </>
            )}

            {isMoonbagsDexOrCetusOrTurboDex &&
              new BigNumber(
                Number(balanceMoonbagsTreasury) + Number(balanceMoonbagsLocked)
              ).gt(0) && (
                <ItemInfo>
                  <Label
                    label={
                      <>
                        <AppSymbolToken symbol={tokenBase?.symbol || "Token"} />{" "}
                        Locked
                      </>
                    }
                    info={`${
                      tokenBase?.symbol || "Token"
                    } collected through trading fee are locked in SHR0 treasury & platform`}
                  />
                  <div
                    className={`flex items-center gap-1 text-[12px] font-normal leading-[1.5]`}
                  >
                    {formatToPercent(
                      (+balanceMoonbagsTreasury +
                        Number(balanceMoonbagsLocked)) /
                        +getCirculatingSupply(pair),
                      5
                    )}
                  </div>
                </ItemInfo>
              )}

            {isFlowXDexAndSevenKDex && (
              <>
                {tokenBase?.lockAmount &&
                  getLockMethod(tokenBase?.lockAmount) && (
                    <ItemInfo>
                      <Label
                        label={"Lock Method"}
                        info={
                          ["TMB", "Trust Me Bro"]?.includes(
                            getLockMethod(tokenBase?.lockAmount)
                          )
                            ? "Coins bought with the first 10% of SUI (24.6% supply) will be locked."
                            : "Coins bought with the first 30% of SUI (50.5% supply) will be locked."
                        }
                      />
                      <div
                        className={`flex items-center gap-1 text-[12px] font-normal leading-[1.5] ${
                          issueLockedAmount ? "text-yellow-500" : ""
                        }`}
                      >
                        {getLockMethod(tokenBase?.lockAmount)}
                      </div>
                    </ItemInfo>
                  )}

                {tokenBase?.lockedAmount && tokenBase.lockAmount !== "0" && (
                  <ItemInfo>
                    <Label label={"Locked Amount"} info={""} />
                    <div
                      className={`flex items-center gap-1 text-[12px] font-normal leading-[1.5]`}
                    >
                      {formatToPercent(
                        dividedBN(
                          convertUnit2Real(
                            tokenBase?.lockedAmount,
                            SUI_DECIMALS
                          ),
                          getCirculatingSupply(pair)
                        )
                      )}
                    </div>
                  </ItemInfo>
                )}

                {!!+tokenBase?.lockTimestamp &&
                  +tokenBase?.lockTimestamp > new Date().valueOf() && (
                    <ItemInfo>
                      <Label label={"Lock Timestamp "} info={""} />
                      <div
                        className={`flex items-center gap-1 text-[12px] font-normal leading-[1.5]`}
                      >
                        <CountdownLock
                          time={tokenBase?.lockTimestamp}
                          onComplete={() => {
                            setIsCompleteTimestamp(true);
                          }}
                        />
                      </div>
                    </ItemInfo>
                  )}
                {!!+tokenBase?.lockTimestamp &&
                  tokenBase.lockAmount !== "0" && (
                    <YourLockedAmount
                      isCompleteTimestamp={isCompleteTimestamp}
                    />
                  )}
              </>
            )}

            {/*stake*/}

            {isHasManageStake && (
              <ItemInfo>
                <Label label={"Token Stake"} />
                <div>
                  <AppButton
                    className="!body-xs-medium-10 !px-3 !py-1"
                    size="small"
                    onClick={() => setIsOpenModalStake(true)}
                  >
                    Manage
                  </AppButton>

                  {isOpenModalStake && (
                    <ModalStake
                      isOpen={isOpenModalStake}
                      onClose={() => setIsOpenModalStake(false)}
                      pair={pair}
                    />
                  )}
                </div>
              </ItemInfo>
            )}

            <ItemInfo>
              <Label
                label={"Top 10 Holder"}
                info={
                  "% owned by top 10 holders. Highlighted if ownership is above 15% "
                }
              />
              <div
                className={`flex items-center gap-1 text-[12px] font-normal leading-[1.5] ${
                  issueTop10Holder ? "text-yellow-500" : ""
                }`}
              >
                {_.isNumber(auditToken?.top10HolderPercent)
                  ? `${auditToken?.top10HolderPercent?.toFixed(2)}%`
                  : "--"}
                {issueTop10Holder && <IssueIcon />}
              </div>
            </ItemInfo>

            <ItemInfo className="border-b-0">
              <Label
                label={"Dev Balance"}
                info={"% owned by dev. Highlighted if ownership is above 10%"}
              />
              <div
                className={`flex items-center gap-1 text-[12px] font-normal leading-[1.5] ${
                  issueDevBalance ? "text-yellow-500" : ""
                }`}
              >
                {_.isNumber(auditToken?.deployerBalancePercent)
                  ? `${auditToken?.deployerBalancePercent?.toFixed(2)}%`
                  : "--"}
                {issueDevBalance && <IssueIcon />}
              </div>
            </ItemInfo>
          </div>
        )}
      </div>
      <PoolInfor />
    </div>
  );
};

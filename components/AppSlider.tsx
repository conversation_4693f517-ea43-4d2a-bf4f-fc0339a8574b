import * as React from "react";

export const AppSlider = ({
  value,
  onChange,
  disabled,
}: {
  value: number | string;
  onChange: (value: number | string) => void;
  disabled?: boolean;
}) => {
  return (
    <div className="relative h-1.5 w-full rounded-lg bg-[#2d2d2d]">
      <div
        className="absolute left-0 top-0 h-1.5 rounded-lg bg-white"
        style={{ width: `${value}%` }}
      />
      <input
        type="range"
        min={0}
        max={100}
        value={+value}
        disabled={disabled}
        onChange={(e) => {
          if (disabled) return;
          onChange(Number(e.target.value));
        }}
        className={`
      absolute left-0 top-0 h-1.5 w-full cursor-pointer appearance-none bg-transparent
      disabled:cursor-not-allowed
      disabled:opacity-50
      [&::-moz-range-thumb]:h-4
      [&::-moz-range-thumb]:w-4
      [&::-moz-range-thumb]:cursor-pointer
      [&::-moz-range-thumb]:rounded-full
      [&::-moz-range-thumb]:border-0
      [&::-moz-range-thumb]:bg-white
      [&::-webkit-slider-thumb]:h-4
      [&::-webkit-slider-thumb]:w-4
      [&::-webkit-slider-thumb]:cursor-pointer
      [&::-webkit-slider-thumb]:appearance-none
      [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-white
    `}
      />
    </div>
  );
};

"use client";

import BigNumber from "bignumber.js";
import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { useOrder } from "@/hooks";
import { RootState } from "@/store";
import { TPair, TPosition } from "@/types";
import * as React from "react";
import rf from "@/services/RequestFactory";
import { AppNumber } from "../AppNumber";
import { formatToPercent } from "@/utils/format";
import { memo, useRef } from "react";
import { ChevronDownIcon } from "@/assets/icons";
import { getDefaultUseAggregator } from "@/utils/dex";
import { useNetwork } from "@/context";

const PositionItem = memo(({ position }: { position: TPosition }) => {
  const router = useRouter();
  const { closePosition } = useOrder();
  const isPnlUp = new BigNumber(position?.pnlToUsd || 0).isGreaterThan(0);
  const { currentNetwork } = useNetwork();

  const onClosePosition = async () => {
    await closePosition(
      100,
      position?.token?.address,
      position.walletName,
      getDefaultUseAggregator(position?.dex as string)
    );
  };

  const getRedirectUrlToPair = async (pairId: string) => {
    const pairDetails: TPair = await rf
      .getRequest("PairRequest")
      .getPair(currentNetwork, pairId);
    return router.push(`/${pairDetails.network}/${pairDetails.slug}`);
  };

  return (
    <div className="bg-white-50 border-white-50 flex h-[24px] w-max items-center gap-1 rounded-[2px] border px-[4px] py-[2px]">
      <div className="flex items-center gap-1">
        <div
          className="body-sm-medium-12 cursor-pointer"
          onClick={() => getRedirectUrlToPair(position?.pair || "")}
        >
          {position.token?.symbol}
        </div>
        <div className="body-sm-regular-12 text-white-500 flex items-center gap-1">
          <AppNumber value={position?.balanceToSui} /> SUI
        </div>

        <div
          className={`body-sm-regular-12 ${
            isPnlUp ? "text-green-500" : "text-red-500"
          }`}
        >
          {formatToPercent(position?.pnlToPercent || 0)}
        </div>
      </div>
      <div
        onClick={onClosePosition}
        className="border-white-50 body-xs-medium-10 text-brand-500 cursor-pointer border-l pl-1"
      >
        Sell
      </div>
    </div>
  );
});

PositionItem.displayName = "PositionItem";

export const MyPositions = memo(() => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const positions = useSelector((state: RootState) => state.user.positions);
  const containerRef = useRef<any>(null);

  if (!accessToken || !positions.length) return <></>;

  const scroll = () => {
    containerRef.current.scrollBy({
      top: 0,
      left: +500,
      behavior: "smooth",
    });
  };

  return (
    <div className="border-white-50 flex h-[32px] items-center border-b py-[2px]">
      <div className="body-sm-medium-12 border-brand-800 text-white-800 flex items-center gap-1 border-l px-[16px]">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M2.25684 2.0228C2.45535 1.62578 2.86112 1.375 3.305 1.375H8.695C9.13888 1.375 9.54465 1.62578 9.74316 2.0228L10.4732 3.48295C10.5203 3.57703 10.5567 3.67566 10.582 3.77702C10.5837 3.78301 10.5851 3.78905 10.5865 3.79516C10.612 3.90315 10.625 4.0141 10.625 4.12582V9.1875C10.625 9.98141 9.98141 10.625 9.1875 10.625H2.8125C2.01859 10.625 1.375 9.98141 1.375 9.1875V4.12582C1.375 4.01411 1.38802 3.90316 1.41355 3.79516C1.41487 3.78906 1.41634 3.783 1.41796 3.77701C1.44331 3.67566 1.47972 3.57703 1.52676 3.48295L2.25684 2.0228ZM2.125 4.25V9.1875C2.125 9.5672 2.4328 9.875 2.8125 9.875H9.1875C9.5672 9.875 9.875 9.5672 9.875 9.1875V4.25H2.125ZM9.64324 3.5H2.35676L2.92766 2.35821C2.99912 2.21528 3.1452 2.125 3.305 2.125H8.695C8.8548 2.125 9.00088 2.21528 9.07234 2.35821L9.64324 3.5ZM4.375 5.75C4.375 5.54289 4.54289 5.375 4.75 5.375V6.125C4.54289 6.125 4.375 5.95711 4.375 5.75ZM4.75 6.125H7.25C7.45711 6.125 7.625 5.95711 7.625 5.75C7.625 5.54289 7.45711 5.375 7.25 5.375H4.75V6.125Z"
            fill="white"
          />
        </svg>
        Positions
      </div>

      <div
        ref={containerRef}
        className="hide-scroll flex flex-1 overflow-x-auto "
      >
        <div className="mr-[40px] flex gap-2">
          {positions.map((item, index) => {
            return <PositionItem position={item} key={index} />;
          })}
        </div>
      </div>

      {positions.length > window.innerWidth / 160 && (
        <div
          onClick={() => scroll()}
          className="absolute right-0 cursor-pointer pl-[16px] pr-[8px]"
          style={{
            background:
              "linear-gradient(270deg, #08090C 72.71%, rgba(0, 0, 0, 0.00) 100%)",
          }}
        >
          <ChevronDownIcon className="w-4 rotate-[-90deg]" />
        </div>
      )}
    </div>
  );
});

MyPositions.displayName = "MyPositions";

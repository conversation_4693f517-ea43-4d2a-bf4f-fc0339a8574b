import React, { useContext, useEffect, useMemo, useState } from "react";
import { Submit<PERSON>and<PERSON>, useForm } from "react-hook-form";
import {
  LimitedOfferIcon,
  PreviewVerifyIcon,
  StandardIcon,
} from "@/assets/icons";
import { AppButton, AppLogoNetwork } from "@/components";
import { toastError, toastSuccess } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { isValidCAAddress } from "@/utils/helper";
import { PreviewToken } from "./preview-token.part";
import { TPair } from "@/types";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import config from "@/config";
import { IFormData, PayType, VerifyType } from "../types";
import InputField from "../InputField";
import UploadSection from "../UploadSection";
import AgreementCheckbox from "../AgreementCheckbox";
import { SelectedWalletPayment } from "../SelectedWalletPayment";
import { RootPairContext } from "@/app/[network]/(token_detail)/provider";

interface CommunityTakeoverProps {
  changeTab: (tab: string) => void;
}
const CommunityTakeover = ({ changeTab }: CommunityTakeoverProps) => {
  const network = "sui";
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };
  const {
    handleSubmit,
    register,
    watch,
    trigger,
    reset,
    setValue,
    setError,
    clearErrors,
    formState: { errors },
  } = useForm<IFormData>({
    criteriaMode: "all",
  });

  const [selectOptionsPay, setSelectOptionsPay] = useState<PayType>(
    PayType.LIMITED_OFFER
  );
  const [deletePreview, setDeletePreview] = useState<any>(null);
  const [isCreating, setIsCreating] = useState(false);
  const watchAll: IFormData = watch();

  const disableLimitedOffer = useMemo(() => {
    const allFieldsFilled = Object.keys(watchAll).every(
      (key) => key === "website" || !!watchAll[key as keyof IFormData]
    );

    const noErrors = Object.keys(errors).length === 0;
    const isSelected = selectOptionsPay === PayType.LIMITED_OFFER;

    return !(allFieldsFilled && noErrors && isSelected);
  }, [errors, watchAll, selectOptionsPay]);

  const disableStandardPricing = useMemo(() => {
    const allFieldsFilled = Object.keys(watchAll).every(
      (key) =>
        key === "website" ||
        key === "alreadyInstalled" ||
        key === "alreadyBacklink" ||
        !!watchAll[key as keyof IFormData]
    );

    const isErrors = Object.keys(errors).some(
      (key) => key !== "alreadyInstalled" && key !== "alreadyBacklink"
    );
    const isSelected = selectOptionsPay === PayType.STANDARD_PRICING;

    return !(allFieldsFilled && !isErrors && isSelected);
  }, [errors, watchAll, selectOptionsPay]);

  useEffect(() => {
    if (pair?.tokenBase?.address) {
      setValue("tokenAddress", pair.tokenBase.address);
    }
  }, [pair?.tokenBase?.address, setValue]);

  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [walletSelected, setWalletSelected] = useState<string>(
    wallets[0]?.address || ""
  );

  const infoWalletSelected = wallets.find(
    (item) => item.address === walletSelected
  );

  const isSufficientBalance = useMemo(() => {
    if (selectOptionsPay === PayType.LIMITED_OFFER) {
      return (
        Number(infoWalletSelected?.balance) <
        Number(config.verifyToken.limited.price)
      );
    }
    return (
      Number(infoWalletSelected?.balance) <
      Number(config.verifyToken.standard.price)
    );
  }, [infoWalletSelected, selectOptionsPay]);

  const onSubmit: SubmitHandler<IFormData> = async (data: IFormData) => {
    setIsCreating(true);
    try {
      if (data) {
        const formData = new FormData();

        formData.append("network", network);
        formData.append("type", VerifyType.TAKE_OVER);
        formData.append("tokenAddress", data.tokenAddress);
        formData.append("contact", `https://t.me/${data.telegramId}`);
        if (data.twitter) {
          formData.append("twitter", `https://x.com/${data.twitter}`);
        }
        formData.append("website", data.website);
        if (data.telegram) {
          formData.append("telegram", `https://t.me/${data.telegram}`);
        }
        formData.append("logoImage", data.logoImage as File);
        formData.append("bannerImage", data.bannerImage as File);
        formData.append("takeoverExplanation", data.takeoverExplanation || "");
        formData.append("payerAddress", walletSelected || "");
        formData.append("paymentType", selectOptionsPay);

        // const payload = {
        //   network,
        //   type: VerifyType.TAKE_OVER,
        //   tokenAddress: data.tokenAddress,
        //   contact: data.telegramId,
        //   twitter: data.twitter,
        //   website: data.website,
        //   telegram: data.telegram,
        //   takeoverExplanation: data.takeoverExplanation || '',
        //   paymentType: selectOptionsPay,
        // };

        const profile = await rf
          .getRequest("ExternalRequest")
          .postTokenProfile(formData);
        const paymentPayload = {
          amountIn:
            selectOptionsPay === PayType.LIMITED_OFFER
              ? config.verifyToken.limited.price.toString()
              : config.verifyToken.standard.price.toString(),
          fromWallet: walletSelected,
          metadata: { tokenProfileId: profile?.id },
          paymentCategory: "CREATE_TOKEN_PROFILE",
          toWallet: "",
          withdrawalType: "payment",
        };

        await rf
          .getRequest("WithdrawRequest")
          .transfer(network, paymentPayload);
      }
      toastSuccess(
        "Payment successful!",
        "You can view your application in My tickets"
      );
      handleResetForm();
      changeTab("list-verify");
    } catch (e: any) {
      toastError(
        "Payment failed!",
        e.message ||
          "Your payment was not successful. Please try again or contact support for further assistance"
      );
      console.error(e);
    } finally {
      setIsCreating(false);
    }
  };

  const handleResetForm = () => {
    clearErrors();
    setDeletePreview(Date.now());
    reset();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="flex gap-2">
        <div className="flex flex-col gap-3 p-4">
          <div className="text-white-1000 text-[12px] font-semibold">
            1. Token Info
          </div>
          <div className="flex flex-col gap-2">
            <InputField
              register={register}
              label="Token Address"
              placeholder="Enter your Token Address"
              name="tokenAddress"
              validation={{
                required: "Token Address is required",
                validate: (value) => {
                  if (typeof value === "string" && !isValidCAAddress(value)) {
                    return "Invalid token address";
                  }
                  return true;
                },
                onBlur: (e) => {
                  trigger("tokenAddress");
                },
              }}
            />
          </div>
          {errors.tokenAddress?.message && (
            <span className="text-[12px] text-red-500">
              {errors.tokenAddress.message.toString()}
            </span>
          )}

          <div className="flex flex-col gap-2">
            <InputField
              register={register}
              label="Contact information"
              name="telegramId"
              prefix="https://t.me/"
              placeholder="Enter your Telegram ID"
              validation={{
                required: "Telegram ID is required",
              }}
            />
            {errors.telegramId?.message && (
              <span className="text-[12px] text-red-500">
                {errors.telegramId.message.toString()}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-2">
            <span className="text-white-700 text-[12px] leading-[16px]">
              Takeover Explanation
              <span className="text-red-500">*</span>
            </span>
            <textarea
              {...register("takeoverExplanation", {
                required: "Takeover Explanation is required",
              })}
              className="border-white-100 placeholder:text-white-300 bg-white-50 body-sm-medium-12 flex min-h-[126px] w-full items-center rounded-[6px] border p-[8px] outline-none"
              placeholder="Explain within 160 characters"
            />
            {errors.takeoverExplanation?.message && (
              <span className="text-[12px] text-red-500">
                {errors.takeoverExplanation.message.toString()}
              </span>
            )}
          </div>
          <div className="flex gap-3">
            <div className="flex w-1/2 flex-col gap-2">
              <InputField
                register={register}
                label="Twitter"
                name="twitter"
                prefix="https://twitter.com/"
                validation={{
                  required: "Twitter is required",
                }}
              />
              {errors.twitter?.message && (
                <span className="text-[12px] text-red-500">
                  {errors.twitter.message.toString()}
                </span>
              )}
            </div>
            <div className="flex w-1/2 flex-col gap-2">
              <InputField
                register={register}
                label="Telegram"
                name="telegram"
                prefix="https://t.me/"
                validation={{
                  required: "Telegram is required",
                }}
              />
              {errors.telegram?.message && (
                <span className="text-[12px] text-red-500">
                  {errors.telegram.message.toString()}
                </span>
              )}
            </div>
          </div>

          <div className="mb-3 flex flex-col gap-2">
            <InputField
              register={register}
              label="Website"
              name="website"
              placeholder="Enter the new official websites link"
            />
          </div>

          <UploadSection
            deletePreview={deletePreview}
            trigger={trigger}
            setError={setError}
            register={register}
            reset={reset}
            errors={errors}
            onLogoChange={(file) => {
              setValue("logoImage", file);
            }}
            onBannerChange={(file) => {
              setValue("bannerImage", file);
            }}
          />
          <AgreementCheckbox
            name="agreeDataProvided"
            label="I agree that all the data provided above can be verified through other channels, and the information I submit will be publicly displayed."
            register={register}
            validation={{
              required: "You must agree to continue",
            }}
          />
          {errors.agreeDataProvided?.message && (
            <span className="text-[12px] text-red-500">
              {errors.agreeDataProvided.message.toString()}
            </span>
          )}
          <AgreementCheckbox
            name="agreeReview"
            label="I agree to RaidenX’s review methods and standards, and accept that RaidenX reserves the right to reject or modify the data."
            register={register}
            validation={{
              required: "You must agree to continue",
            }}
          />
          {errors.agreeReview?.message && (
            <span className="text-[12px] text-red-500">
              {errors.agreeReview.message.toString()}
            </span>
          )}
          <AgreementCheckbox
            name="agreePayment"
            label="By completing the payment, I confirm that I've read and agree that there is no refund in all circumstances."
            register={register}
            validation={{
              required: "You must agree to continue",
            }}
          />
          {errors.agreePayment?.message && (
            <span className="text-[12px] text-red-500">
              {errors.agreePayment.message.toString()}
            </span>
          )}
          <div className="text-white-1000 mt-2 text-[12px] font-semibold">
            2. Payment Method
          </div>
          <div>
            <div className="text-white-700 mb-2 text-[12px] leading-[16px]">
              Choose wallet
            </div>

            <SelectedWalletPayment
              walletAddressSelected={walletSelected || ""}
              wallets={wallets}
              onSelect={setWalletSelected}
              isOnlySelect
            />
            {isSufficientBalance && (
              <div className="mt-2 text-[12px] text-red-500">
                Insufficient balance. Please select a different wallet.
              </div>
            )}
          </div>

          <div className="flex items-center gap-3">
            <div
              onClick={() => setSelectOptionsPay(PayType.LIMITED_OFFER)}
              className={`rounded-8 flex h-[200px] w-[380px] cursor-pointer flex-col gap-4 p-3 ${
                selectOptionsPay === PayType.LIMITED_OFFER
                  ? "border-white-700 border "
                  : ""
              }`}
              style={{
                background:
                  "linear-gradient(180deg, rgba(246, 139, 30, 0.10) 0%, rgba(144, 81, 18, 0.10) 100%)",
              }}
            >
              <div className="rounded-6 flex h-[28px] w-[115px] cursor-pointer items-center justify-center gap-1 !bg-[rgba(246,139,30,0.10)] text-[12px] font-semibold text-orange-500 hover:!bg-orange-700">
                <LimitedOfferIcon />
                Limited Offer
              </div>
              <div className="flex flex-col">
                <AgreementCheckbox
                  name="alreadyInstalled"
                  className="mb-1 !w-full !items-start"
                  label={
                    <div>
                      I have installed{" "}
                      <span>
                        <a
                          href="https://t.me/raidenx"
                          target="_blank"
                          className="underline"
                          onClick={(e) => e.stopPropagation()}
                        >
                          https://t.me/raidenx
                        </a>
                      </span>{" "}
                      to the project&apos;s telegram group
                    </div>
                  }
                  register={register}
                  validation={{
                    required:
                      selectOptionsPay === PayType.LIMITED_OFFER
                        ? "You must agree to continue"
                        : false,
                  }}
                />
                <AgreementCheckbox
                  name="alreadyBacklink"
                  className="mb-1 !w-full !items-start"
                  label={
                    <div>
                      I have included RaidenX logo and backlink into the
                      project&apos;s website with regard to RaidenX{" "}
                      <span>
                        <a
                          href="https://t.me/raidenx"
                          target="_blank"
                          className="underline"
                          onClick={(e) => e.stopPropagation()}
                        >
                          media guideline
                        </a>
                      </span>{" "}
                    </div>
                  }
                  register={register}
                  validation={{
                    required:
                      selectOptionsPay === PayType.LIMITED_OFFER
                        ? "You must agree to continue"
                        : false,
                  }}
                />
              </div>

              <div className="flex items-center gap-3">
                <button
                  type="submit"
                  disabled={
                    disableLimitedOffer || isSufficientBalance || isCreating
                  }
                >
                  <AppButton
                    className="h-[40px] w-[60%] min-w-[225px] text-[14px] font-[500]"
                    disabled={
                      disableLimitedOffer || isSufficientBalance || isCreating
                    }
                    onClick={handleSubmit(onSubmit)}
                  >
                    Pay 20 SUI
                    <AppLogoNetwork
                      network={network}
                      isBase
                      className="ml-[4px] h-[20px] w-[20px]"
                    />
                  </AppButton>
                </button>
                <div className="p-2 text-[12px] font-semibold text-orange-500">
                  50% OFF: <span className="line-through">40 SUI</span>
                </div>
              </div>
            </div>

            <div
              onClick={() => setSelectOptionsPay(PayType.STANDARD_PRICING)}
              className={`rounded-8 bg-white-50 flex h-[200px] w-[380px] cursor-pointer flex-col justify-between gap-4 p-3 ${
                selectOptionsPay === PayType.STANDARD_PRICING
                  ? "border-white-700 border "
                  : ""
              }`}
            >
              <div className="flex flex-col gap-4">
                <AppButton
                  className="h-[28px] max-w-[138px]"
                  variant="secondary"
                >
                  <StandardIcon />
                  Standard Pricing
                </AppButton>

                <div className="text-white-800 text-[12px] leading-[18px]">
                  Prefer convenience? Pay the regular price with no extra steps.
                </div>
              </div>
              <button
                type="submit"
                disabled={
                  disableStandardPricing || isSufficientBalance || isCreating
                }
              >
                <AppButton
                  className="h-[40px] w-full min-w-[225px] text-[14px] font-[500]"
                  disabled={
                    disableStandardPricing || isSufficientBalance || isCreating
                  }
                  onClick={handleSubmit(onSubmit)}
                >
                  Pay 40 SUI{" "}
                  <AppLogoNetwork
                    network={network}
                    isBase
                    className="ml-[4px] h-[20px] w-[20px]"
                  />
                </AppButton>
              </button>
            </div>
          </div>

          <div className="text-white-500 mt-3 text-[12px]">
            ETA: Submission will be verified by RaidenX. Average processing time
            after receiving payment is less than 1 hour.
          </div>
        </div>
        <div className="flex gap-2">
          <div className="bg-white-50 rounded-10 mt-4 h-max w-[380px] p-3">
            <div className="text-white-1000 border-white-100 flex items-center gap-2 border-b border-dashed py-2 text-start text-[14px] font-semibold leading-[1.5]">
              <PreviewVerifyIcon />
              Preview
            </div>
            <PreviewToken previewData={watchAll} />
          </div>
        </div>
      </div>
    </form>
  );
};

export default CommunityTakeover;

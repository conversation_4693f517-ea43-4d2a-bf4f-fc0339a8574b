import React, { useEffect, useRef, useState } from "react";
import {
  FilterIcon,
  ReloadIcon,
  FlashIcon,
  SettingsIcon,
} from "@/assets/icons";
import { AppPopover, AppButton, AppSwapUnit } from "@/components";
import CustomCheckbox from "@/app/advertising/components/CustomCheckbox";
import { AppLogoNetwork } from "@/components/AppLogoNetwork";
import rf from "@/services/RequestFactory";
import { AppNumber } from "../AppNumber";
import { TTransaction } from "@/types/wallet-tracker";
import { filterParams } from "@/utils/helper";
import { NumericFormat } from "react-number-format";
import { TGroup } from "@/types/wallet-tracker";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { useOrder } from "@/hooks/useOrder";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { ModalLiveTradesTableSettings } from "@/modals";
import { AppTimeDisplay } from "../AppTimeDisplay";
import { AppAvatarToken } from "../AppAvatarToken";
import { DEXS, getDexLogoUrl } from "@/utils/dex";
import { getDefaultUseAggregator } from "@/utils/dex";
import { convertMistToDec } from "@/utils/helper";
import Link from "next/link";
import { getLinkTxExplorer } from "@/utils/helper";
import Tooltip from "rc-tooltip";
import { convertDecToMist } from "@/utils/helper";
import { SUI_DECIMALS } from "@/utils/contants";
import { multipliedBN } from "@/utils/helper";
import TooltipWalletName from "./TooltipWalletName";
import { dividedBN } from "@/utils/helper";
import { useNetwork } from "@/context";

const UNIT_TYPE = {
  USD: "USD",
  TOKEN: "TOKEN",
};

const FilterAmount = ({
  setTradingType,
  setMinQuoteAmount,
  priceUnit,
}: {
  priceUnit: string;
  setTradingType: (value: string) => void;
  setMinQuoteAmount: (value: {
    minQuoteAmount?: string;
    minTotalUsd?: string;
  }) => void;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [excludeBuys, setExcludeBuys] = useState<boolean>(false);
  const [excludeSells, setExcludeSells] = useState<boolean>(false);
  const [min, setMin] = useState<string>("");
  const { currentNetwork } = useNetwork();

  const onReset = () => {
    setMin("");
    setExcludeBuys(false);
    setExcludeSells(false);
    setMinQuoteAmount({});
    setTradingType("");
    setIsOpen(false);
  };

  const onFilter = () => {
    if (priceUnit === UNIT_TYPE.TOKEN) {
      setMinQuoteAmount({
        minQuoteAmount: min ? convertDecToMist(min, SUI_DECIMALS) : "",
      });
    } else {
      setMinQuoteAmount({
        minTotalUsd: min ? convertDecToMist(min, SUI_DECIMALS) : "",
      });
    }

    if ((excludeSells && excludeBuys) || (!excludeSells && !excludeBuys)) {
      setTradingType("");
      setIsOpen(false);
      return;
    }

    if (excludeSells) {
      setTradingType("SELL");
      setIsOpen(false);
      return;
    }

    if (excludeBuys) {
      setTradingType("BUY");
      setIsOpen(false);
      return;
    }
  };

  return (
    <AppPopover
      position="left"
      isOpen={isOpen}
      onToggle={() => {
        setIsOpen(!isOpen);
      }}
      onClose={() => setIsOpen(false)}
      trigger={
        <div className="hover:text-white-1000 cursor-pointer">
          <FilterIcon />
        </div>
      }
      content={
        <div className="bg-black-900 border-white-100 w-[266px] rounded-[8px] border p-[12px]">
          <div className="mb-6">
            <div className="mb-4 flex items-center gap-[18px]">
              <div className="body-md-regular-14 text-white-1000">
                Min {priceUnit === UNIT_TYPE.TOKEN ? "SUI" : "Total"}
              </div>
              <div className="border-white-100 flex items-center gap-2 rounded-[6px] border p-2">
                <NumericFormat
                  value={min}
                  onChange={(e) => {
                    setMin(e.target.value.replace(/,/g, ""));
                  }}
                  thousandSeparator=","
                  valueIsNumericString
                  placeholder="0.0"
                  decimalScale={6}
                  className="action-sm-medium-12 text-white-1000 placeholder:text-white-300 w-[130px] bg-transparent outline-none"
                />
                {priceUnit === UNIT_TYPE.TOKEN ? (
                  <AppLogoNetwork network={currentNetwork} isBase />
                ) : (
                  <div className="body-sm-regular-12">$</div>
                )}
              </div>
            </div>

            <CustomCheckbox
              checked={excludeSells}
              onChange={setExcludeSells}
              label="Exclude Sells"
              className="mb-4"
            />

            <CustomCheckbox
              checked={excludeBuys}
              onChange={setExcludeBuys}
              label="Exclude Buys"
            />
          </div>
          <div className="flex items-center gap-[12px]">
            <div
              onClick={onReset}
              className="bg-white-100 text-white-1000 flex h-[24px] w-[24px] cursor-pointer items-center justify-center rounded p-[6px]"
            >
              <ReloadIcon />
            </div>
            <AppButton
              onClick={onFilter}
              variant="buy"
              className="!h-[24px] w-full !rounded !px-2 !py-1 !text-[10px] !font-medium !leading-[14px]"
            >
              Apply Filter
            </AppButton>
          </div>
        </div>
      }
    />
  );
};

const FilterMC = ({
  setMaxMarketCap,
  setMinMarketCap,
}: {
  setMaxMarketCap: (value: string) => void;
  setMinMarketCap: (value: string) => void;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [min, setMin] = useState<string>("");
  const [max, setMax] = useState<string>("");

  const onReset = () => {
    setMin("");
    setMax("");
    setMinMarketCap("");
    setMaxMarketCap("");
    setIsOpen(false);
  };

  const onFilter = () => {
    setMinMarketCap(min);
    setMaxMarketCap(max);
    setIsOpen(false);
  };

  return (
    <AppPopover
      position="left"
      isOpen={isOpen}
      onToggle={() => {
        setIsOpen(!isOpen);
      }}
      onClose={() => setIsOpen(false)}
      trigger={
        <div className="hover:text-white-1000 cursor-pointer">
          <FilterIcon />
        </div>
      }
      content={
        <div className="bg-black-900 border-white-100 w-[266px] rounded-[8px] border p-2">
          <div className="mb-[32px]">
            <div className="flex items-center gap-[12px]">
              <div className="text-white-1000">MC</div>
              <div className="border-white-100 flex items-center gap-2 rounded-[6px] border p-2">
                <NumericFormat
                  value={min}
                  onChange={(e) => {
                    setMin(e.target.value.replace(/,/g, ""));
                  }}
                  thousandSeparator=","
                  valueIsNumericString
                  placeholder="Min"
                  decimalScale={6}
                  className="action-sm-medium-12 text-white-1000 placeholder:text-white-300 w-full  bg-transparent outline-none"
                />
                $
              </div>
              <div className="border-white-100 flex items-center gap-2 rounded-[6px] border p-2">
                <NumericFormat
                  value={max}
                  onChange={(e) => {
                    setMax(e.target.value.replace(/,/g, ""));
                  }}
                  thousandSeparator=","
                  valueIsNumericString
                  placeholder="Max"
                  decimalScale={6}
                  className="action-sm-medium-12 text-white-1000 placeholder:text-white-300 w-full bg-transparent outline-none"
                />
                $
              </div>
            </div>
          </div>
          <div className="flex items-center gap-[12px]">
            <div
              onClick={onReset}
              className="bg-white-100 text-white-1000 flex h-[24px] w-[24px] cursor-pointer items-center justify-center rounded p-[6px]"
            >
              <ReloadIcon />
            </div>
            <AppButton
              onClick={onFilter}
              variant="buy"
              className="!h-[24px] w-full !rounded !px-2 !py-1 !text-[10px] !font-medium !leading-[14px]"
            >
              Apply Filter
            </AppButton>
          </div>
        </div>
      }
    />
  );
};

const LiveTrades = ({ groups, size }: { size: any; groups: TGroup[] }) => {
  const [transactions, setTransactions] = useState<TTransaction[]>([]);
  const [tradingType, setTradingType] = useState<string>("");
  const [minQuoteAmount, setMinQuoteAmount] = useState<{
    minQuoteAmount?: string;
    minTotalUsd?: string;
  }>({});
  const [minMarketCap, setMinMarketCap] = useState<string>("");
  const [maxMarketCap, setMaxMarketCap] = useState<string>("");
  const transactionsRef = useRef<any>(transactions);
  const [newItemIndex, setNewItemIndex] = useState<number | null>(null);
  const [settingsTable, setSettingsTable] = useState<string[]>([
    "name",
    "token",
    "amount",
    "marketCap",
  ]);
  const [isShowModalSettingsTable, setIsShowModalSettingsTable] =
    useState<boolean>(false);
  const [priceUnit, setPriceUnit] = useState<string>(UNIT_TYPE.TOKEN);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const { wallets } = useSelector(
    (state: RootState) => state.user.walletTracker
  );

  const { quickBuy } = useOrder();
  const settingsQuickOrder = useSelector(
    (state: RootState) => state.user.settingsQuickOrder
  );
  const { currentNetwork } = useNetwork();

  const handleWhenMakerTrade = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    transactionsRef.current = [data, ...transactionsRef.current];
    setTransactions(transactionsRef.current);
    setNewItemIndex(0);
    setTimeout(() => {
      setNewItemIndex(null);
    }, 1000);
  };

  useEffect(() => {
    AppBroadcast.on(
      BROADCAST_EVENTS.MAKER_TRADE_SUCCEEDED,
      handleWhenMakerTrade
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.MAKER_TRADE_SUCCEEDED,
        handleWhenMakerTrade
      );
    };
  }, []);

  const getTransactions = async () => {
    if (!groups?.length) return;
    const groupIds = groups?.map((g) => g.id);
    try {
      const res = await rf.getRequest("WalletTrackerRequest").getTransactions(
        filterParams({
          groupIds: groupIds.join(","),
          tradingType,
          minMarketCap,
          maxMarketCap,
          ...minQuoteAmount,
        })
      );
      setTransactions(res);
      transactionsRef.current = res;
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    getTransactions().then();
  }, [tradingType, minQuoteAmount, groups, minMarketCap, maxMarketCap]);

  const getGridTemplateColumns = (settingsTable: string[]) => {
    const firstColWidth = "50px"; // cột setting cố định
    const totalCols = settingsTable.length + 1; // +1 là cột settings icon
    const otherColsWidth = "minmax(80px, 1fr)";
    return [firstColWidth, ...Array(totalCols - 1).fill(otherColsWidth)].join(
      " "
    );
  };

  const gridTemplateColumns = getGridTemplateColumns(settingsTable);

  return (
    <div className="w-full overflow-x-auto">
      <div className="min-w-[620px]">
        <div
          className="border-white-50 text-white-500 grid h-[36px] items-center border-b text-[12px] font-normal leading-[18px]"
          style={{
            gridTemplateColumns,
          }}
        >
          <div
            onClick={() => setIsShowModalSettingsTable(true)}
            className="hover:text-white-1000 flex cursor-pointer items-center justify-center px-2"
          >
            <Tooltip overlay={`Live Trades Table Settings`} placement="right">
              <SettingsIcon />
            </Tooltip>
          </div>
          {settingsTable.includes("status") && (
            <div className="px-2 py-[6px]">Status</div>
          )}
          {settingsTable.includes("name") && (
            <div className="px-2 py-[6px]">Name</div>
          )}
          {settingsTable.includes("token") && (
            <div className="px-2 py-[6px]">Token</div>
          )}
          {settingsTable.includes("amount") && (
            <div className="flex items-center gap-2 px-2 py-[6px]">
              <div>Amount</div>
              <AppSwapUnit unit={priceUnit} setUnit={setPriceUnit} />
              <FilterAmount
                priceUnit={priceUnit}
                setTradingType={setTradingType}
                setMinQuoteAmount={setMinQuoteAmount}
              />
            </div>
          )}
          {settingsTable.includes("marketCap") && (
            <div className="flex items-center gap-2 px-2 py-[6px]">
              <div>$MC</div>
              <FilterMC
                setMinMarketCap={setMinMarketCap}
                setMaxMarketCap={setMaxMarketCap}
              />
            </div>
          )}
          {settingsTable.includes("AvgBuy") && (
            <div className="px-2 py-[6px]">Avg Buy</div>
          )}
          {settingsTable.includes("AvgSell") && (
            <div className="px-2 py-[6px]">Avg Sell</div>
          )}
        </div>

        {/* BODY */}
        <div
          className="customer-scroll overflow-y-auto"
          style={{ height: size.height - 90 }}
        >
          {transactions.length ? (
            transactions.map((item: TTransaction, index) => {
              const dexKey = (item?.pair?.dex?.dex ||
                item?.pair?.dex ||
                "") as keyof typeof DEXS;
              const dexLogoUrl = getDexLogoUrl(dexKey);

              const isNewItem = newItemIndex === index;

              const wallet = wallets.find(
                (w) => w.walletAddress === item.walletAddress
              );

              const handleQuickBuy = () => {
                if (!accessToken) {
                  return;
                }
                quickBuy(
                  null,
                  item.pair,
                  settingsQuickOrder?.buyQuickAmount,
                  SUI_TOKEN_ADDRESS_FULL,
                  getDefaultUseAggregator(
                    (item.pair?.dex?.dex ||
                      item?.pair?.dex) as keyof typeof DEXS
                  )
                ).then();
              };

              return (
                <div
                  key={index}
                  className={`hover:bg-white-50 group relative flex grid cursor-pointer items-center text-[12px] ${
                    item.tradingType === "SELL"
                      ? "text-red-500"
                      : "text-green-500"
                  } ${isNewItem ? "animate-new-transaction" : ""}`}
                  style={{
                    gridTemplateColumns,
                  }}
                >
                  <div className="text-white-500 flex items-center justify-center px-2 underline">
                    <Tooltip overlay={`Open in Suivision`} placement="right">
                      <Link
                        href={getLinkTxExplorer(currentNetwork, item.hash)}
                        target="_blank"
                      >
                        <AppTimeDisplay
                          timestamp={item.timestamp}
                          isAgo
                          suffix=""
                        />
                      </Link>
                    </Tooltip>
                  </div>
                  {settingsTable.includes("status") && (
                    <div className="px-2 py-[10px]">{item.status}</div>
                  )}
                  {settingsTable.includes("name") && (
                    <TooltipWalletName transaction={item} wallet={wallet} />
                  )}
                  {settingsTable.includes("token") && (
                    <div className="text-white-500 flex items-center gap-2 px-2 py-[10px]">
                      <Link
                        href={`/${item?.pair?.network}/${item?.pair?.slug}`}
                        className="group flex items-center gap-2"
                      >
                        <div className="relative">
                          <AppAvatarToken
                            image={item.imageToken || item.iconUrl}
                            size={28}
                          />
                          {item?.pair?.dex && (
                            <div
                              className="absolute bottom-[-4px] right-[-4px] rounded-full p-[2px] "
                              style={{
                                background: "rgba(8, 9, 12, 0.80)",
                              }}
                            >
                              <img
                                src={dexLogoUrl}
                                className="h-[12px] w-[12px]"
                                alt={`${item?.pair?.dex?.dex} logo`}
                              />
                            </div>
                          )}
                        </div>
                        <div className="hover:underline">
                          {item.tokenSymbol}
                        </div>
                      </Link>
                    </div>
                  )}
                  {settingsTable.includes("amount") && (
                    <div className="flex items-center gap-1 px-2 py-[10px]">
                      {priceUnit === UNIT_TYPE.TOKEN ? (
                        <>
                          <AppLogoNetwork network={currentNetwork} isBase />
                          <AppNumber
                            value={convertMistToDec(item.amount, SUI_DECIMALS)}
                          />
                        </>
                      ) : (
                        <AppNumber
                          isForUSD
                          value={convertMistToDec(
                            multipliedBN(
                              dividedBN(item.amount, item.price),
                              item.priceUsd
                            ),
                            SUI_DECIMALS
                          )}
                        />
                      )}
                    </div>
                  )}
                  {settingsTable.includes("marketCap") && (
                    <div className="text-white-500 px-2 py-[10px]">
                      <AppNumber value={item?.pair?.marketCapUsd} />
                    </div>
                  )}
                  {settingsTable.includes("AvgBuy") && (
                    <div className="text-white-500 px-2 py-[10px]">
                      <AppNumber value={item?.avgBuy} />
                    </div>
                  )}
                  {settingsTable.includes("AvgSell") && (
                    <div className="text-white-500 px-2 py-[10px]">
                      <AppNumber value={item?.avgSell} />
                    </div>
                  )}

                  <div className="tablet:hidden absolute right-2 block w-[30px] group-hover:block">
                    <AppButton
                      onClick={handleQuickBuy}
                      size="small"
                      variant="buy"
                    >
                      <FlashIcon />
                    </AppButton>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="body-md-regular-14 flex h-full items-center justify-center">
              No transactions yet.
            </div>
          )}
        </div>
      </div>

      {isShowModalSettingsTable && (
        <ModalLiveTradesTableSettings
          isOpen={isShowModalSettingsTable}
          onClose={() => setIsShowModalSettingsTable(false)}
          setSettingsTable={setSettingsTable}
          settingsTable={settingsTable}
        />
      )}
    </div>
  );
};

export default LiveTrades;

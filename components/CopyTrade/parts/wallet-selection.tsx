import _ from "lodash";
import { default as React, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import {
  CheckboxIcon,
  CheckedIcon,
  CloseIcon,
  SearchIcon,
  SettingsIcon,
} from "@/assets/icons";
import {
  AppButtonSort,
  AppCopy,
  AppLogoNetwork,
  AppNumber,
} from "@/components";
import { useRaidenxWallet } from "@/hooks";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import Storage from "@/libs/storage";
import { toastError } from "@/libs/toast";
import { ROUTE_PATH } from "@/routes";
import { RootState } from "@/store";
import { TWallet } from "@/types";
import Link from "next/link";

export const WalletSelection = ({
  onCloseWalletSettings,
}: {
  onCloseWalletSettings: () => void;
}) => {
  const [search, setSearch] = useState<string>("");
  const [walletsUser, setWalletsUser] = useState<any[]>([]);

  const [sortBy, setSortBy] = useState<string>("balance");
  const [sortType, setSortType] = useState<string>("desc");

  const { wallets, network } = useSelector((state: RootState) => state.user);

  const { activeWalletAddresses } = useRaidenxWallet();
  const [walletsSelected, setWalletsSelected] = useState<string[]>([]);
  useEffect(() => {
    setWalletsSelected(activeWalletAddresses);
  }, []);

  useEffect(() => {
    let dataWallet = wallets;
    if (search) {
      dataWallet = dataWallet.filter((item) =>
        item.address?.toLowerCase().includes(search?.toLowerCase())
      );
    }

    if (sortType === "desc") {
      dataWallet = _.orderBy(dataWallet, [sortBy], ["desc"]);
    }

    if (sortType === "asc") {
      dataWallet = _.orderBy(dataWallet, [sortBy], ["asc"]);
    }

    setWalletsUser(dataWallet);
  }, [search, wallets, sortType, sortBy]);

  const objWalletSelected = wallets.find(
    (item) => item.address === walletsSelected[0]
  );

  const onSelectWallet = (wallet: TWallet) => {
    const data = walletsSelected.includes(wallet.address)
      ? []
      : [wallet.address];
    setWalletsSelected(data);
  };

  const onSelect = () => {
    if (!walletsSelected.length) {
      toastError("Error", "Please select wallet!");
      return;
    }
    AppBroadcast.dispatch(BROADCAST_EVENTS.WALLETS_SELECTED, {
      walletsSelected: walletsSelected,
    });

    Storage.setWalletAddresses(walletsSelected || "");
    onCloseWalletSettings();
  };

  return (
    <div className="w-full">
      <div className="flex justify-between">
        <div className="body-md-medium-14 flex gap-2">
          <div>Wallet Selection</div>
        </div>

        <div className="body-md-medium-14 flex items-center gap-2">
          <div className="border-white-100 flex items-center gap-1 border-b pb-[2px]">
            <SearchIcon />
            <input
              className="body-sm-regular-12 w-[70px] truncate bg-transparent outline-none"
              placeholder="Search"
              value={search}
              onChange={(e) => {
                setSearch(e.target.value);
              }}
            />
          </div>
          <div
            className="text-neutral-alpha-500 hover:text-neutral-alpha-1000 cursor-pointer"
            onClick={onCloseWalletSettings}
          >
            <CloseIcon className="h-[12px] w-[12px]" />
          </div>
        </div>
      </div>

      <div className="mt-[12px]">
        <div className="body-xs-medium-10 text-white-500 mx-[8px] grid grid-cols-2 gap-2">
          <div className="flex items-center gap-1"></div>
          <div className="flex items-center justify-center gap-2">
            <AppButtonSort
              value="balance"
              setSortBy={setSortBy}
              sortBy={sortBy}
              setSortType={setSortType}
              sortType={sortType}
            />
          </div>
        </div>

        <div className="customer-scroll max-h-[200px] overflow-auto">
          {walletsUser.map((item, index) => {
            return (
              <div
                key={index}
                className="text-white-1000 body-sm-regular-12 border-white-50 grid grid-cols-2 gap-2 border-b border-dashed px-[8px] py-[12px]"
              >
                <div className="flex items-center gap-1">
                  <div
                    onClick={() => onSelectWallet(item)}
                    className="cursor-pointer"
                  >
                    {walletsSelected.includes(item.address) ? (
                      <CheckedIcon />
                    ) : (
                      <CheckboxIcon />
                    )}
                  </div>

                  <div>
                    {item.address.slice(
                      item.address.length - 6,
                      item.address.length
                    )}
                  </div>
                  <AppCopy
                    message={item.address}
                    className="text-white-600 hover:text-white-1000 h-[12px] w-[12px]"
                  />
                </div>
                <div className="text-center">
                  <AppNumber value={item?.balance} />
                </div>
              </div>
            );
          })}
        </div>

        <div className="border-white-50 mt-[12px] border-t pt-[16px]">
          <div className="mb-[8px] flex justify-between">
            <div className="body-xs-regular-10">Total SUI</div>
            <div className="body-sm-medium-12 tex-white-1000 flex items-center gap-1">
              <AppNumber value={objWalletSelected?.balance} />

              <AppLogoNetwork
                network={network}
                isBase
                className="h-[12px] w-[12px]"
              />
            </div>
          </div>
        </div>

        <div
          onClick={onSelect}
          className="text-neutral-beta-900 action-sm-medium-14 bg-neutral-alpha-1000 mt-2 flex cursor-pointer items-center justify-center gap-1 rounded-[6px] px-2 py-[10px]"
        >
          Select Wallet
        </div>

        <Link
          href={ROUTE_PATH.WALLET_MANAGER}
          className="body-sm-regular-12 mt-2 flex cursor-pointer justify-center gap-1"
        >
          <SettingsIcon />
          Manage Wallets
        </Link>
      </div>
    </div>
  );
};

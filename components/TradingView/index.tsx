"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import {
  widget,
  ChartingLibraryWidgetOptions,
  ResolutionString,
  IBasicDataFeed,
  ResolveCallback,
  LibrarySymbolInfo,
  ThemeName,
  PeriodParams,
  HistoryCallback,
  Bar,
  SubscribeBarsCallback,
  Mark,
  GetMarksCallback,
  PricedPoint,
  TradingTerminalWidgetOptions,
  IOrderLineAdapter,
} from "@/libs/charting_library";
import * as React from "react";
import { CANDLE_TYPE, CANDLE_UNIT, SYMBOL_TYPE } from "./consts";
import { TCandle, TOrder, TPair, TPairTransaction, TWallet } from "@/types";
import rf from "@/services/RequestFactory";
import { getChartDescription, getSymbolFromPair } from "./utils/symbol";
import {
  DATAFEED_CONFIGURATION,
  getClientTimezone,
  getDisabledFeatures,
  getEnabledFeatures,
  TChartSetting,
} from "./utils/setting";
// import { NavBar } from './parts/Navbar';
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import {
  convertResolutionString2Unit,
  getVolume,
  isInvalidCandle,
  mappingTradeToLastCandle,
} from "./utils/candle";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  formatNumber,
  formatQuotePriceNumber,
  formatUnixTimestamp,
  formatUsdNumber,
} from "@/utils/format";
import {
  buildDisplayOptionsParams,
  createAvgLineButton,
  createDisplayOptionsDropdown,
  createSwitchPriceOrMcapButton,
  createSwitchPriceType,
  getTitle,
} from "./utils/override";
import { priceFormatterFactory } from "./utils/override";
import { TradingType } from "@/enums";
import {
  dividedBN,
  emojiToBase64ImageIcon,
  multipliedBN,
} from "@/utils/helper";
import { getCirculatingSupply } from "@/utils/pair";
import Storage from "@/libs/storage";
import { useMediaQuery } from "react-responsive";
import { usePathname } from "next/navigation";
import retry from "async-retry";
import BigNumber from "bignumber.js";
import { toastError, toastSuccess } from "@/libs/toast";
import { usePairPrice } from "@/hooks/usePairPrice";
export interface ChartContainerProps {
  symbol: ChartingLibraryWidgetOptions["symbol"];
  interval: ChartingLibraryWidgetOptions["interval"];
  libraryPath: ChartingLibraryWidgetOptions["library_path"];
  chartsStorageApiVersion: ChartingLibraryWidgetOptions["charts_storage_api_version"];
  clientId: ChartingLibraryWidgetOptions["client_id"];
  userId: ChartingLibraryWidgetOptions["user_id"];
  fullscreen: ChartingLibraryWidgetOptions["fullscreen"];
  autosize: ChartingLibraryWidgetOptions["autosize"];
  container: ChartingLibraryWidgetOptions["container"];
}

type TradingViewProps = {
  pair: TPair;
  device?: string;
};

type TDisplayOptions = {
  MY_TRADES: boolean;
  ORDERS: boolean;
  DEV_TRADES: boolean;
  TRACKED: boolean;
};

const DEFAULT_RESOLUTION_STRING = "1S" as ResolutionString;
const CHART_PROPERTIES_KEY = "tradingview.chartproperties";

export const TradingView = React.memo((props: TradingViewProps) => {
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const [isChartHeaderReady, setIsChartHeaderReady] = useState(false);
  const [forceReset, setForceReset] = useState(0);
  const [isChartReady, setIsChartReady] = useState(false);
  const tradingViewChart = useRef<any>(null);
  const lastCandleRef = useRef<Bar>({} as Bar);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const openOrders = useSelector((state: RootState) => state.user.openOrders);
  const positions = useSelector((state: RootState) => state.user.positions);
  const { pairPrice } = usePairPrice(props.pair);
  const walletTrackers = useSelector(
    (state: RootState) => state.user.walletTracker
  );
  const walletTrackedAddressesRef = useRef<string[]>([]);
  const walletTrackersRef = useRef(walletTrackers);

  const pathname = usePathname();
  const isHomePage = pathname === "/";

  const userSettings = Storage.getUserSettings();

  const avgLineButtonRef = useRef<any>(null);
  const [showAvgPriceLine, setShowAvgPriceLine] = useState(
    userSettings.showAvgPriceLine
  );
  const showAvgPriceLineRef = useRef<any>(showAvgPriceLine);

  const walletsRef = useRef<any>(null);

  const avgPriceShape = useRef<any>(null);
  const openOrderLines = useRef<any>(null);

  const selectedMakerAddress = useRef<string>("");
  const [displayOptions, setDisplayOptions] = useState<TDisplayOptions>({
    MY_TRADES: true,
    ORDERS: true,
    DEV_TRADES: true,
    TRACKED: true,
  });
  const displayOptionsRef = useRef<any>(displayOptions);
  const displayOptionsButtonRef = useRef<any>(null);

  const [chartSetting, setChartSetting] = useState<TChartSetting>(
    userSettings.chartSetting || {
      type: CANDLE_TYPE.PRICE,
      unit: CANDLE_UNIT.USD,
    }
  );

  const chartSettingRef = useRef<any>(null);
  const onTickRef = useRef<SubscribeBarsCallback>({} as SubscribeBarsCallback);
  const resolutionStringRef = useRef<ResolutionString>(
    DEFAULT_RESOLUTION_STRING
  );

  const chartContainerRef = useRef<HTMLDivElement>(
    null
  ) as React.MutableRefObject<HTMLInputElement>;

  const defaultProps: Omit<ChartContainerProps, "container"> = {
    symbol: "ETHUSDT",
    interval: DEFAULT_RESOLUTION_STRING,
    libraryPath: "/charting_library/",
    chartsStorageApiVersion: "1.1",
    clientId: "tradingview.com",
    userId: "public_user_id",
    fullscreen: false,
    autosize: false,
  };

  const handleFilterMarker = useCallback(
    (event: TBroadcastEvent) => {
      const makerAddress = event.detail.makerAddress;
      selectedMakerAddress.current = makerAddress;
      if (isChartReady && tradingViewChart?.current) {
        getActiveChart()?.clearMarks();
        getActiveChart()?.refreshMarks();
      }
    },
    [isChartReady]
  );

  useEffect(() => {
    walletTrackersRef.current = walletTrackers;
    walletTrackedAddressesRef.current = walletTrackers?.wallets?.map(
      (wallet) => wallet.walletAddress
    );
  }, [walletTrackers]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const address = params.get("makerAddress");
    selectedMakerAddress.current = address || "";
    if (isChartReady && tradingViewChart?.current) {
      getActiveChart()?.refreshMarks();
    }
    AppBroadcast.on(BROADCAST_EVENTS.FILTER_MAKER_IN_CHART, handleFilterMarker);
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.FILTER_MAKER_IN_CHART,
        handleFilterMarker
      );
    };
  }, [isChartReady, handleFilterMarker]);

  useEffect(() => {
    chartSettingRef.current = chartSetting;
    Storage.setUserSettings("chartSetting", chartSetting);
  }, [chartSetting]);

  useEffect(() => {
    walletsRef.current = wallets;
  }, [wallets]);

  useEffect(() => {
    if (isHomePage) return;
    document.title = getTitle(props?.pair, pairPrice, chartSetting);
  }, [props?.pair, pairPrice, chartSetting, isHomePage]);

  useEffect(() => {
    setIsChartReady(false);
    setIsChartHeaderReady(false);
  }, [props?.pair?.pairId]);

  const getActiveChart = () => {
    try {
      return tradingViewChart.current?.activeChart();
    } catch (e) {
      console.error("getActiveChart", e);
      return null;
    }
  };

  const updateStateForAvgLinePriceLine = useCallback(() => {
    if (!tradingViewChart.current) return;
    const textUpdate = showAvgPriceLineRef?.current
      ? "Hide Avg Price Line"
      : "Show Avg Price Line";
    if (avgLineButtonRef.current) {
      avgLineButtonRef.current.textContent = textUpdate;
    }
    if (avgPriceShape.current && tradingViewChart.current) {
      getActiveChart()?.removeEntity(avgPriceShape.current);
      avgPriceShape.current = null;
    }
    if (showAvgPriceLine && tradingViewChart.current) {
      const pairPosition = positions.find(
        (item) => item.pair === props?.pair?.pairId
      );
      if (!pairPosition) {
        return;
      }

      const avgPriceUsd =
        Number(pairPosition?.volumeUsdBought || 0) /
        Number(pairPosition?.baseAmountBought || 0);
      let avgPrice = avgPriceUsd;

      if (chartSetting.unit !== CANDLE_UNIT.USD) {
        const totalBaseTokenIfBuyNow =
          Number(pairPosition?.volumeUsdBought || 0) /
          Number(pairPrice?.priceUsd);
        avgPrice =
          +pairPrice?.price *
          (totalBaseTokenIfBuyNow / +pairPosition?.baseAmountBought);
      }

      if (
        chartSetting.type === CANDLE_TYPE.MCAP ||
        chartSetting.type === CANDLE_TYPE.MCAP_USD
      ) {
        avgPrice = Number(
          multipliedBN(avgPrice, getCirculatingSupply(props.pair))
        );
      }
      try {
        avgPriceShape.current = getActiveChart()?.createShape(
          { price: avgPrice } as PricedPoint,
          {
            shape: "horizontal_line",
            text: "Average Price",
            overrides: {
              linecolor: "#FFD300",
              linestyle: 1,
              showLabel: true,
              horzLabelsAlign: "right",
              textcolor: "#FFD300",
              bold: true,
              vertLabelsAlign: "bottom",
            },
          }
        );
      } catch (e) {
        console.error("updateStateForAvgLinePriceLine", e);
      }
    }
  }, [isChartReady, showAvgPriceLine, positions]);

  const calculateOrderLinePrice = (order: TOrder) => {
    let price = +order.payload.targetPrice;
    if (
      order?.orderType?.toLocaleLowerCase()?.includes("sell") &&
      order?.payload?.targetPriceQuote
    ) {
      price = +order?.payload?.targetPriceQuote;
    }

    if (chartSetting.unit !== CANDLE_UNIT.USD) {
      price = +multipliedBN(
        dividedBN(price, pairPrice?.priceUsd),
        pairPrice?.price
      );
    }

    if (
      chartSetting.type === CANDLE_TYPE.MCAP ||
      chartSetting.type === CANDLE_TYPE.MCAP_USD
    ) {
      price = Number(multipliedBN(price, getCirculatingSupply(props.pair)));
    }
    return price;
  };

  const calculateOrderLineQuantity = (order: TOrder) => {
    const price = calculateOrderLinePrice(order);
    if (
      chartSetting.type === CANDLE_TYPE.MCAP ||
      chartSetting.type === CANDLE_TYPE.MCAP_USD
    ) {
      return "MC: " + formatNumber(price);
    }
    return "Price: " + formatNumber(price);
  };

  const updateStateForMyOrders = useCallback(async () => {
    if (!tradingViewChart.current) {
      return;
    }
    if (openOrderLines.current) {
      openOrderLines.current.forEach((item: any) => {
        item?.remove();
      });
    }
    if (!displayOptionsRef?.current?.ORDERS) {
      openOrderLines.current = [];
      return;
    }
    const orders =
      openOrders.filter((item) => item.pairId === props?.pair?.pairId) || [];
    if (!orders.length) {
      openOrderLines.current = [];
      return;
    }
    openOrderLines.current = [];
    for (const order of orders) {
      if (order.orderType === "buyDca" || order.orderType === "sellDca") {
        return null;
      }

      try {
        openOrderLines.current.push(await createOrderLine(order));
        // return activeChart?.createShape(
        //   { price: Number(price) } as PricedPoint,
        //   {
        //     shape: 'horizontal_line',
        //     text: getTextTypeOrder(),
        //     overrides: {
        //       linecolor:
        //         order.orderType.includes('sell')
        //           ? '#EE4B2B'
        //           : 'rgb(39, 217, 113)',
        //       linestyle: 0,
        //       showLabel: true,
        //       horzLabelsAlign: 'right',
        //       textcolor:
        //         order.orderType.includes('sell')
        //           ? '#EE4B2B'
        //           : 'rgb(39, 217, 113)',
        //       bold: true,
        //       vertLabelsAlign: 'bottom',
        //     },
        //   },
        // );
      } catch (e) {
        console.error("updateStateForMyOrders", e);
      }
    }
    return openOrderLines.current || [];
  }, [isChartReady, openOrders, props?.pair?.pairId]);

  useEffect(() => {
    if (!accessToken || !isChartReady) {
      return;
    }
    updateStateForAvgLinePriceLine();
    Storage.setUserSettings("showAvgPriceLine", showAvgPriceLine);
  }, [
    accessToken,
    isChartReady,
    showAvgPriceLine,
    positions,
    updateStateForAvgLinePriceLine,
  ]);

  useEffect(() => {
    if (!isChartHeaderReady || !isChartReady) {
      return;
    }
    if (tradingViewChart?.current) {
      getActiveChart()?.clearMarks();
    }

    displayOptionsButtonRef?.current?.applyOptions(
      buildDisplayOptionsParams({
        displayOptions,
        setDisplayOptions,
        displayOptionsRef,
      })
    );

    if (tradingViewChart?.current) {
      getActiveChart()?.clearMarks();
      getActiveChart()?.refreshMarks();
    }
  }, [isChartHeaderReady, isChartReady, displayOptions]);

  useEffect(() => {
    if (!accessToken || !isChartReady) {
      return;
    }
    updateStateForMyOrders();
    Storage.setUserSettings("showMyOrders", displayOptions?.ORDERS);
  }, [
    accessToken,
    isChartReady,
    displayOptions?.ORDERS,
    openOrders,
    updateStateForMyOrders,
  ]);

  const getListMyTransaction = async (from: number, to: number) => {
    // const resolutionUnit = convertResolutionString2Unit(resolution);
    const res = await rf
      .getRequest("TransactionRequest")
      .getMyTransactions(props.pair.network, {
        from,
        to,
        pairId: props.pair.pairId,
        page: 1,
        limit: 200,
      });

    const newData = res?.docs || [];

    const pairMyTrades = newData.filter(
      (item: any) => item.pairId === props.pair.pairId
    );
    return pairMyTrades;
  };

  const getMakerListTransaction = async (
    from: number,
    to: number,
    makerAddresses: string
  ) => {
    if (!makerAddresses) {
      return [];
    }
    const res = await rf
      .getRequest("TransactionRequest")
      .getTransactions(props.pair.network, {
        pairIdOrSlug: props.pair.pairId,
        makerAddress: makerAddresses,
        toTimestamp: to,
      });

    const newData = res?.docs || [];

    const pairMyTrades = newData.filter(
      (item: any) => item.pairId === props.pair.pairId
    );
    return pairMyTrades;
  };

  const getQueryBy = () => {
    if (chartSetting.type === CANDLE_TYPE.PRICE) {
      return chartSetting.unit === CANDLE_UNIT.TOKEN_QUOTE
        ? CANDLE_TYPE.PRICE
        : CANDLE_TYPE.PRICE_USD;
    }

    return chartSetting.unit === CANDLE_UNIT.TOKEN_QUOTE
      ? CANDLE_TYPE.MCAP
      : CANDLE_TYPE.MCAP_USD;
  };

  const getBars = async (
    symbolInfo: LibrarySymbolInfo,
    resolution: ResolutionString,
    periodParams: PeriodParams,
    onResult: HistoryCallback
  ) => {
    if (Number(periodParams.from) <= 0 || Number(periodParams.to) <= 0) {
      return onResult([], {
        noData: true,
      });
    }

    const to = periodParams.to;

    const resolutionUnit = convertResolutionString2Unit(resolution);

    try {
      const pair = props.pair;
      const res = await rf
        .getRequest("CandleRequest")
        .getCandles(pair.network, {
          limit: Math.min(periodParams.countBack, 300),
          to: to,
          pair: pair.slug,
          queryBy: getQueryBy(),
          resolution: resolutionUnit,
        });

      const listCandle = res?.candles || [];

      const validListCandle = listCandle.filter((candle: TCandle) => {
        return !isInvalidCandle(candle);
      });

      if (!validListCandle.length) {
        return onResult([], { noData: true });
      }

      // to hotfix mc supply
      let convertRate = "1";
      if ([CANDLE_TYPE.MCAP, CANDLE_TYPE.MCAP_USD]?.includes(getQueryBy())) {
        const totalSupply = props.pair?.tokenBase?.totalSupply;
        const currentTotalSupply = getCirculatingSupply(props.pair);
        convertRate = dividedBN(currentTotalSupply, totalSupply);
      }

      const bars: any = validListCandle.map((candle: TCandle) => {
        return {
          time: +candle.timestamp * 1000,
          open: +multipliedBN(candle.open, convertRate),
          high: +multipliedBN(candle.high, convertRate),
          low: +multipliedBN(candle.low, convertRate),
          close: +multipliedBN(candle.close, convertRate),
          volume: getVolume(chartSetting, candle),
        };
      });

      const lastCandle = bars[bars.length - 1];

      if (periodParams.firstDataRequest) {
        lastCandleRef.current = lastCandle;
      }

      onResult(bars, { noData: false });
    } catch (error: any) {
      console.log(`GetBars onError:`, error);
      onResult([], { noData: true });
      // onError('Something went wrong!');
    }
  };

  const handleWhenNewTransaction = useCallback(
    (event: TBroadcastEvent) => {
      const transaction: TPairTransaction = event.detail;
      if (transaction.pairId !== props.pair.pairId) {
        return;
      }
      if (
        !resolutionStringRef.current ||
        typeof onTickRef.current !== "function"!
      ) {
        return;
      }
      if (
        [TradingType.ADD, TradingType.REMOVE].includes(transaction.tradingType)
      ) {
        return;
      }
      if (!lastCandleRef.current) {
        setForceReset((prev) => prev + 1);
        return;
      }

      const lastCandleUpdated = mappingTradeToLastCandle(
        transaction,
        lastCandleRef.current,
        resolutionStringRef.current,
        onTickRef.current,
        chartSettingRef.current,
        props.pair
      );
      if (lastCandleUpdated) {
        lastCandleRef.current = lastCandleUpdated;
      }

      if (displayOptionsRef?.current?.MY_TRADES && isMyTrade(transaction)) {
        getActiveChart()?.refreshMarks();
      }
    },
    [isChartReady]
  );

  const isMyTrade = (transaction: TPairTransaction) => {
    if (!walletsRef.current?.length) {
      return false;
    }
    const walletAddresses = walletsRef.current?.map(
      (wallet: TWallet) => wallet.address
    );
    return walletAddresses?.includes(transaction.maker.address);
  };

  const handleWhenRefreshData = (event: TBroadcastEvent) => {
    setForceReset((prev) => prev + 1);
  };

  useEffect(() => {
    if (!props?.pair?.pairId) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.TRANSACTION_CREATED,
      handleWhenNewTransaction
    );
    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.TRANSACTION_CREATED,
        handleWhenNewTransaction
      );
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    };
  }, [props?.pair?.pairId]);

  const subscribeBars = (
    symbolInfo: LibrarySymbolInfo,
    resolution: ResolutionString,
    onTick: SubscribeBarsCallback,
    listenerGuid: string,
    onResetCacheNeededCallback: () => void
  ) => {
    resolutionStringRef.current = resolution;
    onTickRef.current = onTick;
  };

  const getMarks = async (
    symbolInfo: LibrarySymbolInfo,
    from: number,
    to: number,
    onDataCallback: GetMarksCallback<Mark>,
    resolution: ResolutionString
  ) => {
    console.log("getMarks", {
      from,
      to,
      resolution,
    });
    let marks: Mark[] = [];
    if (displayOptionsRef?.current?.MY_TRADES && accessToken) {
      marks = marks.concat(await buildMyTradeMarks(from, to));
    }

    if (displayOptionsRef?.current?.DEV_TRADES) {
      marks = marks.concat(await buildDevTradeMarks(from, to));
    }

    if (displayOptionsRef?.current?.TRACKED) {
      marks = marks.concat(await buildTrackedTradeMarks(from, to));
    }

    if (selectedMakerAddress.current) {
      marks = marks.concat(await buildMarkerTradeMarks(from, to));
    }

    return onDataCallback(marks);
  };

  const buildMyTradeMarks = async (from: number, to: number) => {
    let marks: Mark[] = [];
    const listMyTrades = await getListMyTransaction(from, to);
    marks = listMyTrades.map((trade: any) => {
      let color, text, label;
      switch (trade.tradingType) {
        case TradingType.BUY:
          color = "green";
          text = `Bought ${formatNumber(
            trade.baseAmount
          )} tokens for ${formatUsdNumber(trade.totalUsd)} at ${formatUsdNumber(
            trade.priceUsd
          )}/token (${formatQuotePriceNumber(
            trade.price
          )} ${props.pair.network?.toUpperCase()}/token) on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "B";
          break;
        case TradingType.SELL:
          color = "red";
          text = `Sold ${formatNumber(
            trade.baseAmount
          )} tokens for ${formatUsdNumber(trade.totalUsd)} at ${formatUsdNumber(
            trade.priceUsd
          )}/token (${formatQuotePriceNumber(
            trade.price
          )} ${props.pair.network?.toUpperCase()}/token) on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "S";
          break;
        case TradingType.ADD:
          color = "blue";
          text = `Added liquidity ${formatNumber(
            trade.baseAmount
          )} tokens on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "AL";
          break;
        case TradingType.REMOVE:
          color = "orange";
          text = `Removed liquidity ${formatNumber(
            trade.baseAmount
          )} tokens on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "RL";
          break;
      }
      return {
        id: trade.hash,
        time: trade.timestamp,
        color: color,
        text: text,
        label: label,
        labelFontColor: "white",
        minSize: 20,
      };
    });
    return marks;
  };

  const buildMarkerTradeMarks = async (from: number, to: number) => {
    let marks: Mark[] = [];
    const trades = await getMakerListTransaction(
      from,
      to,
      selectedMakerAddress.current
    );
    marks = trades.map((trade: any) => {
      let color, text, label;
      switch (trade.tradingType) {
        case TradingType.BUY:
          color = "green";
          text = `Bought $${formatUsdNumber(
            trade.totalUsd
          )} on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "B";
          break;
        case TradingType.SELL:
          color = "red";
          text = `Sold $${formatUsdNumber(
            trade.totalUsd
          )} on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "S";
          break;
        case TradingType.ADD:
          color = "blue";
          text = `Added liquidity (${formatNumber(
            trade.baseAmount
          )} tokens and ${formatNumber(
            trade.quoteAmount
          )} SUI) on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "AL";
          break;
        case TradingType.REMOVE:
          color = "orange";
          text = `Removed liquidity (${formatNumber(
            trade.baseAmount
          )} tokens and ${formatNumber(
            trade.quoteAmount
          )} SUI) on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "RL";
          break;
      }
      return {
        id: trade.hash,
        time: trade.timestamp,
        color: color,
        text: text,
        label: label,
        labelFontColor: "white",
        minSize: 20,
      };
    });
    return marks;
  };

  const buildDevTradeMarks = async (from: number, to: number) => {
    let marks: Mark[] = [];
    const res = await rf
      .getRequest("TransactionRequest")
      .getDevTrades(props.pair.network, props.pair.pairId, {
        toTimestamp: to,
        fromTimestamp: from,
      });

    marks = res?.map((trade: any) => {
      let color, text, label, imageUrl;
      switch (trade.tradingType) {
        case TradingType.BUY:
          color = "green";
          text = `Dev bought ${formatNumber(
            trade.baseAmount
          )} tokens for ${formatUsdNumber(trade.totalUsd)} at ${formatUsdNumber(
            trade.priceUsd
          )}/token (${formatNumber(
            trade.price
          )} ${props.pair.network?.toUpperCase()}/token) on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = `DB`;
          imageUrl =
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQCAYAAACAvzbMAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAA5SSURBVHgB7d3dcRTHGgbgT6d8b50IGEdgHAFLBLYj8BIBEAEiAlAEEhEYImAdgSECjzMQEfh0M62DkPnX7nTP9PNUdY1s37hKM3r7698IAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAPjqJD//zzzyY9zoLrxvK8KD+/Kc/cLo6Ojl4FN5bev5fpMQRf66K0bEzt73j3ro7ez/l9F/0aguuGT/3H9IcvP/JHOqb2OrVd/uf04V4EX2MI79/eXXs//4jp3dwFB9NzBfIy2JdXpb2I6aMdg49K799fIUDmtIvp3Xzu3dwvAcIh7MIH+1ECpKrc0TlNbefdvDkBwqH5YK8RIM04T+2ZYa5v95+Aw7od04KFv9IfzrMS3tCCbWov86IG7+W3ESDMaRvTB5vDZBvQhk1M72Xu4AzBFxMg1DCkdiZIaMw2pkr5UfBFBAg1DSFIaM9JeSeH4JMECC0YYgoSQwi0YkjtTx2bTxMgtGQbhhBox3FMHRvv40cIEFpkCIGWnAiRDxMgtGoI1QjtECIfIEBo3UlZpz8E1CVErhEgLMEmpnX6twPqyiHyIHhLgLAUQ0yrYny81PbEzvWJAGFpnhhGoAG/G1YVICyTsWhqe7vENzonQFiqHCJulaSmTe9DqgKEJdsKESp7lN7B4+iUAGHpcog8Cagjh0e3VUjPd6KzHg9SiFwcHR09Dq4bS2vdcWlDLM/99P49Te/fRXRGgLAWeU7kTfqInwZX5Rv3TmJByuqm3PK+nzvlOUS7LquQk+iMISzWxPr8FchXH+drZnNnILVfU/sh/eu7qT2Ldqup+z3OhahA5rFL7V604zjeHzLI7Va86/UtWT499a7719el3FueW5Qj1vMy7iHakb+lbWpdVcACZCZL+oNWevFLGT64bkjt99R+ClYpfUvn6XHeYJD8HJ0FiCEs/uUjwwensYzJ2Oy2lVnrV4LkcmirBZvehrEECJ9VAuVBCZPcs295LPrSA/Mh61fmS7bpx1ZW4G2jIwKEr5I+1lflg81Bkud1xmjXWc+bvHpSVprlaqT2UtpNdESA8E3ymvc8hFCqklaDZIhpjJwOlIn22iFyJzoiQLixK0HS4kY+Q1kdyRVyejyMeo57OqVXgLA3ZRghB8ku2qIK6UiZXD+NejbRCQHCXpVJzTyM0FI1silLPunHSdQbVu3m5kwBwkGUaiRPtI/Rhq5PTe1NOZeqVifm++iEAOFgynh0rkbGqG+Ijk9N7VEZyqoxoa4CgX0oO/BziLyK+ro8r6hzNeZCunnHBAgHdyVExqir67sbOrWL+QkQ2KcyJt1CiKhCOlL2hsw9jCVAYN+uVCI1N3rlj/uXoCe74CAECLMqIVL7aPvfgp68Dg5CgDC7FCLPo/JGL7vTuzIGByFAqOUk6n7YqpB+dHdX+VwECFWUSfWaQ1m/mEzvhgA5EAFCNWWFTK3LgC6vIIV9G6MTAoTa8r6MWj3EnwP2r5uKR4BQVRnKqjWh3t0VpMzi7+iEAKEFT6Ner20brN3cnYQxOiFAqK5yFWIYa/3mPtxwF50QILSiVhViGGv9fox5jdEJAUITShVSa0XWJlizIeZzUa4x6IIAoSXPo45NsErlfvI5h7D+iI4IEJpR9oXsYn7mQdZrE/Oq1QmqQoDQmho9uME8yGrNfWTNLjoiQGjNedThiPeVKcNXm5jPrpw23Q0BQlPKB1hjErKbe6w78ijmVWsRSDUChBbVGMaae6knB1Sqj23MZ0ydn/PojAChRTUmIlUg6/Iy5vU4OiRAaFGNIazj0mtl4dLvMQ9dDTGfLquPTIDQnLKpsEaIbIJFK+FxEvPqsvrIBAitqrKcN1isSuFx3mv1kX0X0KYaFcitYHHKHp4nMf/JymN0XH1kAoRWWcrLZ6Xw2KTHWdSpHh/2tu/jOkNYtGqM+Q3BIuTgSC2vtMptiPk9TuHR1bElH6ICoUl5Ij39gRhj3j8OeSXWcZnEpzFlqGob09llm6jnNL0jJ4EAoWk1/pAPUWf4jGuunKR7pzw3Ud+zFB4PgrcECC17HfPPSwyxrgD5Lf0hvhPLcVzaEO3J4bEN/k+A0LIx5re2U3mHMLezD6cqj38ziU7Lag1hwVUPhceHqUBoWY0AcS8Il8bUfu3pitqvpQKhZWPM7/uAaYPgT8Lj01QgtKxGBfLfoGe71O71vkHwS6lAaFmNAFGB9GmX2t0UHHeFx5dTgQC9ysNTL1J7avPotxEgtMwqLPbp8pqAHBrPVRo3J0BoVjnOJGBP8oqqXbA3AgToxf3UIcknG+Qq5JVhq5sTIEAvfintrRQmOUh2qb1QmXwbq7CAXuVqJO8wf5nC5K/Uzsr9InwhAQIwLZ7Yxrsw2QafJUAA3jekdiZIPk+A0KxygRDUMsS7IPkl+BcBQssECC0YUvs9hcgTnZr3CRB43xjwYXnC/c9yUyIhQGib3h6tGWIKEUNaIUBomwChRfm9zENa3V8yJUBoWY0AGQO+TJ4TeRQdsxOdlg0xvzexLs9jOjxwSY6vtB/L83a06SSFyHh0dPQsOiRAaFmNCmRt5yO9Tn/czmMFyi7xHCR34sqRJA04T/9vr3u8vdAQFi0bYn5j0KR8XlVq+e6OX2O6OfJeTGdZteD3HldnCRBadivm54TWBcgn6ebKKt8gmP7xh9RqDyENqZ1FZwQILTOExWfli6FS28YUJGPUs+ltZZYAoWU1Jk67G8deixIkOUQeRz2PetqtLkBoUrn4Z24XLhlavvQ7PEmPh1FHDo8n0QkBQquGmN8YrEKebE+Pn6LOkOS2lwl1AUKrNjG/v4PVKMtqa1UiXWwwFCC06seYn/mPlSl7YE5jftse5kIECK0ygc5epBDJK6Nq/G5XvyJLgNCcsuPYOVjsU42hrN9i5QQILapy7lGPR1H0Iu9ij/l3rQ+lM7RaAoQW/Rzz2wVrV2N/yCZWTIDQlDLxuIn5vQ5WrVQhcy/rvRMrJkBoTa1TVndBD+Y+M2uz5tVYAoTW1Bi+ysx/9KHG77nVu0xuTIDQjLJ7t0YFks9QGoMe7GJ+AgRmsIk6dkEXSkdh7nmQGptiZyFAaEmt4x/+CHoyxryGWCkBQhPKevkh6tgFPRljXkOslAChFbWqj535j+68iXlZhQWHUibPN1HHi4DDEiBwQDWPvn4ecGBrvR9EgFBVmfvYRh2vDF/BtxMg1HYW9dS4J4L6vg/2QoBQTao+7kfdFSq7oEerv+hpLgKEKsqY8EnUY/VVv2bfGb7Wd02AUEseuqrZE5z7UD0aUA42VIHsiQBhdukjzquuNlHPWO7Kpj81zqWa++iU2QgQZpXCIx+WeBJ11bhYiDZUOawzVkqAMJsy71Fz1VU2hsnzntW44EkFAjdRwuNl1B9/NnneqfQO5uGrGkNYq73tUoBwcFfCY4j6DF/1637UMcZKCRAOqvT6WgmPU9VHn0onZht1rPa2SwHCwaSP9rdoJzzG1J4Gvap53poAga+RwuNJepxHO2vuVR+dqlx95CXjq51E/y5gj8qQVV5p1dI90PkjVn10qGwcfBn17GLFVCDsRf5QS9XxZ7QVHpmJ837lzswQ9az6umQVCDdSenh5dcuDaPOIiHO7zvtUTjyosXHwql2smADhmywgOLIxVB9dKuFxEnWtfs+RAOGrlAugfo5pUrL1Q+kemzjvS+nY5KHUbdS3+gM7BQifVD7IPKexlNC4dGroqi+lc1N7zuOq1V+XLEB4T1nyuEntx5iCYxPLM0b94QtmUjo5ecjqQbTjfM3Ldy8JkHkcl95RK4Yrz1sxVRW3y3PpdyXkj/ZuDx9v7xofTu1i7k2AzOPyOA8O76F5j/UqoZFP1N1GO0NV15338g4KENbksXmP5SvDqFnueOWfb5WfL6vk1nWz8k+AsBZ50vwkuO5+OZNsCYZYvm6qj0yAsAbP0kfb0gRqS9Ywr7UUY3S278hRJixdPulUeNCC7vYdCRCWLIeHFVe0oMsjcwQIS5V3+QoPWjCm9jA6ZA6EJTo150Ejut53pAJhaR4LDxpyr+d9RyoQliL38B7a50FDcnis/ryrTxEgLMEY0zDBGNCGezozhrBo32lqPwkPGpErYeFRqEBo1RjTh7oLaMOY2q/pnXwVvKUCoUWXVccuoA27mIZRhccVAoSW7GL6SB/Y30FD8uINc3AfYAiLFozhJF3as4spPFQdHyFAqClXGXm46qmKg4bkdzF3aJ4GnyRAqGGMKTi6uPaTxdCh+UoChDntYurZ7QLaITi+kQDh0HyctGoXOjQ3IkA4hDG1F6k993HSkNyByRPi+d00fLoHAoR9yB9iPhPodUyhMQa0IQfGHzG9n6+Exn4JEL7WGNNH+Xd57gQGDcjBMMb0Tr4uT4FxYAKE7KK0qz+Pqb0pz8vSf/RBMqMvfS8vdGIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABow/8Az1chIn3r85oAAAAASUVORK5CYII=";
          break;
        case TradingType.SELL:
          color = "red";
          text = `Dev sold ${formatNumber(
            trade.baseAmount
          )} tokens for ${formatUsdNumber(trade.totalUsd)} at ${formatUsdNumber(
            trade.priceUsd
          )}/token (${formatNumber(
            trade.price
          )} ${props.pair.network?.toUpperCase()}/token) on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = `DS`;
          imageUrl =
            "data:image/png;base64,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";
          break;
        case TradingType.ADD:
          color = "blue";
          text = `Dev added liquidity ${formatNumber(
            trade.baseAmount
          )} tokens on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = `DA`;
          imageUrl =
            "data:image/png;base64,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";
          break;
        case TradingType.REMOVE:
          color = "orange";
          text = `Dev removed liquidity ${formatNumber(
            trade.baseAmount
          )} tokens on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = `DR`;
          imageUrl =
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQCAYAAACAvzbMAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAA2iSURBVHgB7d3hlRVFGgbgzz3+X4yAJgLHCLxEIEbgNQIhAoYIhAiACNQIuETgGIFtBAsRuFXbNSsIDgxzb1V19fOcU6dR/3iG7nn7q6r+KgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOILPYoP+/PPPXbo8Df5uLtdX5c+vyzWPV5999tlFcGPp/nuRLlNwE3O5virjj3CvVvd5bNcU/N101X9Mv/jyJT+Ycxq/pXHI/5we1lfBdUzh/rup6ar/+Ld79WUs9+khOKotVyAvgmO5KOOXWB7UOfhH6f77PQRIK4dYAuUgUG5OgHAKh1jC5Gdh8i4B0o05lnv1uTD5NAKEU8uVyZNY3vjmQID0aU7jUbhPr+VfAad1FsuGhd/TL86nJbyhN1O8fZ9OwQcJEGrap/Eiv4GnsQ/o0z4EyUcRILQwpfFUkNC5fSxB8jCNW8E7BAgtTSFI6N95Gr+me/Re8BYBQg+mWILElAG9mtL4Kd2fPwb/J0DoyT7KlEFAn+6XinkKBAhdOveQ0rEpls0gU2ycAKFXU6hG6NcUy7rIWWyYAKF3uRrxtkeP8s6sF1sOEQHCGuxi4w8q3boMkSk2SICwFlMsUwb3A/qy2RARIKzNj9ZF6NAUGzxjSICwRudChA7ttvadiABhrXKIOFWS3tzfUsNQAcKa7YUIHdpMFSJAWLu99hJ05mwrmz0ECCO4b02EzjzcQgdfAcIozm3xpSM5PIa/Hx1py2ju9n6+dYMjbfORwq9iXfIv4NvlOsU6jwDOP/M76X5c28/+o30e1HBI4/vox603xlTG7XJd+9feuSX8Xedav+Xx2n8eZTrorIyvY+lO0PsUUf7/26fxOAalAqnjkB7gu7ES5edz+aDm6xTrcpF+3l9FpxpUIHdGDNRyn+5juU+n6NOqnv3rsgbCO/IUUBr5rfXbNO6kf5UfgDwNMsc6nNmZNb5yn+7LPZor/Dn6sxv5uxABwgeVB/V+eVDzm/3z6D9MNvVB19ale/NZx0Gyi0EJEK4lPaR5emgfS5D0+tZ36ekWtlLylxwksVTMh+jHdzEoAcInyTtLOn/ry6Y0fB+yMXm9p6w7PIo+TKMeRSBAuLE3gqSXB/ZNprI2Kt2T5+nyIPqwiwEJEI6mPLA5SA7RF1XIRuXNINHHFvpdDEiAcFQdTh9keSfMPtiksi7yJNr6MgYkQDiJUo3khfY5+rCJ3kS8X95FGG0r42nE+0+AcDJ5x1YsO2LmaG+KDfQm4kqtq+IpBiNAOKnyBXQOkYto7wdVyHaVHmnPo53hdmIJEE7ujRCZo61NdEjlSs+inSkGI0CoonQk7SFEVCEbVqqQQ7RxOwYjQKjmjUqkZXvrHB73gi1rOY01FAFCVSVEWu/LH7a1BB/l52hjisEIEKpLIZIf4Jb78ofukMrVynRqD5s6Vk+A0Mp5tF0PUYVs28vgxgQITZS3wJZTWfcspm/aHNyYAKGZxvvyL48bZZvm4MYECK3l7zJa7cr6JtiqObgxAUJTZSqr1YL6zjTWZrV4aXkdgxEg9CC33G5VhewD6vhPDEaA0FzjKsQ0FrX8EYMRIPSiVRViGotaWnZgOAkBQhdKFdJqR9Yu2JoWLw3DfbwoQOhJqxYTu2BrBMgRCBC60bBTqnWQ7Zmirlelyh6KAKE3LVpMDHncKFeqfbjTkK1TBAi9eRZtaPG+LV9GXYcYkAChK6Xde4u54uGOG+VKtf++DzEgAUKPWpT7td9IaaS08q85ZTmnF6Mh28cLEHrUYjeWCmQ7ak9XHmJQAoQetXhbu5XeTKdgC2rvumt5eNpJCRC60/DEuF0wtDJ9NUU9F6NOX2UChF412c4bjO5h1DVs9ZEJEHrV4q3tdjCsUn3sop68eP4sBiZA6JWtvBzb06jrUQxOgNCrOeqbgiGl6iNPXU1Rz/DVRyZA6FJZSJ+jrltamoyn7K47j7oexAYIEHrWovncFAyjhMeLqOtZegFq1Vm6KgFCz36L+qZgCKWazOExRT1zbGDt45IAoWdz1GcKawCl8vg16r8QfFv6uW2CAKFnprC4trJdt0V4PBr5o8H3ESD0rEWAqEBWKlcdafwUy7RV7b/HHB7nsTGfB/Rrjvr+HaxKqTi+S2MfbTzfYnhkAoSetahAvgi6V0Lj61i+LN9FOzk89rFRAoSetQgQFUgnyi6qPKYy8pktZ2X0MNX4JIXH/dgwAQLje5F+GceKXAZHzx6k8HgcGydA6JldWMcxBccyp/F9Co9DYBcW/SrtTKAXuTX7V8LjLyoQgKvNoep4LxUIwPvlCjivddwRHu+nAgF4W/6a/HksTRFNo15BgAAs1UbuoPtctfHxBAiwVZeh8TJfVRvXJ0DolsOdOLEHWzg18JQECD0TIJzSD+klJX/dniuQiy21YT8WAQJvm4OtuGyL8r92JClM5nQ5pPFLvprS+jDbeOmZCoSaplg6+uaW8L+nQHlamjbyDwQIPRMgtJLvvX0sfcRymOyDdwgQetYiQOaAt01pPC1BMgX/J0Do2RT1vQ54vymWqa2Hwf8IEHrWogKxcMqHnKtGFgKEnk1R3xzwYVMs6yNnsWEChJ7djvpUIHysKTYeIgKEnpnConf5Ht1siAgQetbiobwIuJ4cIj9tcU1EgNClRm90r3x9zCea0ngaGyNA6NUU9c0Bn26XXnzux4bohUWvdlHfHzGm/EttLd+3TG/8OW+iuFXGWayjM8HDFCKbOYhKgNCrL6O+Udc/fhmh02xp73/ZAPHrWF4yeguV/P+TA/s8NsAUFr2ygM5b8lt9Pi0wjcdpfJvGF+lffxvLoVA9+WErZ9kIELpTOqDqg8UHpRDJJwnmELkTyznmPcj37i42QIDQoyZ76tMvIhXISuUpujT2sQTJHO39EBsgQOjRN1HfIVi9stbzVRpPoq2zLUxjCRC6Uh66XdT3WzCEslaSF7IfRTubmMYSIPTmXrRxCIaSQuQ82q6L7GJwAoTetJi+yqx/jClXInO00WIrelUChG6UXkItKpB5hO8keFf5oO9BtDF8g0UBQk920cYhGFbe6htt/o5vjd5gUYDQk1ZHhb4MRvdLtDHFwAQIXSgfD07RxiEY3bNoY4qBCRB60ar6OFj/GF9ZCzlEfUN/CyJAaK7ME++ijVZTG9TX4u9agMCJtao+st4a8XE6c9QnQOBUytrHPtq4MH21KS2+9fl3DEyA0FrLY0Bb90uiovKy4MjiIxIgNJOqj9yxdIp2DsHWCJAjEiA0URbOz6Mdu6+2aQ6ORoDQSp66arnA2MvhQ9SlAjkiAUJ1qfrIu6520U7uffUs2KLXwdEIEKpK4ZGbJZ5HWy3PiYBhCBCqKeseLXddZXNYPIejECBUUcLjRbT/sMriORyJAOHk3giPKdozfQVHIkA4qRQe+VCdXsLjieoDjkeAcDIpPL6LfsJjTuNxAEcjQDiJFB4/xnIGQy/N5FQfcGSfBxxRmbLKO616Og86f/eh+oAjU4FwFCk4bpWq49foKzwyC+dwAioQbiQHR7rkpoj3o8+zD5756hxOQ4DwSVYQHNkcqg84GQHCtZQDoL6J5RCo3k9be2ThHE5HgHClUmnkNY21hMalJ6au4LQECG8pX43v0vgyluDYxfrM0b5hIwxPgNRxq0z99GJ643o7lqrirFzXUmH8k3zew91UfTj3AU5MgNRx2c6D03tg3QPq8B0II3lk3QPqESCMIi+anwdQjQBhBM9TeNwPoCoBwtpdxPIxI1CZAGHNcnjYcQWNCBDW6nkID2jKNl7W6Ik1D2hPBcLaPBIe0AcVCGuRp6oe+M4D+iFAWIM5lvWOOYBumMKid0/S+Ep4cCQ2XRyRCoRezWl8n4LjEHA8tQNkioGpQOjRZdVxCKBbKhB6cohll9Uh4DTm4GgECD2YQyddxjTFwAQILeX56Dxd9dgX5VRyEXWt/YC2KwkQWphjCY5ngoPK5qgrn0Y6jbqL0CI6NR1i+Z7jThqqDqor91zt+24XgxIgnFp+WB+l8UV6eO9aIKcDh6jrLAYlQDiFOZYpqhwYOTjOVRt05GXU9U0MyhoIx5DD4ec0fstXX43TudoL6VNaB9mNWH0LEK5rjuUB/KNcDwKDNcm/yNMv9PzSU3OH1L2oP3V2cgKE7M2Fxcs/z2m8Ltf8zzksZlNRDCJPsT6Mer5LoWUqFwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYHj/Bf8Yss6g4jXEAAAAAElFTkSuQmCC";
          break;
      }
      return {
        id: trade.hash,
        time: trade.timestamp,
        color: color,
        text: text,
        label: label,
        imageUrl: imageUrl,
        labelFontColor: "white",
        minSize: 25,
      };
    });
    return marks;
  };

  const getTrackedWalletName = useCallback((address: string) => {
    const wallet = walletTrackersRef?.current?.wallets?.find(
      (wallet) => wallet.walletAddress?.toLowerCase() === address.toLowerCase()
    );
    return wallet?.walletName;
  }, []);

  const getTrackedWalletEmoji = useCallback((address: string) => {
    const wallet = walletTrackersRef?.current?.wallets?.find(
      (wallet) => wallet.walletAddress?.toLowerCase() === address.toLowerCase()
    );
    return wallet?.emoji;
  }, []);

  const buildTrackedTradeMarks = async (from: number, to: number) => {
    let marks: Mark[] = [];
    const trades = await getMakerListTransaction(
      from,
      to,
      walletTrackedAddressesRef.current.join(",")
    );
    marks = trades.map((trade: any) => {
      let color, text, label;
      switch (trade.tradingType) {
        case TradingType.BUY:
          color = "green";
          text = `${getTrackedWalletName(
            trade.maker.address
          )} bought $${formatUsdNumber(
            trade.totalUsd
          )} on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "B";
          break;
        case TradingType.SELL:
          color = "red";
          text = `${getTrackedWalletName(
            trade.maker.address
          )} sold $${formatUsdNumber(trade.totalUsd)} on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "S";
          break;
        case TradingType.ADD:
          color = "blue";
          text = `${getTrackedWalletName(
            trade.maker.address
          )} added liquidity (${formatNumber(
            trade.baseAmount
          )} tokens and ${formatNumber(
            trade.quoteAmount
          )} SUI) on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "AL";
          break;
        case TradingType.REMOVE:
          color = "orange";
          text = `${getTrackedWalletName(
            trade.maker.address
          )} removed liquidity (${formatNumber(
            trade.baseAmount
          )} tokens and ${formatNumber(
            trade.quoteAmount
          )} SUI) on ${formatUnixTimestamp(
            trade.timestamp * 1000,
            "MMM D HH:mm:ss"
          )}`;
          label = "RL";
          break;
      }
      return {
        id: trade.hash,
        time: trade.timestamp,
        color: color,
        text: text,
        label: label,
        labelFontColor: "white",
        imageUrl: emojiToBase64ImageIcon(
          getTrackedWalletEmoji(trade.maker.address) || "😀"
        ),
        minSize: 20,
      };
    });
    return marks;
  };

  const datafeed: IBasicDataFeed = {
    onReady: (callback: any) => {
      setTimeout(() => callback(DATAFEED_CONFIGURATION));
    },
    searchSymbols: () => {},
    resolveSymbol: async (
      symbolName: string,
      onSymbolResolvedCallback: ResolveCallback
    ) => {
      const symbolInfo: LibrarySymbolInfo = {
        ticker: getSymbolFromPair(props.pair),
        name: getSymbolFromPair(props.pair),
        description: getChartDescription(props.pair, isMobile),
        pricescale: 10 ** 10, // need change precision
        volume_precision: 3, // need change precision
        minmov: 1,
        minmove2: 4,
        exchange: "raidenx.io",
        listed_exchange: "",
        session: "24x7",
        has_intraday: true,
        has_daily: true,
        format: "price",
        has_seconds: true,
        seconds_multipliers: ["1"],
        has_weekly_and_monthly: false,
        intraday_multipliers: DATAFEED_CONFIGURATION.intraday_multipliers,
        timezone: getClientTimezone(),
        type: SYMBOL_TYPE.bitcoin,
        supported_resolutions:
          DATAFEED_CONFIGURATION.supported_resolutions as ResolutionString[],
      };
      onSymbolResolvedCallback(symbolInfo);
    },
    getBars,
    getMarks,
    subscribeBars,
    unsubscribeBars: () => {},
  };

  const getInitInterval = () => {
    if (typeof window === "undefined") {
      return;
    }
    const searchParams = new URLSearchParams(window.location.search);
    const resolutionParam = searchParams.get("resolutionString");
    if (
      resolutionParam &&
      DATAFEED_CONFIGURATION.supported_resolutions.includes(resolutionParam)
    ) {
      return resolutionParam;
    }
    return (
      localStorage.getItem("tradingview.chart.lastUsedTimeBasedResolution") ||
      (defaultProps.interval as ChartingLibraryWidgetOptions["interval"])
    );
  };

  useEffect(() => {
    setIsChartReady(false);
    setIsChartHeaderReady(false);
    const initInterval = getInitInterval();
    const widgetOptions: TradingTerminalWidgetOptions = {
      symbol: defaultProps.symbol as string,
      theme: "Dark" as ThemeName,
      datafeed: datafeed,
      interval: initInterval as ResolutionString,
      container: chartContainerRef.current,
      library_path: defaultProps.libraryPath as string,

      locale: "en",
      disabled_features: getDisabledFeatures(),
      enabled_features: getEnabledFeatures(),
      client_id: defaultProps.clientId,
      user_id: defaultProps.userId,
      fullscreen: defaultProps.fullscreen || false,
      autosize: defaultProps.autosize || true,
      custom_css_url:
        props.device === "mobile"
          ? "/tradingview-mobile.css?id=211120241"
          : "/tradingview.css?id=211120241",
      timezone: getClientTimezone(),
      time_frames: [
        {
          text: "3M",
          resolution: "60" as ResolutionString,
          description: "3 months in 1 hour intervals",
        },
        {
          text: "1M",
          resolution: "30" as ResolutionString,
          description: "1 month in 30 minutes intervals",
        },
        {
          text: "5D",
          resolution: "5" as ResolutionString,
          description: "5 days in 5 minutes intervals",
        },
        {
          text: "1D",
          resolution: "1" as ResolutionString,
          description: "1 day in 1 minute intervals",
        },
      ],
      overrides: {
        "scalesProperties.fontSize": 13,
        "mainSeriesProperties.styledText.fontSize": 15, // size text
        volumePaneSize: "small",
        "paneProperties.legendProperties.showSeriesOHLC": false,
        "paneProperties.backgroundGradientStartColor":
          props.device === "mobile" ? "#1F2630" : "#121318",
        "paneProperties.backgroundGradientEndColor":
          props.device === "mobile" ? "#1F2630" : "#121318",
        "mainSeriesProperties.candleStyle.upColor": "#569781",
        "mainSeriesProperties.candleStyle.downColor": "#d13845",
        "mainSeriesProperties.candleStyle.drawWick": true,
        "mainSeriesProperties.candleStyle.drawBorder": true,
        "mainSeriesProperties.candleStyle.borderColor": "#378658",
        "mainSeriesProperties.candleStyle.borderUpColor": "#16C782",
        "mainSeriesProperties.candleStyle.borderDownColor": "#EA3943",
        "mainSeriesProperties.candleStyle.wickUpColor": "#16C782",
        "mainSeriesProperties.candleStyle.wickDownColor": "#EA3943",
        "mainSeriesProperties.volCandlesStyle.height": 400,
      },
      studies_overrides: {
        "volume.volume.transparency": 50, // Set transparency of volume bars
      },
      custom_formatters: {
        priceFormatterFactory: (symbolInfo, minTick) => {
          return priceFormatterFactory(
            symbolInfo,
            minTick,
            chartSettingRef?.current
          );
        },
      },
    };

    const tvWidget = new widget(widgetOptions);
    tradingViewChart.current = tvWidget;
    awaitReady();

    return () => {
      tvWidget.remove();
    };
  }, [chartSetting, props?.pair?.slug, forceReset]);

  // Function to override localStorage chart properties
  const overrideChartProperties = useCallback(() => {
    if (typeof window === "undefined") return;

    try {
      const currentProperties = localStorage.getItem(CHART_PROPERTIES_KEY);
      if (!currentProperties) return;

      const chartProperties = JSON.parse(currentProperties);

      // Check if paneProperties exists and has background properties
      if (chartProperties.paneProperties) {
        const hasBackgroundGradientStart =
          chartProperties.paneProperties.backgroundGradientStartColor;
        const hasBackgroundGradientEnd =
          chartProperties.paneProperties.backgroundGradientEndColor;
        const hasBackground = chartProperties.paneProperties.background;

        // Override background properties if they exist
        if (
          hasBackgroundGradientStart ||
          hasBackgroundGradientEnd ||
          hasBackground
        ) {
          chartProperties.paneProperties.backgroundGradientStartColor =
            props.device === "mobile" ? "#1F2630" : "#151618";
          chartProperties.paneProperties.backgroundGradientEndColor =
            props.device === "mobile" ? "#1F2630" : "#151618";
          chartProperties.paneProperties.background =
            props.device === "mobile" ? "#1F2630" : "#151618";

          // Save updated properties to localStorage
          localStorage.setItem(
            CHART_PROPERTIES_KEY,
            JSON.stringify(chartProperties)
          );
          // console.log("Background properties overridden in localStorage");
        }
      }
    } catch (error) {
      console.error("Error overriding chart properties:", error);
    }
  }, []);
  // Override chart properties immediately when component initializes
  overrideChartProperties();

  const awaitReady = async () => {
    if (!tradingViewChart.current) return;
    tradingViewChart.current.onChartReady(() => {
      setIsChartReady(true);
    });
    if (isMobile) {
      await Promise.race([
        tradingViewChart.current?.headerReady(),
        new Promise((_, reject) => setTimeout(() => reject("timeout"), 800)),
      ]).catch(() => {
        setIsChartHeaderReady(true);
        setIsChartReady(true);
        console.log("Header ready timeout after 800ms, continuing...");
      });
    } else {
      await tradingViewChart.current?.headerReady();
      setIsChartHeaderReady(true);
    }

    try {
      await retry(
        async () => {
          drawCustomChartHeader();
        },
        {
          retries: 50,
          minTimeout: 100,
          maxTimeout: 200,
        }
      );
    } catch (e) {
      console.error("drawCustomChartHeader", e);
    }
  };

  const createOrderLine = async (order: TOrder) => {
    console.log("createOrderLine", order);
    if (!["sellLimit", "buyLimit"].includes(order.orderType)) {
      return;
    }
    const price = calculateOrderLinePrice(order);

    console.log("====price", price);

    const getTextTypeOrder = () => {
      if (order.orderType === "sellLimit") {
        if (order?.payload?.targetPriceQuote) {
          return order.payload.sellLimitType === "takeProfit"
            ? "Sell above"
            : "Sell below";
        }
        return new BigNumber(order.payload.targetPrice).lt(pairPrice?.priceUsd)
          ? "Sell below"
          : "Sell above";
      }
      if (order.orderType === "buyLimit") {
        return new BigNumber(order.payload.targetPrice).lt(pairPrice?.priceUsd)
          ? "Buy below"
          : "Buy above";
      }
      return "";
    };

    return getActiveChart()
      ?.createOrderLine()
      .onCancel(
        order.id,
        async function (this: IOrderLineAdapter, text: string) {
          console.log(
            "onCancel called",
            text,
            this,
            props.pair.network,
            "order.id",
            order.id
          );
          try {
            await rf
              .getRequest("NewOrderRequest")
              .cancelOrder(props.pair.network, order.id);
            toastSuccess("Success", "Cancel successfully!");
            AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
          } catch (e: any) {
            console.error(e);
            toastError("Error", e.message || "Something went wrong!");
          }
        }
      )
      .setPrice(price)
      .setText(getTextTypeOrder())
      .setQuantity(calculateOrderLineQuantity(order))
      .setLineColor(order.orderType?.includes("buy") ? "#16C782" : "#EA3943")
      .setBodyBorderColor(
        order.orderType?.includes("buy") ? "#16C782" : "#EA3943"
      )
      .setBodyBackgroundColor("#121318")
      .setBodyTextColor(
        order.orderType?.includes("buy") ? "#16C782" : "#EA3943"
      )
      .setQuantityBackgroundColor("#121318")
      .setQuantityTextColor(
        order.orderType?.includes("buy") ? "#16C782" : "#EA3943"
      )
      .setQuantityBorderColor(
        order.orderType?.includes("buy") ? "#16C782" : "#EA3943"
      )
      .setCancelButtonBorderColor(
        order.orderType?.includes("buy") ? "#16C782" : "#EA3943"
      )
      .setCancelButtonBackgroundColor("#121318")
      .setCancelButtonIconColor(
        order.orderType?.includes("buy") ? "#16C782" : "#EA3943"
      );
  };

  const drawCustomChartHeader = () => {
    if (!tradingViewChart.current) return;
    const tvWidget = tradingViewChart.current;
    createSwitchPriceType(
      tvWidget,
      chartSetting,
      props.pair,
      (newChartSetting: TChartSetting) => {
        setChartSetting(newChartSetting);
      }
    );

    createSwitchPriceOrMcapButton(
      tvWidget,
      chartSetting,
      (newChartSetting: TChartSetting) => {
        setChartSetting(newChartSetting);
      }
    );

    createDisplayOptionsDropdown(
      tvWidget,
      {
        displayOptions,
        setDisplayOptions,
        displayOptionsRef,
      },
      (displayOptionsApi: any) => {
        displayOptionsButtonRef.current = displayOptionsApi;
      }
    );

    if (accessToken) {
      avgLineButtonRef.current = createAvgLineButton(
        tvWidget,
        showAvgPriceLineRef.current,
        () => {
          showAvgPriceLineRef.current = !showAvgPriceLineRef.current;
          setShowAvgPriceLine(showAvgPriceLineRef.current);
        }
      );
    }
  };

  return (
    <div className="h-full">
      <div ref={chartContainerRef} className={"TVChartContainer h-full"} />
    </div>
  );
});

TradingView.displayName = "TradingView";

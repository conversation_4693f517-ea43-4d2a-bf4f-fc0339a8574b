"use client";
import React, { useEffect, useRef, useState } from "react";
import { NumericFormat } from "react-number-format";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import {
  ChevronDownIcon,
  CloseIcon,
  EditSnipeIcon,
  RemoveSnipeIcon,
} from "@/assets/icons";
import { AppButton, AppCopy, AppDataTable } from "@/components";
import { getTokenNameFromTokenAddress } from "@/components/Snipe/helper";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastSuccess } from "@/libs/toast";
import { ModalBottomMobile } from "@/modals/ModalBottomMobile";
import { ModalRemoveSnipe } from "@/modals/ModalRemoveSnipe";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TSnipe } from "@/types/snipe.type";
import { DEXS, getDexLogoUrl, getDexName } from "@/utils/dex";
import { formatNumber, formatShortAddress } from "@/utils/format";
import { isZero, toStringBN } from "@/utils/helper";
import { AutoSellDetail } from "@/components/Snipe/auto-sell-details";
import Image from "next/image";
import { useNetwork } from "@/context";

interface SnipingListPartPropTypes {
  snipe: any;
  index: number;
}

interface ISnipeItem {
  buyAmount: string;
  slippage: string;
  tipAmount: string;
}

const ButtonEditSnipe = ({
  unit,
  snipe,
  onEditSnipe,
  snipeItem,
  setSnipeItem,
}: {
  unit: string;
  snipe: any;
  onEditSnipe: () => Promise<void>;
  snipeItem: ISnipeItem;
  setSnipeItem: (value: ISnipeItem) => void;
}) => {
  const [isShowModalEdit, setIsShowModalEdit] = useState<boolean>(false);

  const handleEditSnipe = async () => {
    await onEditSnipe().then(() => {
      setIsShowModalEdit(false);
    });
  };

  return (
    <>
      <button
        style={{
          boxShadow:
            "0px 0px 9px var(--0, 0px) var(--Brand-700, rgba(0, 204, 163, 0.60)), 0px 1px 2.3px 0px var(--Brand-300, #66FFE0) inset",
        }}
        className="bg-black-900 flex h-auto w-max items-center justify-center gap-1 rounded-[6px] p-[4px]"
      >
        <EditSnipeIcon
          className="cursor-pointer"
          onClick={() => setIsShowModalEdit(true)}
        />
      </button>

      {isShowModalEdit && (
        <ModalBottomMobile
          isOpen={isShowModalEdit}
          onClose={() => setIsShowModalEdit(false)}
        >
          <div className="w-full p-[16px]">
            <div className="flex items-center justify-between">
              <div>Edit Snipe</div>
              <div
                className="text-white-500 cursor-pointer"
                onClick={() => setIsShowModalEdit(false)}
              >
                <CloseIcon className="h-[12px] w-[12px]" />
              </div>
            </div>

            <div className="flex flex-col gap-[12px] py-[12px]">
              <div className="body-md-medium-14 text-white-800 border-white-50 flex h-[48px] cursor-pointer items-center justify-center rounded-[6px] border">
                <div className="text-white-800 border-white-50 flex-shrink-0 border-r px-[16px] text-[12px] font-normal leading-[18px]">
                  Snipe amount
                </div>
                <NumericFormat
                  value={snipeItem.buyAmount ?? snipe?.buyAmount}
                  allowLeadingZeros
                  allowNegative={false}
                  thousandSeparator=","
                  className={`h-full w-full bg-transparent px-[16px] text-left text-[14px] outline-none`}
                  decimalScale={8}
                  isAllowed={({ floatValue }) => {
                    return floatValue !== undefined && floatValue > 0;
                  }}
                  suffix={` ${unit}`}
                  onValueChange={({ floatValue }) => {
                    if (!isZero(floatValue)) {
                      return setSnipeItem({
                        ...snipeItem,
                        buyAmount: toStringBN(floatValue),
                      });
                    } else if (!floatValue) {
                      setSnipeItem({
                        ...snipeItem,
                        buyAmount: "0",
                      });
                    }
                  }}
                />
              </div>
              <div className="body-md-medium-14 text-white-800 border-white-50 flex h-[48px] cursor-pointer items-center justify-center rounded-[6px] border">
                <div className="text-white-800 border-white-50 flex-shrink-0 border-r px-[16px] text-[12px] font-normal leading-[18px]">
                  Slippage
                </div>
                <NumericFormat
                  value={snipeItem.slippage ?? snipe?.slippage}
                  allowLeadingZeros
                  allowNegative={false}
                  thousandSeparator=","
                  className={`h-full w-full bg-transparent px-[16px] text-left text-[14px] outline-none`}
                  decimalScale={2}
                  isAllowed={({ floatValue }) => {
                    return (
                      floatValue !== undefined &&
                      floatValue > 0 &&
                      floatValue <= 100
                    );
                  }}
                  suffix=" %"
                  onValueChange={({ floatValue }) => {
                    if (!isZero(floatValue)) {
                      return setSnipeItem({
                        ...snipeItem,
                        slippage: toStringBN(floatValue),
                      });
                    } else if (!floatValue) {
                      setSnipeItem({
                        ...snipeItem,
                        slippage: "0",
                      });
                    }
                  }}
                />
              </div>
            </div>
            <div className="grid w-full grid-cols-2 gap-2">
              <AppButton size="large" onClick={() => setIsShowModalEdit(false)}>
                Cancel
              </AppButton>
              <AppButton variant="buy" size="large" onClick={handleEditSnipe}>
                Apply
              </AppButton>
            </div>
          </div>
        </ModalBottomMobile>
      )}
    </>
  );
};
const SnipeItem = React.memo(
  ({ snipe, index }: SnipingListPartPropTypes) => {
    const unit = getTokenNameFromTokenAddress(snipe?.buyByToken);
    const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [isOpenModalRemove, setIsOpenModalRemove] = useState<boolean>(false);
    const [isShowDetails, setIsShowDetails] = useState<boolean>(false);
    const { currentNetwork } = useNetwork();

    const [snipeItem, setSnipeItem] = useState<ISnipeItem>({
      buyAmount: snipe?.buyAmount,
      slippage: snipe?.slippage,
      tipAmount: snipe?.tipAmount,
    });

    const { dexes: listDex } = useSelector(
      (state: RootState) => state.metadata
    );
    const objDexSelected = listDex?.find(
      (item) => item?.dex === snipe?.targetDex
    );

    const handleUpdateSnipeFunzone = async () => {
      try {
        await rf.getRequest("SnipeFunzoneRequest").updateSettings({
          network: currentNetwork,
          userWalletAddress: snipe.userWalletAddress,
          targetDex: snipe.targetDex || null,
          targetWalletAddress: snipe.targetWalletAddress || null,
          buyByToken: snipe.buyByToken || null,
          data: {
            buyAmount: +snipeItem.buyAmount,
            slippage: +snipeItem.slippage,
            tipAmount: +snipeItem.tipAmount,
            targetDex: snipe.targetDex || null,
            targetWalletAddress: snipe.targetWalletAddress || null,
            buyByToken: snipe.buyByToken || null,
          },
        });
        AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_SNIPING_LIST);
        setIsEdit(false);
        toastSuccess("Success", "Update snipe by dev wallet successfully!");
      } catch (error: any) {
        toastError("Error", error.message || "Something went wrong!");
      }
    };

    const handleSettingAutoSellSnipe = async () => {
      try {
        await rf.getRequest("SnipeFunzoneRequest").updateSettings({
          network: currentNetwork,
          userWalletAddress: snipe.userWalletAddress,
          targetDex: snipe.targetDex || null,
          targetWalletAddress: snipe.targetWalletAddress || null,
          buyByToken: snipe.buyByToken || null,
          data: {
            autoSellSettings: {
              ...snipe?.autoSellSettings,
              isActive: !snipe?.autoSellSettings?.isActive,
            },
          },
        });
        AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_SNIPING_LIST);
      } catch (error: any) {
        toastError("Error", error.message || "Something went wrong!");
      }
    };

    const handleDeleteSnipeFunzone = async () => {
      try {
        await rf.getRequest("SnipeFunzoneRequest").deleteSettings({
          network: currentNetwork,
          userWalletAddress: snipe.userWalletAddress,
          targetDex: snipe.targetDex || null,
          targetWalletAddress: snipe.targetWalletAddress || null,
          buyByToken: snipe.buyByToken || null,
        });
        AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_SNIPING_LIST);
        toastSuccess("Success", "Delete snipe by dev wallet successfully!");
        setIsOpenModalRemove(false);
      } catch (error: any) {
        toastError("Error", error.message || "Something went wrong!");
      }
    };

    if (isMobile) {
      return (
        <div className="border-white-150 bg-white-25 w-full rounded-[4px] border p-2.5">
          <div className="border-white-100 flex justify-between border-b border-dashed pb-2">
            <div className="flex flex-col gap-2.5">
              <div className="flex items-center gap-1">
                <Image
                  src={getDexLogoUrl(snipe?.targetDex as keyof typeof DEXS)}
                  className="h-[16px] w-[16px] rounded-full"
                  width={16}
                  height={16}
                  alt={getDexName(snipe?.targetDex as keyof typeof DEXS)}
                  unoptimized
                />
                <span className="">{objDexSelected?.name}</span>
              </div>
              <div className="bg-white-50 flex h-max items-center gap-1 rounded-[2px] px-1">
                <div className="text-white-500 text-[10px]">Dev Wallet:</div>
                <div className="text-white-900 flex items-center gap-1 text-[10px]">
                  {formatShortAddress(snipe.targetWalletAddress, 5, 3)}{" "}
                  <AppCopy message={snipe.targetWalletAddress} />
                </div>
              </div>
            </div>
            <div className="flex items-center justify-center">
              {formatNumber(snipeItem.buyAmount, 8, "0")} {unit}
            </div>
          </div>
          {isShowDetails && (
            <div className="border-white-50 mt-2 border-b border-dashed pb-2">
              <div className="flex items-center justify-between py-1">
                <div className="body-xs-regular-10 text-white-500">
                  Target Pair
                </div>
                <div className="flex items-center gap-1">
                  {`Token/${getTokenNameFromTokenAddress(
                    snipe?.targetTokenQuoteAddress
                  )}`}
                </div>
              </div>

              <div className="flex items-center justify-between py-1">
                <div className="body-xs-regular-10 text-white-500">
                  Tip Amount
                </div>
                <div className="flex items-center gap-1">
                  {formatNumber(snipeItem.tipAmount, 6)}
                </div>
              </div>

              <div className="flex items-center justify-between py-1">
                <div className="body-xs-regular-10 text-white-500">
                  Slippage
                </div>
                <div className="flex items-center gap-1">
                  {formatNumber(snipeItem.slippage, 2, "0")}%
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="body-xs-regular-10 text-white-500">Wallet</div>
                <div className="flex items-center gap-2">
                  <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                    {formatShortAddress(snipe?.userWalletAddress, 5, 3)}{" "}
                    <AppCopy message={snipe?.userWalletAddress} />
                  </div>
                </div>
              </div>
            </div>
          )}
          <div className="mt-[4px] flex justify-between">
            <div
              className="body-xs-medium-10 flex cursor-pointer items-center gap-[2px]"
              onClick={() => setIsShowDetails(!isShowDetails)}
            >
              {isShowDetails ? "Hide" : "More"} Detail{" "}
              <ChevronDownIcon
                className={`h-[12px] w-[12px] ${
                  isShowDetails ? "rotate-[180deg]" : ""
                }`}
              />
            </div>

            <div className="flex items-center gap-2.5">
              <div className="rounded-6 border-white-150 cursor-pointer border p-[4px] text-[12px]">
                <RemoveSnipeIcon
                  className="cursor-pointer"
                  onClick={() => setIsOpenModalRemove(true)}
                />
              </div>
              {isOpenModalRemove && (
                <ModalRemoveSnipe
                  isOpen={isOpenModalRemove}
                  onClose={() => setIsOpenModalRemove(false)}
                  onConfirm={handleDeleteSnipeFunzone}
                />
              )}
              <ButtonEditSnipe
                unit={unit}
                snipe={snipe}
                onEditSnipe={handleUpdateSnipeFunzone}
                snipeItem={snipeItem}
                setSnipeItem={setSnipeItem}
              />
            </div>
          </div>
        </div>
      );
    }

    const _renderContent = () => {
      return (
        <div className="flex w-full">
          <div className="td text-white-500 w-[3%] cursor-pointer justify-center">
            {index + 1}
          </div>

          <div className="td w-[14%]">
            <div className="flex items-center gap-1">
              <Image
                src={getDexLogoUrl(snipe?.targetDex as keyof typeof DEXS)}
                className="h-[16px] w-[16px] rounded-full"
                width={16}
                height={16}
                alt={getDexName(snipe?.targetDex as keyof typeof DEXS)}
                unoptimized
              />
              <span className="">{objDexSelected?.name}</span>
            </div>
          </div>

          <div className="td w-[14%] !justify-start">
            <div className="flex items-center gap-1">
              <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                {`Token/${getTokenNameFromTokenAddress(
                  snipe?.targetTokenQuoteAddress
                )}`}
              </div>
            </div>
          </div>

          <div className="td w-[11%] flex-col !items-start justify-center gap-0">
            <div className="flex items-center gap-1">
              <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                {formatShortAddress(snipe.targetWalletAddress, 5, 3)}{" "}
                <AppCopy message={snipe.targetWalletAddress} />
              </div>
            </div>
          </div>

          <div className="td w-[11%] flex-col !items-start justify-center gap-0">
            <div
              className={`flex items-center gap-1 ${
                isEdit ? "text-white-1000" : "text-white-500"
              }`}
            >
              {!isEdit ? (
                <>
                  {formatNumber(snipe?.buyAmount, 8, "0")} {unit}
                </>
              ) : (
                <NumericFormat
                  disabled={!isEdit}
                  value={snipe?.buyAmount}
                  allowLeadingZeros
                  allowNegative={false}
                  thousandSeparator=","
                  className={`body-sm-regular-12 w-full bg-transparent text-left outline-none`}
                  decimalScale={8}
                  isAllowed={({ floatValue }) => {
                    return floatValue !== undefined && floatValue > 0;
                  }}
                  suffix={` ${unit}`}
                  onValueChange={({ floatValue }) => {
                    if (!isZero(floatValue)) {
                      return setSnipeItem({
                        ...snipeItem,
                        buyAmount: toStringBN(floatValue),
                      });
                    } else if (!floatValue) {
                      setSnipeItem({
                        ...snipeItem,
                        buyAmount: "0",
                      });
                    }
                  }}
                />
              )}
            </div>
          </div>

          <div className="text-white-1000 td w-[10%] flex-col !items-start justify-center gap-0">
            <div
              className={`flex items-center gap-1 ${
                isEdit ? "text-white-1000" : "text-white-500"
              }`}
            >
              {!isEdit ? (
                <>{formatNumber(snipe?.tipAmount, 6, "0")} SUI</>
              ) : (
                <NumericFormat
                  disabled={!isEdit}
                  value={snipe?.tipAmount}
                  allowLeadingZeros
                  allowNegative={false}
                  thousandSeparator=","
                  className={`body-sm-regular-12 w-full bg-transparent text-left outline-none`}
                  decimalScale={6}
                  // isAllowed={({ floatValue }) => {
                  //   return (
                  //     floatValue !== undefined &&
                  //     floatValue > 0 &&
                  //     floatValue <= 100
                  //   );
                  // }}
                  onValueChange={({ floatValue }) => {
                    if (!isZero(floatValue)) {
                      return setSnipeItem({
                        ...snipeItem,
                        tipAmount: toStringBN(floatValue),
                      });
                    } else if (!floatValue) {
                      setSnipeItem({
                        ...snipeItem,
                        tipAmount: "0",
                      });
                    }
                  }}
                />
              )}
            </div>
          </div>

          <div className="text-white-1000 td w-[10%] flex-col !items-start justify-center gap-0">
            <div
              className={`flex items-center gap-1 ${
                isEdit ? "text-white-1000" : "text-white-500"
              }`}
            >
              {!isEdit ? (
                <>{formatNumber(snipe?.slippage, 2, "0")}%</>
              ) : (
                <NumericFormat
                  disabled={!isEdit}
                  value={snipe?.slippage}
                  allowLeadingZeros
                  allowNegative={false}
                  thousandSeparator=","
                  className={`body-sm-regular-12 w-full bg-transparent text-left outline-none`}
                  decimalScale={2}
                  suffix=" %"
                  isAllowed={({ floatValue }) => {
                    return (
                      floatValue !== undefined &&
                      floatValue > 0 &&
                      floatValue <= 100
                    );
                  }}
                  onValueChange={({ floatValue }) => {
                    if (!isZero(floatValue)) {
                      return setSnipeItem({
                        ...snipeItem,
                        slippage: toStringBN(floatValue),
                      });
                    } else if (!floatValue) {
                      setSnipeItem({
                        ...snipeItem,
                        slippage: "0",
                      });
                    }
                  }}
                />
              )}
            </div>
          </div>

          <div
            className={`td w-[11%] flex-col !items-start justify-center gap-0`}
          >
            <div className="flex min-w-[160px] items-center  gap-2">
              <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                {formatShortAddress(snipe?.userWalletAddress, 5, 3)}{" "}
                <AppCopy message={snipe?.userWalletAddress} />
              </div>
            </div>
          </div>

          <div className="td flex w-[16%] min-w-[160px] items-center gap-2">
            <div className="flex items-center gap-2.5">
              <AutoSellDetail
                onSetting={handleSettingAutoSellSnipe}
                isFunzone
                autoSellSettings={snipe?.autoSellSettings}
              />
              {!isEdit ? (
                <>
                  <RemoveSnipeIcon
                    className="cursor-pointer"
                    onClick={() => setIsOpenModalRemove(true)}
                  />
                  <EditSnipeIcon
                    className="cursor-pointer"
                    onClick={() => setIsEdit(!isEdit)}
                  />
                </>
              ) : (
                <>
                  <button
                    className="bg-white-100 rounded-4 text-white-1000 flex max-h-6 w-[56px] items-center justify-center p-[2px]"
                    onClick={() => setIsEdit(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-white-1000 rounded-4 text-black-900 flex max-h-6 w-[56px] items-center justify-center p-[2px]"
                    onClick={handleUpdateSnipeFunzone}
                  >
                    Save
                  </button>
                </>
              )}
            </div>
            {isOpenModalRemove && (
              <ModalRemoveSnipe
                isOpen={isOpenModalRemove}
                onClose={() => setIsOpenModalRemove(false)}
                onConfirm={handleDeleteSnipeFunzone}
              />
            )}
          </div>
        </div>
      );
    };

    return <>{_renderContent()}</>;
  },
  (prevProps, nextProps) => {
    return JSON.stringify(prevProps.snipe) === JSON.stringify(nextProps.snipe);
  }
);
SnipeItem.displayName = "SnipeItem";

const SnipingListPart = ({ heightContent }: { heightContent: number }) => {
  const dataTableRef = useRef<HTMLDivElement>(null);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const { currentNetwork } = useNetwork();

  const handleWhenRefreshData = () => {
    (dataTableRef.current as any)?.refresh();
  };
  useEffect(() => {
    AppBroadcast.on(
      BROADCAST_EVENTS.REFRESH_DATA_SNIPING_LIST,
      handleWhenRefreshData
    );
    AppBroadcast.on(BROADCAST_EVENTS.ORDER_UPDATED, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ORDER_UPDATED,
        handleWhenRefreshData
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.REFRESH_DATA_SNIPING_LIST,
        handleWhenRefreshData
      );
    };
  }, []);

  const getSnipeDevWalletOnFunzone = async (dataTableParams?: any) => {
    if (dataTableParams?.page > 1) return { data: [] };
    const res = await rf
      .getRequest("SnipeFunzoneRequest")
      .getListSettings(currentNetwork);
    return {
      data: res,
    };
  };

  if (!accessToken) {
    return (
      <div className="my-[100px] flex items-center justify-center">
        You need connect wallet to view this page
      </div>
    );
  }
  return (
    <div>
      {!isMobile && (
        <div
          className={`active-tab text-neutral-alpha-1000 border-neutral-alpha-50 flex h-[40px] w-full items-center gap-1 border-b px-4 py-3 text-[12px] font-semibold leading-[16px]`}
        >
          Sniping List
        </div>
      )}
      <AppDataTable
        isHideHeader={isMobile}
        ref={dataTableRef}
        minWidth={900}
        getData={getSnipeDevWalletOnFunzone as any}
        renderHeader={() => (
          <>
            <div className="thead w-[3%] justify-center">No</div>
            <div className="thead w-[14%]">Target Platform</div>
            <div className="thead w-[14%]">Target pair</div>
            <div className="thead w-[11%]">Dev Wallet</div>
            <div className="thead w-[11%]">Snipe amount</div>
            <div className="thead w-[10%]">Tip Amount</div>
            <div className="thead w-[10%]">Slippage</div>
            <div className="thead w-[11%]">Wallet</div>
            <div className="thead w-[16%] min-w-[160px]"></div>
          </>
        )}
        renderRow={(item: TSnipe, index: number) => {
          return <SnipeItem snipe={item} index={index} />;
        }}
        height={heightContent}
        overrideBodyClassName={
          isMobile ? "flex flex-col gap-2 mb-2 text-[12px]" : ""
        }
      />
    </div>
  );
};

export default SnipingListPart;

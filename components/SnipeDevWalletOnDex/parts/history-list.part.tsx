"use client";
import React, { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { ChevronDownIcon, LinkExternalIcon } from "@/assets/icons";
import { AppCopy, AppDataTable } from "@/components";
import { getTokenNameFromTokenAddress } from "@/components/Snipe/helper";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TSnipe } from "@/types/snipe.type";
import { DEXS, getDexLogoUrl, getDexName } from "@/utils/dex";
import { formatNumber, formatShortAddress } from "@/utils/format";
import { getLinkTxExplorer } from "@/utils/helper";
import isEqual from "lodash/isEqual";
import Image from "next/image";
import { useNetwork } from "@/context";

interface SnipingListPartPropTypes {
  snipe: any;
  index: number;
}
const SnipeItem = React.memo(
  ({ snipe, index }: SnipingListPartPropTypes) => {
    const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
    const [isShowDetails, setIsShowDetails] = useState<boolean>(false);
    const { dexes: listDex } = useSelector(
      (state: RootState) => state.metadata
    );
    const unit = getTokenNameFromTokenAddress(
      snipe?.originalSettings?.buyByToken
    );
    const unitMinLiquidity = getTokenNameFromTokenAddress(
      snipe?.originalSettings?.targetTokenQuoteAddress
    );
    const objDexSelected = listDex?.find(
      (item) => item?.dex === snipe?.originalSettings?.targetDex
    );
    const { currentNetwork } = useNetwork();

    if (isMobile) {
      return (
        <div className="border-white-150 bg-white-25 w-full rounded-[4px] border p-2.5">
          <div className="border-white-100 flex justify-between border-b border-dashed pb-2">
            <div className="flex flex-col gap-2.5">
              <div className="flex items-center gap-1">
                <Image
                  src={getDexLogoUrl(
                    snipe?.originalSettings?.targetDex as keyof typeof DEXS
                  )}
                  className="h-[16px] w-[16px] rounded-full"
                  width={16}
                  height={16}
                  alt={getDexName(
                    snipe?.originalSettings?.targetDex as keyof typeof DEXS
                  )}
                  unoptimized
                />
                <span className="">{objDexSelected?.name}</span>
              </div>
              <div className="bg-white-50 flex h-max items-center gap-1 rounded-[2px] px-1">
                <div className="text-white-500 text-[10px]">Dev Wallet:</div>
                <div className="text-white-900 flex items-center gap-1 text-[10px]">
                  {formatShortAddress(
                    snipe?.originalSettings?.targetWalletAddress,
                    5,
                    3
                  )}{" "}
                  <AppCopy
                    message={snipe?.originalSettings?.targetWalletAddress}
                  />
                </div>
              </div>
            </div>
            <div className="flex flex-col items-center justify-center gap-2">
              {formatNumber(snipe?.originalSettings?.buyAmount, 8, "0")} {unit}
              <div
                className={`rounded-4 flex items-center justify-center px-[4px] py-[1px] ${
                  snipe?.result?.status === "Success"
                    ? "bg-green-800 text-green-500"
                    : "bg-red-800 text-red-500"
                }`}
              >
                {snipe?.result?.status}
              </div>
            </div>
          </div>
          {isShowDetails && (
            <div className="border-white-50 mt-2 border-b border-dashed pb-2">
              <div className="flex items-center justify-between py-1">
                <div className="body-xs-regular-10 text-white-500">
                  Target Pair
                </div>
                <div className="flex items-center gap-1">
                  {`Token/${getTokenNameFromTokenAddress(
                    snipe?.originalSettings?.targetTokenQuoteAddress
                  )}`}
                </div>
              </div>

              <div className="flex items-center justify-between py-1">
                <div className="body-xs-regular-10 text-white-500">
                  Min liquidity
                </div>
                <div className="flex items-center gap-1">
                  {formatNumber(
                    snipe?.originalSettings?.minLiquidityByTokenQuote,
                    8,
                    "0"
                  )}{" "}
                  {unitMinLiquidity}
                </div>
              </div>
              <div className="flex items-center justify-between py-1">
                <div className="body-xs-regular-10 text-white-500">
                  Tip Amount
                </div>
                <div className="flex items-center gap-1">
                  {formatNumber(snipe?.originalSettings?.tipAmount, 6, "0")} SUI
                </div>
              </div>
              <div className="flex items-center justify-between py-1">
                <div className="body-xs-regular-10 text-white-500">
                  Slippage
                </div>
                <div className="flex items-center gap-1">
                  {formatNumber(snipe?.originalSettings?.slippage, 2, "0")}%
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="body-xs-regular-10 text-white-500">Wallet</div>
                <div className="flex items-center gap-2">
                  <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                    {formatShortAddress(
                      snipe?.originalSettings?.userWalletAddress,
                      5,
                      3
                    )}{" "}
                    <AppCopy
                      message={snipe?.originalSettings?.userWalletAddress}
                    />
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="body-xs-regular-10 text-white-500">Txn</div>
                <div className="flex items-center gap-2">
                  <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                    {formatShortAddress(snipe?.result?.txHash, 5, 3)}{" "}
                    <a
                      href={getLinkTxExplorer(
                        currentNetwork,
                        snipe?.result?.txHash
                      )}
                      className="text-blue-500"
                      target="_blank"
                    >
                      <LinkExternalIcon />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          )}
          <div className="mt-[4px] flex justify-between">
            <div
              className="body-xs-medium-10 flex cursor-pointer items-center gap-[2px]"
              onClick={() => setIsShowDetails(!isShowDetails)}
            >
              {isShowDetails ? "Hide" : "More"} Detail{" "}
              <ChevronDownIcon
                className={`h-[12px] w-[12px] ${
                  isShowDetails ? "rotate-[180deg]" : ""
                }`}
              />
            </div>
          </div>
        </div>
      );
    }

    const _renderContent = () => {
      return (
        <div className="flex w-full">
          <div className="td text-white-500 w-[3%] cursor-pointer justify-center">
            {index + 1}
          </div>

          <div className="td w-[10%]">
            <div className="flex items-center gap-1">
              <Image
                src={getDexLogoUrl(
                  snipe?.originalSettings?.targetDex as keyof typeof DEXS
                )}
                className="h-[16px] w-[16px] rounded-full"
                width={16}
                height={16}
                alt={getDexName(
                  snipe?.originalSettings?.targetDex as keyof typeof DEXS
                )}
                unoptimized
              />

              <span className="">{objDexSelected?.name}</span>
            </div>
          </div>

          <div className="td w-[10%]">
            <div className="flex items-center gap-1">
              <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                {`Token/${getTokenNameFromTokenAddress(snipe?.buyByToken)}`}
              </div>
            </div>
          </div>

          <div className="td w-[10%]">
            <div className="flex items-center gap-1">
              <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                {formatShortAddress(
                  snipe?.originalSettings?.targetWalletAddress,
                  5,
                  3
                )}{" "}
                <AppCopy
                  message={snipe?.originalSettings?.targetWalletAddress}
                />
              </div>
            </div>
          </div>

          <div className="td w-[10%]">
            <div className={`text-white-500 flex items-center gap-1`}>
              {formatNumber(
                snipe?.originalSettings?.minLiquidityByTokenQuote,
                8,
                "0"
              )}{" "}
              {unitMinLiquidity}
            </div>
          </div>

          <div className="td w-[10%]">
            <div className={`text-white-500 flex items-center gap-1`}>
              {formatNumber(snipe?.originalSettings?.buyAmount, 8, "0")} {unit}
            </div>
          </div>

          <div className="text-white-1000 td w-[10%]">
            <div className={`text-white-500 flex items-center gap-1`}>
              {formatNumber(snipe?.originalSettings?.tipAmount, 6, "0")} SUI
            </div>
          </div>

          <div className="text-white-1000 td w-[10%]">
            <div className={`text-white-500 flex items-center gap-1`}>
              {formatNumber(snipe?.originalSettings?.slippage, 2, "0")}%
            </div>
          </div>

          <div className={`td w-[10%]`}>
            <div className="flex min-w-[160px] items-center  gap-2">
              <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                {formatShortAddress(
                  snipe?.originalSettings?.userWalletAddress,
                  5,
                  3
                )}{" "}
                <AppCopy message={snipe?.originalSettings?.userWalletAddress} />
              </div>
            </div>
          </div>

          <div className="td flex w-[10%] items-center gap-2 ">
            <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
              {formatShortAddress(snipe?.result?.txHash, 5, 3)}{" "}
              <a
                href={getLinkTxExplorer(currentNetwork, snipe?.result?.txHash)}
                className="text-blue-500"
                target="_blank"
              >
                <LinkExternalIcon />
              </a>
            </div>
          </div>

          <div
            className={`td w-[7%] flex-col items-start justify-center gap-0`}
          >
            <div
              className={`rounded-4 flex items-center justify-center px-[4px] py-[1px] ${
                snipe?.result?.status === "Success"
                  ? "bg-green-800 text-green-500"
                  : "bg-red-800 text-red-500"
              }`}
            >
              {snipe?.result?.status}
            </div>
          </div>
        </div>
      );
    };

    return <>{_renderContent()}</>;
  },
  (prevProps, nextProps) => {
    return isEqual(prevProps.snipe, nextProps.snipe);
  }
);
SnipeItem.displayName = "SnipeItem";
const HistoryListPart = ({ heightContent }: { heightContent: number }) => {
  const dataTableRef = useRef<HTMLDivElement>(null);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const { currentNetwork } = useNetwork();

  const handleWhenRefreshData = () => {
    (dataTableRef.current as any)?.refresh();
  };
  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.ORDER_UPDATED, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ORDER_UPDATED,
        handleWhenRefreshData
      );
    };
  }, []);

  const getHistorySnipeDevWallet = async (dataTableParams?: any) => {
    if (dataTableParams?.page > 1) return { data: [] };
    const res = await rf
      .getRequest("SnipeDexRequest")
      .getHistoryListFilter(currentNetwork, {
        isSnipeDevWallet: true,
        isSnipeTokenAddress: false,
      });
    return {
      data: res,
    };
  };

  if (!accessToken) {
    return (
      <div className="my-[100px] flex items-center justify-center">
        You need connect wallet to view this page
      </div>
    );
  }
  return (
    <div>
      {!isMobile && (
        <div className="border-neutral-alpha-50 flex h-[40px] items-center justify-between border-b p-[12px]">
          <div className="text-neutral-alpha-1000 action-sm-semibold-12 text-[12px]">
            History
          </div>
        </div>
      )}
      <AppDataTable
        isHideHeader={isMobile}
        ref={dataTableRef}
        minWidth={1000}
        getData={getHistorySnipeDevWallet as any}
        renderHeader={() => (
          <>
            <div className="thead w-[3%] justify-center">No</div>
            <div className="thead w-[10%]">Target Dex</div>
            <div className="thead w-[10%]">Target pair</div>
            <div className="thead w-[10%]">Dev Wallet</div>
            <div className="thead w-[10%]">Min Liquidity</div>
            <div className="thead w-[10%]">Snipe amount</div>
            <div className="thead w-[10%]">Tip amount</div>
            <div className="thead w-[10%]">Slippage</div>
            <div className="thead w-[10%]">Wallet</div>
            <div className="thead w-[10%]">Txn</div>
            <div className="thead w-[7%]">Status</div>
          </>
        )}
        renderRow={(item: TSnipe, index: number) => {
          return <SnipeItem snipe={item} index={index} />;
        }}
        height={heightContent}
        overrideBodyClassName={
          isMobile ? "flex flex-col gap-2 mb-2 text-[12px]" : ""
        }
      />
    </div>
  );
};

export default HistoryListPart;

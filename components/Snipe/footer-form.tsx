import * as React from "react";
import {
  CoinTip,
  SettingsIcon,
  SlippageIcon,
  GasIcon,
  ChevronDownIcon,
} from "@/assets/icons";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { ButtonSignIn } from "@/components/ButtonSignIn";
import { useMemo } from "react";
import { useSelector } from "react-redux";
import { AppButton, AppToggle } from "@/components";
import { RootState } from "@/store";
import { SUI_ADDRESS, TARGET_TOKEN_CAN_BUY_MULTIPLE_TOKEN } from "./constant";
import { getTokenNameFromTokenAddress } from "./helper";
import { BigNumber } from "bignumber.js";
import { ITokenCanBuyMultipleToken } from "./type";
import { multipliedBN } from "@/utils/helper";
import { useTradingWallet } from "@/hooks/useTradingWallet";

export const FooterForm = ({
  snipeForm,
  onShowSettings,
  createOrder,
  balance,
  toggleSetAutoSell,
  autoSell,
  onShowSettingAutoSell,
}: {
  snipeForm: any;
  onShowSettings: () => void;
  createOrder: () => void;
  toggleSetAutoSell: () => void;
  onShowSettingAutoSell: () => void;
  autoSell: boolean;
  balance: any;
}) => {
  const settingsSnipeOrder = useSelector(
    (state: RootState) => state.user.settingsSnipeOrder
  );
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const { activeTradingWallets: activeSnipeWallets } = useTradingWallet(
    SUI_ADDRESS,
    snipeForm.buyByToken
  );

  const isShowMultipleToken = {
    canBuyMultipleToken: TARGET_TOKEN_CAN_BUY_MULTIPLE_TOKEN.some(
      (item: ITokenCanBuyMultipleToken) =>
        item?.tokenAddress === snipeForm.targetTokenQuoteAddress
    ),
    token: getTokenNameFromTokenAddress(snipeForm.buyByToken),
    tokenAddress: snipeForm.buyByToken,
  };

  const dontAllowBuyAmountZero = useMemo(
    () => (snipeForm.buyAmount ? +snipeForm.buyAmount === 0 : false),
    [snipeForm.buyAmount]
  );

  const totalAmount = useMemo(() => {
    const amount = new BigNumber(snipeForm.buyAmount || 0);
    // const tip = new BigNumber(settings?.tipAmount || 0);
    return amount.plus(0).decimalPlaces(8, BigNumber.ROUND_DOWN).toNumber();
  }, [snipeForm.buyAmount]);

  const isExceedBalance = useMemo(() => {
    if (!+balance) return true;
    return activeSnipeWallets.some((wallet) =>
      new BigNumber(multipliedBN(wallet.quoteBalance, 0.99)).lt(totalAmount)
    );
  }, [balance, activeSnipeWallets, totalAmount]);

  const _renderButton = () => {
    if (!accessToken) {
      return (
        <ButtonSignIn type={TRADE_TYPE.BUY} className="tablet:mt-2 !mt-0" />
      );
    }

    return (
      <AppButton
        variant="buy"
        className="tablet:mt-2 action-sm-medium-14 border-white-150 bg-brand-800 mt-0 flex h-[40px] flex-col items-center justify-center rounded-[8px] border px-2"
        onClick={createOrder}
        disabled={isExceedBalance || dontAllowBuyAmountZero}
      >
        <div className="text-brand-500 md:body-md-medium-14 body-sm-medium-12">
          Snipe Now{" "}
          {!!+snipeForm.buyAmount &&
            `${totalAmount} ${isShowMultipleToken.token}`}
        </div>
      </AppButton>
    );
  };

  return (
    <>
      <div className="mb-3 mt-6">
        <div className="flex items-center justify-between gap-2">
          <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center gap-1">
            <AppToggle value={autoSell} onChange={toggleSetAutoSell} />
            <div
              className="flex cursor-pointer items-center gap-1"
              onClick={onShowSettingAutoSell}
            >
              Auto Sell
              <ChevronDownIcon className="w-4 rotate-[-90deg]" />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <div className="text-neutral-alpha-500 body-sm-medium-12 flex w-max items-center gap-[4px]">
              <GasIcon />
              <div>{settingsSnipeOrder?.gasPrice || "--"}</div>
            </div>
            <div className="text-neutral-alpha-500 body-sm-medium-12 border-white-50 flex w-max items-center gap-[4px] border-l pl-2">
              <CoinTip />
              <div>{settingsSnipeOrder?.tipAmount || "--"}</div>
            </div>
            <div className="text-neutral-alpha-500 body-sm-medium-12 border-white-50 flex w-max items-center gap-[4px] border-x px-2">
              <SlippageIcon />
              <div>{settingsSnipeOrder?.slippage || "--"}%</div>
            </div>
            <div
              onClick={onShowSettings}
              className="border-neutral-alpha-500 tablet:p-0 tablet:border-none text-neutral-alpha-500 body-sm-medium-12 hover:text-neutral-alpha-1000 flex cursor-pointer gap-1 rounded-md border border-solid p-2"
            >
              <SettingsIcon className="h-5 w-5" />
            </div>
          </div>
        </div>
      </div>

      <div>{_renderButton()}</div>

      {isExceedBalance && !dontAllowBuyAmountZero && snipeForm.buyAmount && (
        <div className="body-xs-regular-10 mt-2 text-center text-yellow-500">
          Some of the selected wallets have insufficient balance to cover the
          buy amount, gas fee and tip
        </div>
      )}
      {dontAllowBuyAmountZero && (
        <div className="body-xs-regular-10 mt-2 text-center text-yellow-500">
          Please enter a valid amount
        </div>
      )}
    </>
  );
};

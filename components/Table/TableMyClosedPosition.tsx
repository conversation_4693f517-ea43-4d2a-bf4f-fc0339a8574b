"use client";
import React, { useEffect, useRef, useState } from "react";
import rf from "@/services/RequestFactory";
import { TPair } from "@/types/pair.type";
import {
  AppAvatarToken,
  AppCopy,
  AppDataTable,
  AppNumber,
  AppUserAddress,
} from "@/components";
import { TPosition } from "@/types";
import { RootState } from "@/store";
import { useSelector } from "react-redux";
import { formatToPercent, formatUsdNumber } from "@/utils/format";
import { useRouter } from "next/navigation";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { dividedBN, isGreaterBN, minusBN } from "@/utils/helper";
import { ModalFlex } from "@/modals";
import { ChevronDownIcon, SparklesIcon } from "@/assets/icons";
import { useMediaQuery } from "react-responsive";
import { useNetwork } from "@/context";
const ClosedPositionItem = React.memo(
  ({ position }: { position: TPosition }) => {
    const router = useRouter();
    const [isShowModalFlex, setIsShowModalFlex] = useState(false);
    const [isShowDetails, setIsShowDetails] = useState<boolean>(false);

    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

    const pnlToUsd = minusBN(position.volumeUsdSold, position.volumeUsdBought);
    const isPnlUp = isGreaterBN(pnlToUsd, 0);
    const pnlToPercent = dividedBN(pnlToUsd, position.volumeUsdBought);
    const { currentNetwork } = useNetwork();

    const onRedirectToDetails = async (pairId: string) => {
      if (!pairId) return;
      router.push(`/${currentNetwork}/${position?.slug}`);
    };

    const _renderContent = () => {
      if (isMobile) {
        return (
          <div className="border-white-50 bg-white-25 w-full rounded-[4px] border p-2.5">
            <div className="border-white-50 flex justify-between border-b border-dashed pb-2">
              <div className="flex gap-2.5">
                <AppAvatarToken
                  size={40}
                  image={
                    position?.tokenBase?.logoImageUrl ||
                    position?.tokenBase?.iconUrl ||
                    ""
                  }
                />
                <div>
                  <div
                    onClick={() => onRedirectToDetails(position?.pair || "")}
                    className="body-md-semibold-14 text-white-1000 cursor-pointer"
                  >
                    {position?.token?.symbol}
                  </div>
                  <div className="bg-white-50 flex h-max items-center gap-1 rounded-[2px] px-1">
                    <div className="body-2xs-regular-8 text-white-500">
                      Wallet:
                    </div>
                    <div className="flex items-center gap-1">
                      <AppUserAddress
                        network={currentNetwork}
                        address={position.walletName}
                        className="text-white-1000 body-xs-medium-10 w-max"
                      />
                      <AppCopy
                        message={position.walletName}
                        className="text-white-600 hover:text-white-1000"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <AppNumber
                  value={position?.pnlToUsd}
                  decimals={8}
                  isForUSD
                  className="body-md-medium-14 text-right"
                />
                <div
                  className={`body-xs-regular-10 text-right ${
                    isPnlUp ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {formatToPercent(position?.pnlToPercent || 0)}
                </div>
              </div>
            </div>
            {isShowDetails && (
              <div className="border-white-50 mt-2 border-b border-dashed pb-2">
                <div className="flex items-center justify-between py-1">
                  <div className="body-xs-regular-10 text-white-500">
                    Invested
                  </div>
                  <div className="flex items-center gap-1">
                    <AppNumber
                      value={position?.volumeUsdBought}
                      isForUSD
                      className="body-xs-medium-10"
                    />
                    {/*<div className="flex body-xs-regular-10 text-white-600">*/}
                    {/*  (<AppNumber value={position?.quoteAmountBought} />*/}
                    {/*  <span className="pl-[2px]">SUI</span> )*/}
                    {/*</div>*/}
                  </div>
                </div>
                <div className="flex items-center justify-between py-1">
                  <div className="body-xs-regular-10 text-white-500">Sold</div>
                  <div className="flex items-center gap-1">
                    <AppNumber
                      value={position?.volumeUsdSold}
                      isForUSD
                      className="body-xs-medium-10"
                    />
                    {/*<div className="flex body-xs-regular-10 text-white-600">*/}
                    {/*  (<AppNumber value={position?.quoteAmountSold} />*/}
                    {/*  <span className="pl-[2px]">SUI</span> )*/}
                    {/*</div>*/}
                  </div>
                </div>
              </div>
            )}
            <div className="mt-[8px] flex justify-between">
              <div
                className="body-xs-medium-10 flex cursor-pointer items-center gap-[2px]"
                onClick={() => setIsShowDetails(!isShowDetails)}
              >
                {isShowDetails ? "Hide" : "More"} Detail{" "}
                <ChevronDownIcon
                  className={`h-[12px] w-[12px] ${
                    isShowDetails ? "rotate-[180deg]" : ""
                  }`}
                />
              </div>

              <div className="flex gap-2">
                <button
                  style={{
                    boxShadow:
                      "0px 0px 9px var(--0, 0px) var(--Brand-700, rgba(0, 204, 163, 0.60)), 0px 1px 2.3px 0px var(--Brand-300, #66FFE0) inset",
                  }}
                  className="bg-black-900 flex h-[32px] w-[70px] items-center justify-center gap-1 rounded-[6px]"
                  onClick={() => setIsShowModalFlex(true)}
                >
                  <SparklesIcon className="text-white-1000 h-3 w-3" />
                  <span className="action-xs-medium-12 text-white-1000">
                    Flex
                  </span>
                </button>
              </div>
            </div>
          </div>
        );
      }
      return (
        <div className="flex w-full">
          <div className="td w-[20%] gap-2 pl-[20px]">
            <AppAvatarToken
              size={40}
              image={
                position?.tokenBase?.logoImageUrl ||
                position?.tokenBase?.iconUrl ||
                ""
              }
            />
            <div
              onClick={() => onRedirectToDetails(position?.pair || "")}
              className="text-white-1000 cursor-pointer text-[12px] font-normal leading-[18px]"
            >
              {position?.token?.symbol}
            </div>
          </div>
          <div className="td w-[12%]">
            <AppUserAddress
              network={currentNetwork}
              address={position.walletName}
              className="text-white-1000"
            />
            <AppCopy
              message={position.walletName}
              className="text-white-600 hover:text-white-1000"
            />
          </div>

          <div className="td w-[19%] flex-col !items-start justify-center gap-0">
            <div className="text-green-500">
              <AppNumber
                value={position?.volumeUsdBought}
                isForUSD
                className="body-sm-regular-12"
              />
            </div>
            {/*<div className="body-xs-regular-10 text-white-500 flex gap-1 items-center">*/}
            {/*  {position && !!+position?.quoteAmountBought && (*/}
            {/*    <AppLogoNetwork*/}
            {/*      network={network}*/}
            {/*      className="w-[12px] h-[12px]"*/}
            {/*      isBase*/}
            {/*    />*/}
            {/*  )}*/}
            {/*  <AppNumber value={position?.quoteAmountBought} />*/}
            {/*</div>*/}
          </div>

          <div className="td w-[19%] flex-col !items-start justify-center gap-0">
            <div className="text-red-500">
              <AppNumber
                value={position?.volumeUsdSold}
                isForUSD
                className="body-sm-regular-12"
              />
            </div>
            {/*<div className="body-xs-regular-10 text-white-500 flex gap-1 items-center">*/}
            {/*  {position && !!position?.quoteAmountSold && (*/}
            {/*    <AppLogoNetwork*/}
            {/*      network={network}*/}
            {/*      className="w-[12px] h-[12px]"*/}
            {/*      isBase*/}
            {/*    />*/}
            {/*  )}*/}
            {/*  <AppNumber value={position?.quoteAmountSold} />*/}
            {/*</div>*/}
          </div>

          <div
            className={`td w-[19%] flex-col !items-start justify-center !gap-0 ${
              isPnlUp ? "text-green-500" : "text-red-500"
            }`}
          >
            <AppNumber
              value={pnlToUsd}
              decimals={8}
              isForUSD
              className="body-sm-regular-12"
            />
            <div
              className={`body-xs-regular-10 flex items-center gap-1 ${
                isPnlUp ? "text-green-600" : "text-red-600"
              }`}
            >
              {formatToPercent(pnlToPercent || 0)}
            </div>
          </div>
          <div className="td flex w-[11%] justify-start">
            <button
              style={{
                boxShadow:
                  "0px 0px 9px var(--0, 0px) var(--Brand-700, rgba(0, 204, 163, 0.60)), 0px 1px 2.3px 0px var(--Brand-300, #66FFE0) inset",
              }}
              className="bg-black-900 inline-flex h-auto items-center gap-1 rounded-[4px] px-2.5 py-1.5"
              onClick={() => setIsShowModalFlex(true)}
            >
              <SparklesIcon className="text-white-1000 h-3 w-3" />
              <span className="text-white-1000 text-[10px] font-medium leading-[14px]">
                Flex
              </span>
            </button>
          </div>
        </div>
      );
    };

    return (
      <>
        {_renderContent()}
        {isShowModalFlex && (
          <ModalFlex
            isOpen={isShowModalFlex}
            onClose={() => setIsShowModalFlex(false)}
            data={{
              isPnlUp: isPnlUp,
              isClosed: true,
              token: position?.token?.symbol || "",
              pairId: position?.pair || "",
              baseAmountBought: position?.baseAmountBought || "",
              volumeUsdBought: position?.volumeUsdBought || "",
              volumeUsdSold: position?.volumeUsdSold || "",
              baseAmountSold: position?.baseAmountSold || "",
              currentPnLPercent: formatToPercent(pnlToPercent || 0),
              currentPnLValueUsd: formatUsdNumber(pnlToUsd || 0),
              totalInvested: formatUsdNumber(position?.volumeUsdBought || "0"),
              totalSold: formatUsdNumber(position?.volumeUsdSold || 0),
              tokenAddress: position?.token?.address || "",
            }}
          />
        )}
      </>
    );
  },
  (prevProps, nextProps) => {
    return (
      JSON.stringify(prevProps.position) === JSON.stringify(nextProps.position)
    );
  }
);

ClosedPositionItem.displayName = "ClosedPositionItem";

export const TableMyClosedPosition = ({
  heightContent,
  pair,
}: {
  heightContent: number;
  pair?: TPair;
}) => {
  const dataTableRef = useRef<HTMLDivElement>(null);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const { currentNetwork } = useNetwork();

  const getMyClosedPositions = async (dataTableParams: any) => {
    const paramsFilter = {
      ...dataTableParams,
      closed: true,
    };

    const res = await rf
      .getRequest("MyPositionRequest")
      .getMyPositions(currentNetwork, paramsFilter);

    const positions = (res?.docs || [])?.map((position: TPosition) => {
      const pnlToUsd = minusBN(
        position.volumeUsdSold,
        position.volumeUsdBought
      );
      const pnlToPercent = dividedBN(pnlToUsd, position.volumeUsdBought);

      return {
        ...position,
        pnlToUsd,
        pnlToPercent,
      };
    });

    const sortedPositions = positions.sort((a: TPosition, b: TPosition) => {
      return Number(b.pnlToUsd) - Number(a.pnlToUsd);
    });

    return {
      data: sortedPositions,
    };
  };

  const handleWhenRefreshData = (event: TBroadcastEvent) => {
    (dataTableRef.current as any)?.refresh();
  };

  useEffect(() => {
    if (!accessToken) return;
    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    };
  }, [accessToken]);

  if (!accessToken) {
    return (
      <>
        <div></div>
      </>
    );
  }

  return (
    <AppDataTable
      isHideHeader={isMobile}
      ref={dataTableRef}
      getData={getMyClosedPositions}
      renderHeader={() => (
        <>
          <div className="thead w-[20%] pl-[30px]">Token</div>
          <div className="thead w-[12%]">Wallet</div>
          <div className="thead w-[19%]">Invested</div>
          <div className="thead w-[19%]">Sold</div>
          <div className="thead w-[19%]">Total PnL</div>
          <div className="thead w-[11%]"></div>
        </>
      )}
      renderRow={(item: TPosition, index: number) => {
        return <ClosedPositionItem key={index} position={item} />;
      }}
      height={heightContent}
      overrideBodyClassName={isMobile ? "flex flex-col gap-2 mb-2" : ""}
    />
  );
};

"use client";

import BigNumber from "bignumber.js";
import React, { useEffect, useRef, useState } from "react";
import { NumericFormat } from "react-number-format";
import { useSelector } from "react-redux";
import {
  ChevronDownIcon,
  EyeHideIcon,
  PinIcon,
  SparklesIcon,
} from "@/assets/icons";
import {
  AppAvatarToken,
  AppAvatarTokenQuote,
  // AppButton,
  AppCopy,
  AppDataTable,
  // AppLogoNetwork,
  AppNumber,
  AppPopover,
  AppUserAddress,
} from "@/components";
import { useOrder } from "@/hooks";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TPosition } from "@/types";
import { TPair } from "@/types/pair.type";
import { formatToPercent, formatUsdNumber } from "@/utils/format";
// import { TradingType } from '@/enums';
import { useMediaQuery } from "react-responsive";
import { useRouter } from "next/navigation";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import Storage from "@/libs/storage";
import { toastError, toastInfo } from "@/libs/toast";
import { ModalClosePosition } from "@/modals";
import { ModalFlex } from "@/modals/ModalFlex";
import { dividedBN, minusBN } from "@/utils/helper";
import { NETWORKS } from "@/utils/contants";
import { getDefaultUseAggregator } from "@/utils/dex";
import { useNetwork } from "@/context";
const DEFAULT_CLOSE_PERCENT = [10, 25, 50, 75, 100];

const ButtonCloseMobile = ({
  closeAmount,
  onClosePositions,
  setCloseAmount,
}: {
  closeAmount: number;
  onClosePositions: () => Promise<any>;
  setCloseAmount: (value: number) => void;
}) => {
  const [isShowModalClose, setIsShowModalClose] = useState<boolean>(false);

  const handleClosePosition = async () => {
    onClosePositions().then(() => {
      setIsShowModalClose(false);
    });
  };

  return (
    <>
      <div
        onClick={() => setIsShowModalClose(true)}
        className="text-brand-500 action-xs-medium-12 border-brand-800 flex cursor-pointer items-center gap-1 rounded-[6px] border px-[16px] py-[8px] "
      >
        Close <ChevronDownIcon className="h-[12px] w-[12px]" />
      </div>

      {isShowModalClose && (
        <ModalClosePosition
          closeAmount={closeAmount}
          onClosePositions={handleClosePosition}
          setCloseAmount={setCloseAmount}
          isOpen={isShowModalClose}
          onClose={() => setIsShowModalClose(false)}
        />
      )}
    </>
  );
};

const PositionItem = React.memo(
  ({ position, pair }: { position: TPosition; pair?: TPair }) => {
    const router = useRouter();
    const { closePosition } = useOrder();

    const [closeAmount, setCloseAmount] = useState(100);
    const [isOpenOptions, setIsOpenOptions] = useState(false);
    const [isShowModalFlex, setIsShowModalFlex] = useState(false);
    const [isShowDetails, setIsShowDetails] = useState<boolean>(false);
    const { currentNetwork } = useNetwork();

    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

    const isPnlUp = new BigNumber(position?.pnlToUsd || 0).isGreaterThan(0);

    const onClosePosition = async () => {
      await closePosition(
        closeAmount,
        position?.token?.address,
        position.walletName,
        getDefaultUseAggregator(position?.dex as string)
      );
    };

    const handleShowPosition = async () => {
      try {
        await rf
          .getRequest("MyPositionRequest")
          .showPosition(currentNetwork, position?.token?.address);
        AppBroadcast.dispatch(
          BROADCAST_EVENTS.SHOW_POSITION,
          position?.token?.address
        );
        toastInfo("Success", "Show position successfully!");
      } catch (error: any) {
        console.error(error);
        toastError("Error", error.message || "Something went wrong!");
      }
    };

    const onRedirectToDetails = async (pairId: string) => {
      if (!pairId) return;
      router.push(`/${currentNetwork}/${position?.slug}`);
    };

    if (new BigNumber(position?.balance || 0).isZero()) {
      return <></>;
    }

    const _renderContent = () => {
      if (isMobile) {
        return (
          <div className="border-white-50 bg-white-25 w-full rounded-[4px] border p-2.5">
            <div className="border-white-50 flex justify-between border-b border-dashed pb-2">
              <div className="flex gap-2.5">
                <AppAvatarToken
                  size={40}
                  image={
                    position?.tokenBase?.logoImageUrl ||
                    position?.tokenBase?.iconUrl ||
                    ""
                  }
                />
                <div>
                  <div
                    onClick={() => onRedirectToDetails(position?.pair || "")}
                    className="body-md-semibold-14 text-white-1000 cursor-pointer"
                  >
                    {position?.token?.symbol}
                  </div>
                  <div className="bg-white-50 flex h-max items-center gap-1 rounded-[2px] px-1">
                    <div className="body-2xs-regular-8 text-white-500">
                      Wallet:
                    </div>
                    <div className="flex items-center gap-1">
                      <AppUserAddress
                        network={currentNetwork}
                        address={position.walletName}
                        className="text-white-1000 body-xs-medium-10 w-max"
                      />
                      <AppCopy
                        message={position.walletName}
                        className="text-white-600 hover:text-white-100"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <AppNumber
                  value={position?.pnlToUsd}
                  decimals={8}
                  isForUSD
                  className="body-md-medium-14 text-right"
                />
                <div
                  className={`body-xs-regular-10 text-right ${
                    isPnlUp ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {formatToPercent(position?.pnlToPercent || 0)}
                </div>
              </div>
            </div>
            {isShowDetails && (
              <div className="border-white-50 mt-2 border-b border-dashed pb-2">
                <div className="flex items-center justify-between py-1">
                  <div className="body-xs-regular-10 text-white-500">
                    Invested
                  </div>
                  <div className="flex items-center gap-1">
                    <AppNumber
                      value={position?.volumeUsdBought}
                      isForUSD
                      className="body-xs-medium-10"
                    />
                    {/*<div className="flex body-xs-regular-10 text-white-600">*/}
                    {/*  (<AppNumber value={position?.quoteAmountBought} />*/}
                    {/*  <span className="pl-[2px]">SUI</span> )*/}
                    {/*</div>*/}
                  </div>
                </div>
                <div className="flex items-center justify-between py-1">
                  <div className="body-xs-regular-10 text-white-500">Sold</div>
                  <div className="flex items-center gap-1">
                    <AppNumber
                      value={position?.volumeUsdSold}
                      isForUSD
                      className="body-xs-medium-10"
                    />
                    {/*<div className="flex body-xs-regular-10 text-white-600">*/}
                    {/*  (<AppNumber value={position?.quoteAmountSold} />*/}
                    {/*  <span className="pl-[2px]">SUI</span> )*/}
                    {/*</div>*/}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="body-xs-regular-10 text-white-500">
                    Remaining
                  </div>
                  <div className="flex items-center gap-1">
                    <AppNumber
                      value={position?.balanceToUsd}
                      isForUSD
                      className="body-xs-medium-10"
                    />
                    <div className="body-xs-regular-10 text-white-600 flex">
                      (<AppNumber value={position?.balanceToSui} />
                      <span className="pl-[2px]">SUI</span> )
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="mt-[8px] flex justify-between">
              <div
                className="body-xs-medium-10 flex cursor-pointer items-center gap-[2px]"
                onClick={() => setIsShowDetails(!isShowDetails)}
              >
                {isShowDetails ? "Hide" : "More"} Detail{" "}
                <ChevronDownIcon
                  className={`h-[12px] w-[12px] ${
                    isShowDetails ? "rotate-[180deg]" : ""
                  }`}
                />
              </div>

              <div className="flex gap-2">
                {/*<AppButton variant="outline">*/}
                {/*  <EyeShowIcon className="w-5 h-5"/>*/}
                {/*</AppButton>*/}
                <div
                  className="rounded-6 border-white-150 cursor-pointer border px-[16px] py-[8px] text-[12px]"
                  onClick={handleShowPosition}
                >
                  Show
                </div>
                <button
                  style={{
                    boxShadow:
                      "0px 0px 9px var(--0, 0px) var(--Brand-700, rgba(0, 204, 163, 0.60)), 0px 1px 2.3px 0px var(--Brand-300, #66FFE0) inset",
                  }}
                  className="bg-black-900 flex h-auto w-[70px] items-center justify-center gap-1 rounded-[6px]"
                  onClick={() => setIsShowModalFlex(true)}
                >
                  <SparklesIcon className="text-white-1000 h-3 w-3" />
                  <span className="action-xs-medium-12 text-white-1000">
                    Flex
                  </span>
                </button>
                <ButtonCloseMobile
                  closeAmount={closeAmount}
                  onClosePositions={onClosePosition}
                  setCloseAmount={setCloseAmount}
                />
              </div>
            </div>
          </div>
        );
      }

      return (
        <div className="flex w-full">
          <div
            className="td w-[5%] cursor-pointer justify-center"
            onClick={handleShowPosition}
          >
            <EyeHideIcon className="text-white-800 h-[16px] w-[16px] " />
          </div>
          <div className="td w-[13%] min-w-[120px] gap-2">
            {position?.pair === pair?.pairId && (
              <div className="w-[12px]">
                <PinIcon />
              </div>
            )}
            <AppAvatarToken
              size={40}
              image={
                position?.tokenBase?.logoImageUrl ||
                position?.tokenBase?.iconUrl ||
                ""
              }
            />
            <div
              onClick={() => onRedirectToDetails(position?.pair || "")}
              className="text-white-1000 cursor-pointer text-[12px] font-normal leading-[18px]"
            >
              {position?.token?.symbol}
            </div>
          </div>
          <div className="td w-[12%]">
            <AppUserAddress
              network={currentNetwork}
              address={position.walletName}
              className="text-white-1000"
            />
            <AppCopy
              message={position.walletName}
              className="text-white-600 hover:text-white-1000"
            />
          </div>

          <div className="td w-[13%] flex-col !items-start justify-center gap-0">
            <div className="text-green-500">
              <AppNumber
                value={position?.volumeUsdBought}
                isForUSD
                className="body-sm-regular-12"
              />
            </div>
            {/*<div className="body-xs-regular-10 text-white-500 flex gap-1 items-center">*/}
            {/*  {position && !!+position?.quoteAmountBought && (*/}
            {/*    <AppLogoNetwork*/}
            {/*      network={network}*/}
            {/*      className="w-[12px] h-[12px]"*/}
            {/*      isBase*/}
            {/*    />*/}
            {/*  )}*/}
            {/*  <AppNumber value={position?.quoteAmountBought} />*/}
            {/*</div>*/}
          </div>

          <div className="td w-[13%] flex-col !items-start justify-center gap-0">
            <div className="text-red-500">
              <AppNumber
                value={position?.volumeUsdSold}
                isForUSD
                className="body-sm-regular-12"
              />
            </div>
            {/*<div className="body-xs-regular-10 text-white-500 flex gap-1 items-center">*/}
            {/*  {position && !!position?.quoteAmountSold && (*/}
            {/*    <AppLogoNetwork*/}
            {/*      network={network}*/}
            {/*      className="w-[12px] h-[12px]"*/}
            {/*      isBase*/}
            {/*    />*/}
            {/*  )}*/}
            {/*  <AppNumber value={position?.quoteAmountSold} />*/}
            {/*</div>*/}
          </div>

          <div className="text-white-1000 td w-[13%] flex-col !items-start justify-center !gap-0">
            <div>
              <AppNumber
                value={position?.balanceToUsd}
                isForUSD
                className="body-sm-regular-12"
              />
            </div>
            <div className="body-xs-regular-10 text-white-500 flex items-center gap-1">
              {!!Number(position?.balanceToQuote) && (
                <AppAvatarTokenQuote pair={position} size={12} />
              )}
              <AppNumber value={position?.balanceToQuote} />
            </div>
          </div>

          <div
            className={`td w-[13%] flex-col !items-start justify-center !gap-0 ${
              isPnlUp ? "text-green-500" : "text-red-500"
            }`}
          >
            <AppNumber
              value={position?.pnlToUsd}
              decimals={8}
              isForUSD
              className="body-sm-regular-12"
            />
            <div
              className={`body-xs-regular-10 flex items-center gap-1 ${
                isPnlUp ? "text-green-600" : "text-red-600"
              }`}
            >
              {formatToPercent(position?.pnlToPercent || 0)}
            </div>
          </div>

          <div className="td flex w-[15%] min-w-[150px] items-center gap-2">
            <AppPopover
              isOpen={isOpenOptions}
              onToggle={() => {
                setIsOpenOptions(!isOpenOptions);
              }}
              onClose={() => setIsOpenOptions(false)}
              trigger={
                <div className="bg-white-50 border-white-50 flex cursor-pointer items-center gap-2 rounded-[6px] border p-[8px]">
                  <NumericFormat
                    placeholder={`Percent close`}
                    value={closeAmount ?? ""}
                    allowLeadingZeros
                    allowNegative={false}
                    thousandSeparator=","
                    suffix={"%"}
                    className="body-sm-regular-12 w-[40px] border-0 bg-transparent outline-none"
                    decimalScale={2}
                    onValueChange={({ floatValue }) => {
                      return setCloseAmount(floatValue || 0);
                    }}
                  />
                  <ChevronDownIcon
                    className={`duration-400 h-4 w-4 ${
                      isOpenOptions ? "rotate-[180deg]" : ""
                    }`}
                  />
                </div>
              }
              content={
                <div className="flex gap-[4px] rounded-[8px] bg-[#212224] p-[4px]">
                  {DEFAULT_CLOSE_PERCENT.map((item, index) => {
                    return (
                      <div
                        onClick={() => {
                          setCloseAmount(item);
                          setIsOpenOptions(false);
                        }}
                        key={index}
                        className="border-white-50 hover:bg-white-50 hover:border-white-100 body-sm-regular-12 flex h-[36px] cursor-pointer items-center rounded-[6px] border px-[8px] py-[4px]"
                      >
                        {item}%
                      </div>
                    );
                  })}
                </div>
              }
              position="left"
            />

            <div
              className="text-brand-500 hover:text-brand-600 cursor-pointer text-[10px] font-semibold leading-[14px]"
              onClick={(e) => {
                e.stopPropagation();
                onClosePosition().then();
              }}
            >
              Close
            </div>
          </div>
          <div className="td flex w-[8%] items-center justify-start">
            <button
              style={{
                boxShadow:
                  "0px 0px 9px var(--0, 0px) var(--Brand-700, rgba(0, 204, 163, 0.60)), 0px 1px 2.3px 0px var(--Brand-300, #66FFE0) inset",
              }}
              className="bg-black-900 inline-flex h-auto items-center gap-1 rounded-[4px] px-2.5 py-1.5"
              onClick={() => setIsShowModalFlex(true)}
            >
              <SparklesIcon className="text-white-1000 h-3 w-3" />
              <span className="text-white-1000 text-[10px] font-medium leading-[14px]">
                Flex
              </span>
            </button>
          </div>
        </div>
      );
    };

    return (
      <>
        {_renderContent()}

        {isShowModalFlex && (
          <ModalFlex
            isOpen={isShowModalFlex}
            onClose={() => setIsShowModalFlex(false)}
            data={{
              isPnlUp: isPnlUp,
              token: position?.token?.symbol || "",
              pairId: position?.pair || "",
              baseAmountBought: position?.baseAmountBought || "",
              volumeUsdBought: position?.volumeUsdBought || "",
              volumeUsdSold: position?.volumeUsdSold || "",
              baseAmountSold: position?.baseAmountSold || "",
              currentPnLPercent: formatToPercent(position?.pnlToPercent || 0),
              currentPnLValueUsd: formatUsdNumber(position?.pnlToUsd || 0),
              totalInvested: formatUsdNumber(position?.volumeUsdBought || "0"),
              totalSold: formatUsdNumber(position?.volumeUsdSold || 0),
              tokenAddress: position?.token?.address || "",
            }}
          />
        )}
      </>
    );
  },
  (prevProps, nextProps) => {
    return (
      JSON.stringify(prevProps.position) === JSON.stringify(nextProps.position)
    );
  }
);
PositionItem.displayName = "PositionItem";

type TableMyHiddenPositionsPropTypes = {
  heightContent: number | string;
  pair?: TPair;
  isHideOtherPair?: boolean;
};

export const TableMyHiddenPositions = (
  props: TableMyHiddenPositionsPropTypes
) => {
  const { heightContent, pair } = props;
  const { currentNetwork } = useNetwork();

  const dataTableRef = useRef<HTMLDivElement>(null);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const initialHideOtherTokens = Storage.getIsHideOtherTokenForPosition();
  const [isHideOtherTokens, setIsHideOtherTokens] = useState(
    initialHideOtherTokens
  );

  const isHideOtherTokensRef = useRef<boolean>(isHideOtherTokens);

  const getMyHiddenPositions = async (dataTableParams: any) => {
    const paramsFilter = {
      ...dataTableParams,
      isHidden: true,
    };

    const res = await rf
      .getRequest("MyPositionRequest")
      .getMyPositions(currentNetwork, paramsFilter);

    const positions = (res?.docs || [])?.map((position: TPosition) => {
      const pnlToUsd = minusBN(
        position.volumeUsdSold,
        position.volumeUsdBought
      );
      const pnlToPercent = dividedBN(pnlToUsd, position.volumeUsdBought);

      return {
        ...position,
        pnlToUsd,
        pnlToPercent,
      };
    });

    const sortedPositions = positions.sort((a: TPosition, b: TPosition) => {
      return Number(b.pnlToUsd) - Number(a.pnlToUsd);
    });

    return {
      data: sortedPositions,
    };
  };

  useEffect(() => {
    if (!accessToken || props?.isHideOtherPair === undefined) return;
    isHideOtherTokensRef.current = !!props?.isHideOtherPair;
    setIsHideOtherTokens(isHideOtherTokensRef.current);
  }, [accessToken, props?.isHideOtherPair]);

  const handleWhenRefreshData = (event: TBroadcastEvent) => {
    (dataTableRef.current as any)?.refresh();
  };

  useEffect(() => {
    if (!accessToken) return;
    AppBroadcast.on(BROADCAST_EVENTS.SHOW_POSITION, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.SHOW_POSITION,
        handleWhenRefreshData
      );
    };
  }, [accessToken]);

  const hideOtherTokens = () => {
    isHideOtherTokensRef.current = !isHideOtherTokensRef.current;
    Storage.setIsHideOtherTokenForPosition(isHideOtherTokensRef.current);
    setIsHideOtherTokens(isHideOtherTokensRef.current);
  };

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  if (!accessToken) {
    return (
      <>
        <div></div>
      </>
    );
  }

  return (
    <AppDataTable
      isHideHeader={isMobile}
      ref={dataTableRef}
      minWidth={790}
      getData={getMyHiddenPositions as any}
      renderHeader={() => (
        <>
          <div className="thead w-[5%]" />
          <div className="thead w-[13%] min-w-[120px]">Token</div>
          <div className="thead w-[12%]">Wallet</div>
          <div className="thead w-[13%]">Invested</div>
          <div className="thead w-[13%]">Sold</div>
          <div className="thead w-[13%]">Remaining</div>
          <div className="thead w-[13%]">Total PnL</div>
          <div className="thead text-brand-500 w-[15%] min-w-[150px]">
            {pair && (
              <button className="cursor-pointer" onClick={hideOtherTokens}>
                {isHideOtherTokens ? "Show Other Tokens" : "Hide Other Tokens"}
              </button>
            )}
          </div>
          <div className="thead w-[8%]"></div>
        </>
      )}
      renderRow={(item: TPosition, index: number) => {
        return <PositionItem key={index} position={item} pair={pair} />;
      }}
      height={heightContent}
      overrideBodyClassName={isMobile ? "flex flex-col gap-2 mb-2" : ""}
    />
  );
};

TableMyHiddenPositions.displayName = "TableMyHiddenPositions";

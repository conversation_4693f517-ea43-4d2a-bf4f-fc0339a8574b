"use client";

import React, { useState, useRef, useEffect } from "react";
import { ChevronDownIcon } from "@/assets/icons";
import { AppLogoNetwork } from "./AppLogoNetwork";
import { NETWORKS } from "@/utils/contants";
import { useNetwork } from "@/context/network";

const NETWORK_OPTIONS = [
  {
    name: "SUI",
    value: NETWORKS.SUI,
    displayName: "Sui",
  },
  {
    name: "HYPE",
    value: NETWORKS.HYPEREVM,
    displayName: "Hype",
  },
  {
    name: "SOMNIA",
    value: NETWORKS.SOMNIA,
    displayName: "Somnia",
  },
];

export const NetworkDropdown = () => {
  const { currentNetwork, setCurrentNetwork } = useNetwork();
  const [showDropdown, setShowDropdown] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  console.log("currentNetwork", currentNetwork);

  const selectedOption = NETWORK_OPTIONS.find(
    (option) => option.value === currentNetwork
  );

  const handleClickOutside = (event: Event) => {
    if (
      contentRef.current &&
      !contentRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside, true);
    return () => {
      document.removeEventListener("click", handleClickOutside, true);
    };
  }, []);

  const handleNetworkSelect = (networkValue: string) => {
    setCurrentNetwork(networkValue);
    setShowDropdown(false);
    console.log("Network switched to:", networkValue);
  };

  return (
    <div className="relative" ref={contentRef}>
      <div
        onClick={() => setShowDropdown(!showDropdown)}
        className="bg-brand-800 hover:bg-brand-900 border-brand-800 text-brand-500 flex h-[32px] cursor-pointer items-center gap-2 rounded-[8px] border p-2 text-[12px] font-normal leading-[16px]"
      >
        <AppLogoNetwork network={currentNetwork} className="h-4 w-4" isBase />
        <span className="font-medium">{selectedOption?.displayName}</span>
        <ChevronDownIcon
          className={`h-3 w-3 transition-transform duration-200 ${
            showDropdown ? "rotate-180" : ""
          }`}
        />
      </div>

      {showDropdown && (
        <div
          className="absolute right-0 z-10 mt-1 w-full min-w-[120px] rounded-[8px]"
          style={{
            background:
              "linear-gradient(0deg, rgba(255, 255, 255, 0.10) 0%, rgba(255, 255, 255, 0.10) 100%), #08090C",
            boxShadow: "4px 4px 8px 0px rgba(8, 9, 12, 0.50)",
          }}
        >
          <div className="p-1">
            {NETWORK_OPTIONS.map((option) => {
              const isActive = option.value === currentNetwork;
              return (
                <div
                  key={option.value}
                  className={`flex cursor-pointer items-center gap-2 rounded-[4px] p-2 text-[12px] font-normal leading-[16px] ${
                    isActive
                      ? "bg-neutral-alpha-50 text-white-1000"
                      : "text-neutral-0 hover:bg-neutral-alpha-50"
                  }`}
                  onClick={() => handleNetworkSelect(option.value)}
                >
                  <AppLogoNetwork
                    network={option.value}
                    className="h-4 w-4"
                    isBase
                  />
                  <span>{option.displayName}</span>
                  {isActive && (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 16 16"
                      fill="none"
                      className="ml-auto"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M13.854 4.146a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.793l6.646-6.647a.5.5 0 0 1 .708 0z"
                        fill="currentColor"
                      />
                    </svg>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

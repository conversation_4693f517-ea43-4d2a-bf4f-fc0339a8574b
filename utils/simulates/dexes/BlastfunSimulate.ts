import BaseSimulate from "../BaseSimulate";
import { coinWithBalance, Transaction } from "@mysten/sui/transactions";
import BigNumber from "bignumber.js";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import { bcs } from "@mysten/sui/bcs";
import { TCoinMetadata, TPosition } from "@/types";
import { getOwnerCoinOnchain, getReferenceGasPrice } from "@/utils/suiClient";
import { CoinStruct } from "@mysten/sui/client";
import { isZero } from "@/utils/helper";
import { NETWORKS } from "@/utils/contants";
import rf from "@/services/RequestFactory";

const SUI_ADDRESS =
  "0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI";

// Blastfun constants
const BLASTFUN_PACKAGE_ID =
  "0x1dc6658e2d0df5303dbc44053495424a428f5789426ffe0f040710bfb8d26213";
const BLASTFUN_MEMEZAV_ID =
  "0x2319e3e76dfad73d8f4684bdbf42be4f32d8ce4521dd61becc8261dc918d82c0";
const STANDARD_ID =
  "0x6bf2f2ab6a4a2ac23bce06af472b10089bad7500ea525ac7761fef1d176378e6";

export default class BlastfunSimulate extends BaseSimulate {
  public name = "BlastfunSimulate";
  private buildBuyTransaction = (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: any,
    gasBasePrice: bigint
  ): { tx: Transaction; amountOut: string | number } => {
    const tx = new Transaction();
    tx.setGasBudget(30000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);
    console.log(exactAmountIn, "exactAmountIn");
    const coinIn = coinWithBalance({
      balance: BigInt(exactAmountIn.toString()),
      type: SUI_ADDRESS,
      useGasCoin: true,
    });

    tx.moveCall({
      target: `${BLASTFUN_PACKAGE_ID}::blastfun_router::buy_exact_in`,
      typeArguments: [tokenOut.address, tokenIn.address],
      arguments: [
        tx.object(BLASTFUN_MEMEZAV_ID),
        tx.object(poolObjectId),
        coinIn,
        tx.pure(bcs.option(bcs.Address).serialize(null).toBytes()),
        tx.pure(bcs.option(bcs.vector(bcs.U8)).serialize(null).toBytes()),
        tx.pure.u64(0),
        tx.pure.string("-1"), // order id
      ],
    });

    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenOut: TCoinMetadata,
    tokenIn: TCoinMetadata,
    poolObjectId: any,
    gasBasePrice: bigint
  ): { tx: Transaction; amountOut: string | number } => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    const coinIn = coinWithBalance({
      balance: BigInt(exactAmountIn.toString()),
      type: tokenIn.address,
      useGasCoin: false,
    });

    tx.moveCall({
      target: `${BLASTFUN_PACKAGE_ID}::blastfun_router::sell_exact_in`,
      typeArguments: [tokenIn.address, tokenOut.address],
      arguments: [
        tx.object(BLASTFUN_MEMEZAV_ID),
        tx.object(poolObjectId),
        tx.object(STANDARD_ID),
        coinIn,
        tx.pure(bcs.option(bcs.Address).serialize(null).toBytes()),
        tx.pure.u64("0"),
        tx.pure.string("-1"), // order id
      ],
    });

    return { tx, amountOut: NaN };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      tokenIn,
      tokenOut,
      poolObjectId,
      gasBasePrice
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenOut,
      tokenIn,
      poolObjectId,
      gasBasePrice
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const pair = await rf
      .getRequest("PairRequest")
      .getPair(NETWORKS.SUI, position.pair);

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.tokenQuote,
      position.tokenBase,
      position.poolId,
      gasBasePrice
    );

    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}

"use client";

import cx from "classnames";
import clsx from "classnames";
import { jwtDecode } from "jwt-decode";
import * as React from "react";
import { useCallback, useEffect, useMemo, useState } from "react";
import Marquee from "react-fast-marquee";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  CherronUpIcon,
  ChevronDownIcon,
  IWalletIcon,
  Logo,
  LogoutIcon,
  MobileSearchIcon,
  SearchIcon,
  // BellIcon,
  StarIcon,
  Telegram,
  TelegramIcon16,
  TransactionIcon,
  TwitterIcon,
  TwitterIcon16,
  CloseAuditCheckIcon,
  ImportIcon,
} from "@/assets/icons";
import { BgInstallApp, ITrendingIcon } from "@/public/images";
import { AppAvatarToken } from "@/components";
import AppDrawer from "@/components/AppDrawer";
import ButtonInstallPWA from "@/components/ButtonInstallPWA";
import config from "@/config";
import Storage from "@/libs/storage";
import { ModalSearch } from "@/modals";
import { ROUTE_PATH } from "@/routes";
import { AppDispatch, RootState } from "@/store";
import { setIsShowSidebarMenu } from "@/store/metadata.store";
import { clearUser } from "@/store/user.store";
import { TPair } from "@/types";
import { formatNumber } from "@/utils/format";
import { isAndroidMobile, isIOSMobile } from "@/utils/helper";
import { Alert } from "./Alert";
import ModalSettingsOrder from "@/modals/ModalSettingOrder";
import Image from "next/image";
import { useCurrentAccount, useDisconnectWallet } from "@mysten/dapp-kit";
import { formatShortAddress } from "../utils/format";
import { useLogin } from "../hooks/useLogin";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { AppButton } from "@/components/AppButton";
import { ModalDeposits } from "@/modals/ModalDeposits";
import { AppLogoNetwork } from "../components/AppLogoNetwork";
import { AppNumber } from "../components/AppNumber";
import { getSuiBalanceOnchain } from "../utils/suiClient";
import { convertMistToDec } from "../utils/helper";
import { SUI_DECIMALS } from "../utils/contants";
import { NetworkDropdown } from "../components/NetworkDropdown";
import { useNetwork } from "@/context";

const DropdownMenu = ({
  menu,
  title,
  className,
}: {
  menu: any;
  title: string;
  className?: string;
}) => {
  const [showMenu, setShowMenu] = useState<boolean>(false);
  return (
    <>
      <div
        className="relative"
        onMouseEnter={() => setShowMenu(true)}
        onMouseLeave={() => setShowMenu(false)}
      >
        <div className="hover:text-neutral-alpha-1000 text-neutral-alpha-800 mb-[-15px] flex cursor-pointer items-center gap-1 px-1 pb-4 text-[14px] font-medium leading-[20px] tracking-[0.14px]">
          <div className="!font-[500]">{title}</div>
          <ChevronDownIcon
            className={`${showMenu ? "rotate-[-180deg]" : ""} duration-500`}
          />
        </div>
        {showMenu && (
          <div
            className={clsx("absolute left-0 z-10 w-max pt-[4px]", className)}
          >
            <div
              style={{
                background:
                  "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
                boxShadow:
                  "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
              }}
              className="flex w-full flex-col rounded-[8px]"
            >
              {menu.map((group: any, index: number) => {
                return (
                  <div key={index} className="flex flex-col p-2">
                    {group.items.map((menu: any, index: number) => {
                      return (
                        <Link
                          key={index}
                          href={{
                            pathname: menu.path,
                          }}
                          target={menu.target}
                          onClick={() => setShowMenu(false)}
                        >
                          <div className="hover:bg-neutral-alpha-50 text-white-1000 hover:text-neutral-0 flex cursor-pointer items-center gap-2 rounded-[4px] px-2.5 py-2 text-[12px] font-normal leading-[18px]">
                            {menu.name}
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </>
  );
};
const SNIPE_MENU = [
  {
    group: "1",
    items: [
      {
        name: "Liquidity Sniping",
        path: ROUTE_PATH.LIQUIDITY_SNIPING,
        target: "_self",
      },
      {
        name: "Snipe Migration Dex",
        path: ROUTE_PATH.SNIPE_MIGRATION_DEX,
        target: "_self",
      },
      {
        name: "Snipe by Dev Wallet on Dex",
        path: ROUTE_PATH.SNIPE_DEV_WALLET_ON_DEX,
        target: "_self",
      },
      {
        name: "Snipe by Dev Wallet on Funzone",
        path: ROUTE_PATH.SNIPE_DEV_WALLET_ON_FUNZONE,
        target: "_self",
      },
    ],
  },
];

const EARN_MENU = [
  {
    group: "1",
    items: [
      {
        name: "Airdrop",
        path: ROUTE_PATH.AIRDROP,
        target: "_self",
      },
      {
        name: "Referral",
        path: ROUTE_PATH.REFERRALS,
        target: "_self",
      },
    ],
  },
];

const DISCOVER_MENU = [
  {
    group: "1",
    items: [
      {
        name: "New Pair",
        path: ROUTE_PATH.NEW_PAIRS,
        target: "_self",
      },
      {
        name: "Fun Zone",
        path: ROUTE_PATH.MEME_ZONE,
        target: "_self",
      },
      {
        name: "Trending",
        path: ROUTE_PATH.TRENDING,
        target: "_self",
      },
      {
        name: "Premium Signals",
        path: config.homePage.link.telegramPremiumSignal,
        target: "_blank",
      },
    ],
  },
];

const DEVELOPERS_MENU = [
  {
    group: "1",
    items: [
      {
        name: "Dev Portal",
        path: ROUTE_PATH.DEVELOPER,
        target: "_self",
      },
      {
        name: "Data Services",
        path: config.homePage.link.apiDocs,
        target: "_blank",
      },
      {
        name: "Buy Bot",
        path: config.homePage.link.buyBot,
        target: "_blank",
      },
    ],
  },
];

const RESOURCES_MENU = [
  {
    group: "1",
    items: [
      {
        name: "User Guides",
        path: config.homePage.link.userGuide,
        target: "_blank",
      },
      {
        name: "API Docs",
        path: config.homePage.link.apiDocs,
        target: "_blank",
      },
      {
        name: "Brand Kit",
        path: ROUTE_PATH.MEDIA_KIT,
        target: "_self",
      },
      {
        name: "About Us",
        path: ROUTE_PATH.ABOUT_US,
        target: "_self",
      },
      {
        name: "Blog",
        path: "/blogs",
        target: "_self",
      },
    ],
  },
];

const MENU = [
  {
    name: <DropdownMenu menu={DISCOVER_MENU} title="Discover" />,
    path: "discover",
    isPrivate: false,
    type: "dropdown",
    icon: <TransactionIcon />,
    dropdownItems: [
      ROUTE_PATH.NEW_PAIRS,
      ROUTE_PATH.MEME_ZONE,
      ROUTE_PATH.TRENDING,
    ],
  },
  {
    name: <DropdownMenu menu={SNIPE_MENU} title="Snipe" />,
    path: "snipe",
    isPrivate: true,
    icon: <TransactionIcon />,
    type: "dropdown",
    dropdownItems: [
      ROUTE_PATH.LIQUIDITY_SNIPING,
      ROUTE_PATH.SNIPE_DEV_WALLET_ON_DEX,
      ROUTE_PATH.SNIPE_DEV_WALLET_ON_FUNZONE,
    ],
  },
  {
    name: "Copy Trade",
    path: ROUTE_PATH.COPY_TRADING,
    isPrivate: true,
    icon: <TransactionIcon />,
    target: "_self",
  },
  {
    name: "Multi Chart",
    path: ROUTE_PATH.MULTICHART,
    isPrivate: false,
    icon: <TransactionIcon />,
    target: "_self",
  },
  {
    name: "Trackers",
    path: "/trackers",
    isPrivate: false,
    icon: <TransactionIcon />,
  },
  {
    name: <DropdownMenu menu={EARN_MENU} title="Earn" />,
    path: "earn",
    isPrivate: false,
    icon: <TransactionIcon />,
    type: "dropdown",
    dropdownItems: [ROUTE_PATH.AIRDROP, ROUTE_PATH.REFERRALS],
  },
  {
    name: <DropdownMenu menu={DEVELOPERS_MENU} title="Developers" />,
    path: "developers",
    isPrivate: false,
    icon: <TransactionIcon />,
    type: "dropdown",
    dropdownItems: [],
  },
  {
    name: <DropdownMenu menu={RESOURCES_MENU} title="Resources" />,
    path: "resources",
    isPrivate: false,
    icon: <TransactionIcon />,
    type: "dropdown",
    dropdownItems: [ROUTE_PATH.ABOUT_US],
  },
  {
    name: "Mobile App",
    path: ROUTE_PATH.APP,
    isPrivate: false,
    icon: <TransactionIcon />,
    target: "_self",
  },
  // {
  //   name: "Launchpad",
  //   path: "https://moonbags.io",
  //   isPrivate: false,
  //   icon: <TransactionIcon />,
  //   target: "_blank",
  // },
  // {
  //   name: 'Spot',
  //   path: '',
  //   isPrivate: false,
  //   icon: <TransactionIcon />,
  // },
];

const MENU_EXPLORE = [
  {
    name: "Airdrop",
    path: ROUTE_PATH.AIRDROP,
    isPrivate: false,
  },
  {
    name: "Referral Tracking",
    path: ROUTE_PATH.REFERRALS,
    isPrivate: false,
  },
  {
    name: "Token Profile",
    path: ROUTE_PATH.TOKEN_PROFILE,
    isPrivate: true,
  },
  {
    name: "Liquidity Sniping",
    path: ROUTE_PATH.LIQUIDITY_SNIPING,
    isPrivate: true,
  },
  {
    name: "Snipe Migration Dex",
    path: ROUTE_PATH.SNIPE_MIGRATION_DEX,
    isPrivate: true,
  },
  {
    name: "Snipe by Dev Wallet on Dex",
    path: ROUTE_PATH.SNIPE_DEV_WALLET_ON_DEX,
    isPrivate: true,
  },
  {
    name: "Snipe by Dev Wallet on Funzone",
    path: ROUTE_PATH.SNIPE_DEV_WALLET_ON_FUNZONE,
    isPrivate: true,
  },
  {
    name: "Copy Trade",
    path: ROUTE_PATH.COPY_TRADING,
    isPrivate: true,
  },
  {
    name: "Wallet Tracker",
    path: "/trackers",
    isPrivate: true,
  },
  {
    name: "Buy Bot",
    path: config.homePage.link.buyBot,
    isPrivate: false,
    target: "_blank",
  },
];

const MORE_PROFILE = [
  {
    name: "Customer Support",
    path: config.customerSupportUrl,
    isPrivate: false,
    target: "_blank",
  },
  {
    name: "User guide",
    path: config.homePage.link.userGuide,
    isPrivate: false,
    target: "_blank",
  },
  {
    name: "About Us",
    path: ROUTE_PATH.ABOUT_US,
    target: "_self",
    isPrivate: false,
  },
];
const SOCIAL_LINKS = [
  {
    name: "Twitter",
    path: config.linkSocial.twitter,
    icon: <TwitterIcon16 />,
  },
  {
    name: "Telegram",
    path: config.linkSocial.telegram,
    icon: <TelegramIcon16 />,
  },
];

export const Profile = () => {
  const [showMenu, setShowMenu] = useState<boolean>(false);
  const [isShowSettings, setIsShowSettings] = useState<boolean>(false);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );

  const DROPDOWN_MENU = [
    {
      group: "1",
      items: [
        {
          name: "Referral Tracking",
          path: ROUTE_PATH.REFERRALS,
          target: "_self",
        },
        {
          name: "Wallet Manager",
          path: ROUTE_PATH.WALLET_MANAGER,
          target: "_self",
        },
        {
          name: "Token Profile",
          path: ROUTE_PATH.TOKEN_PROFILE,
          target: "_self",
        },
      ],
    },
    {
      group: "2",
      items: [
        {
          name: "Developer",
          path: ROUTE_PATH.DEVELOPER,
          target: "_self",
        },
        {
          name: "Customer Support",
          path: config.customerSupportUrl,
          target: "_blank",
        },
      ],
    },
  ];
  const decodedInfo = accessToken && (jwtDecode(accessToken) as any);
  const dispatch = useDispatch<AppDispatch>();
  const currentAccount = useCurrentAccount();
  const { mutate: disconnect } = useDisconnectWallet();

  const onLogout = () => {
    if (isExternalWallet) {
      disconnect();
    }
    dispatch(clearUser());
  };

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.LOGOUT, onLogout);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.LOGOUT, onLogout);
    };
  }, [onLogout]);

  return (
    <>
      <div
        className="relative"
        onMouseEnter={() => setShowMenu(true)}
        onMouseLeave={() => setShowMenu(false)}
      >
        <div className="body-sm-semibold-12 border-brand-900 bg-brand-900 text-brand-500 flex h-[32px] max-w-[112px] cursor-pointer items-center gap-1 rounded-[6px] border p-2 ">
          {!!currentAccount?.address ? (
            formatShortAddress(currentAccount?.address, 5, 4)
          ) : (
            <div className="truncate">
              @{decodedInfo?.userName || decodedInfo?.displayName}
            </div>
          )}

          <ChevronDownIcon
            className={`h-4 w-4 ${
              showMenu ? "rotate-[-180deg]" : ""
            } duration-500`}
          />
        </div>
        {showMenu && (
          <div className={cx("absolute right-0 z-10 w-[220px] pt-[4px]")}>
            <div className="border-neutral-alpha-50 bg-black-900 shadow-[0px_4px_12px_0px_rgba(8, 9, 12, 1)] flex w-full flex-col rounded-[8px] border backdrop-blur-[10px]">
              {accessToken && (
                <>
                  {DROPDOWN_MENU.map((group, index) => {
                    return (
                      <div
                        key={index}
                        className="border-white-50 flex flex-col border-t p-2"
                      >
                        {group.items.map((menu, index) => {
                          return (
                            <Link
                              key={index}
                              href={{
                                pathname: menu.path,
                              }}
                              target={menu.target}
                              onClick={() => setShowMenu(false)}
                            >
                              <div className="hover:bg-neutral-alpha-50 text-white-1000 hover:text-neutral-0 flex cursor-pointer items-center gap-2 rounded-[4px] px-2.5 py-2 text-[12px] font-normal leading-[18px]">
                                {menu.name}
                              </div>
                            </Link>
                          );
                        })}
                      </div>
                    );
                  })}
                  <div className="border-white-50 border-b p-2 pt-0">
                    <div
                      onClick={() => setIsShowSettings(true)}
                      className="hover:bg-neutral-alpha-50 text-white-1000 hover:text-neutral-0 flex cursor-pointer items-center gap-2 rounded-[4px] px-2.5 py-2 text-[12px] font-normal leading-[18px]"
                    >
                      Settings
                    </div>
                  </div>
                </>
              )}

              <div className="border-neutral-alpha-50 flex flex-col border-b p-2">
                <div
                  onClick={onLogout}
                  className="hover:bg-neutral-alpha-50 text-white-700 flex cursor-pointer items-center gap-2 rounded-[4px] px-2.5 py-2 text-[12px] font-normal leading-[18px]"
                >
                  Logout
                </div>
              </div>
              <div className="flex-between flex w-full gap-2 px-4 py-3">
                <div className="text-white-500 flex-1 text-[10px] font-normal leading-[16px]">
                  Join us
                </div>
                <div className="flex items-center justify-end gap-2.5">
                  <a
                    href={config.linkSocial.twitter}
                    target="_blank"
                    className="text-white-500 hover:text-neutral-alpha-1000 "
                  >
                    <TwitterIcon className="h-4 w-4" />
                  </a>
                  <a
                    href={config.linkSocial.telegram}
                    target="_blank"
                    className="text-white-500 hover:text-neutral-alpha-1000 "
                  >
                    <Telegram className="h-4 w-4" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {isShowSettings && (
        <ModalSettingsOrder
          isOpen={isShowSettings}
          onClose={() => setIsShowSettings(false)}
        />
      )}
    </>
  );
};

const ProfileMobile = () => {
  const { accessToken } = useSelector((state: RootState) => state.user);
  const decodedInfo = accessToken && (jwtDecode(accessToken) as any);
  const currentAccount = useCurrentAccount();

  return (
    <div className="border-brand-900 bg-brand-900 text-brand-500 flex w-full cursor-pointer items-center gap-1 rounded-[6px] border p-2 ">
      <div className="max-w-[96px] truncate text-[12px] font-medium">
        {!!currentAccount?.address ? (
          formatShortAddress(currentAccount?.address, 4, 3)
        ) : (
          <>@{decodedInfo?.userName || decodedInfo?.displayName}</>
        )}
      </div>
    </div>
  );
};

const MobileProfileLayout = ({
  getLoginUrl,
  closeDrawer,
  isOpenDrawer,
  openDrawer,
}: {
  getLoginUrl: () => string;
  closeDrawer: () => void;
  isOpenDrawer: boolean;
  openDrawer: () => void;
}) => {
  const [isShowSettings, setIsShowSettings] = useState<boolean>(false);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );
  const dispatch = useDispatch<AppDispatch>();
  const pathname = usePathname();
  const { onLogin } = useLogin();
  const { mutate: disconnect } = useDisconnectWallet();

  const onLogout = () => {
    if (isExternalWallet) {
      disconnect();
    }

    dispatch(clearUser());
    closeDrawer();
  };

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.LOGOUT, onLogout);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.LOGOUT, onLogout);
    };
  }, [onLogout]);

  return (
    <div className="max-tablet:gap-1 flex items-center justify-end gap-2">
      <div className="flex items-center gap-2 px-2">
        <Search />
        {accessToken && <Alert />}
        <Link href={ROUTE_PATH.FAVOURITES}>
          <div className="flex cursor-pointer items-center">
            <StarIcon />
          </div>
        </Link>
      </div>

      <div>
        {isExternalWallet || accessToken ? (
          <ProfileMobile />
        ) : (
          <div
            onClick={onLogin}
            className="cw-max bg-neutral-alpha-1000 text-neutral-beta-900 flex cursor-pointer items-center gap-2 rounded-[6px] p-2 px-4 text-[12px] font-medium leading-[16px]"
          >
            Sign in
          </div>
        )}
      </div>

      <div>
        <AppDrawer
          isOpen={isOpenDrawer}
          toggleDrawer={closeDrawer}
          className={`!w-full ${
            isIOSMobile() ? "!h-[calc(100%-59px)]" : "!h-[calc(100%-47px)]"
          }`}
        >
          <div className="flex h-full flex-col items-center">
            <div className="border-white-100 flex w-full items-center justify-between border-b px-2 py-1.5">
              <Link href={ROUTE_PATH.HOME}>
                <div className="cursor-pointer">
                  <Logo />
                </div>
              </Link>

              <div className="flex flex-shrink-0 items-center gap-2">
                <Search />
                <div className="flex items-center">
                  {/* <Notification /> */}
                  {accessToken && <Alert />}
                  <Link href={ROUTE_PATH.FAVOURITES}>
                    <div className="flex cursor-pointer items-center p-2">
                      <StarIcon />
                    </div>
                  </Link>
                </div>
                <>
                  {accessToken && <ProfileMobile />}
                  <div
                    onClick={closeDrawer}
                    className="bg-neutral-alpha-100 cursor-pointer rounded-[6px] p-2"
                  >
                    <CloseAuditCheckIcon />
                  </div>
                </>
              </div>
            </div>
            <div className="border-white-100 w-full border-b">
              <div className="text-white-1000 flex items-center p-4 pb-0 text-[18px] font-semibold">
                Menu
              </div>
              {isAndroidMobile() ? (
                <ButtonInstallPWA />
              ) : (
                <div className="relative p-4">
                  <img
                    src={BgInstallApp.src}
                    alt="bg-install"
                    className="w-full"
                  />
                  <div className="absolute bottom-0 right-0 top-0 flex w-full flex-col items-center justify-center px-20 py-[12px]">
                    <div className="mb-1 font-semibold">
                      Install RaidenX App
                    </div>
                    <div className="text-white-500 mb-[10px] text-[10px] font-medium">
                      Add RaidenX Web App to your homescreen for instant access.
                      No Play Store download needed.
                    </div>
                    <Link href={ROUTE_PATH.APP}>
                      <button className="bg-white-0 text-black-900 rounded-6 border-white-50 flex cursor-pointer items-center gap-[4px] border px-[10px] py-[8px] text-center text-[12px]">
                        <ImportIcon className="text-black-900" />
                        Install app
                      </button>
                    </Link>
                  </div>
                </div>
              )}
            </div>
            <div className="flex h-full max-h-[calc(100%-47px)] w-full flex-col justify-between overflow-y-auto">
              <div>
                <div className="border-white-100 w-full border-b p-[16px] pb-[8px]">
                  <div className="text-white-300 p-2 text-[10px] leading-[16px]">
                    EXPLORE
                  </div>
                  {MENU_EXPLORE.map((menu, index) => {
                    if (menu.isPrivate && !accessToken) return;
                    if (menu?.target === "_blank") {
                      return (
                        <a
                          key={index}
                          href={menu.path}
                          target="_blank"
                          className={`${
                            menu.path === pathname
                              ? "text-neutral-alpha-1000 rounded-[6px] font-semibold"
                              : "text-neutral-alpha-800"
                          }  hover:text-neutral-alpha-1000 flex cursor-pointer items-center gap-2 px-2 py-[10px] text-start text-[14px] leading-[20px] tracking-[0.14px]`}
                        >
                          <span>{menu.name}</span>
                        </a>
                      );
                    }

                    return (
                      <Link href={menu.path} key={index} onClick={closeDrawer}>
                        <div
                          className={`${
                            menu.path === pathname
                              ? "text-neutral-alpha-1000 rounded-[6px] font-semibold"
                              : "text-neutral-alpha-800"
                          }  hover:text-neutral-alpha-1000 flex cursor-pointer items-center gap-2 px-2 py-[10px] text-start text-[14px] leading-[20px] tracking-[0.14px]`}
                        >
                          {menu.name}
                        </div>
                      </Link>
                    );
                  })}
                </div>

                <div className="border-white-100 w-full border-b px-[16px] pb-[8px] pt-[10px]">
                  <div className="text-white-300 p-2 text-[10px] leading-[16px]">
                    MORE
                  </div>
                  {MORE_PROFILE.map((menu, index) => {
                    if (menu.isPrivate && !accessToken) return;
                    if (menu?.target === "_blank") {
                      return (
                        <a
                          key={index}
                          href={menu.path}
                          target="_blank"
                          className={`${
                            menu.path === pathname
                              ? "text-neutral-alpha-1000 rounded-[6px] font-semibold"
                              : "text-neutral-alpha-800"
                          }  hover:text-neutral-alpha-1000 flex cursor-pointer items-center gap-2 px-2 py-[10px] text-start text-[14px] leading-[20px] tracking-[0.14px]`}
                        >
                          <span>{menu.name}</span>
                        </a>
                      );
                    }
                    return (
                      <Link href={menu.path} key={index} onClick={closeDrawer}>
                        <div
                          className={`${
                            menu.path === pathname
                              ? "text-neutral-alpha-1000 rounded-[6px] font-semibold"
                              : "text-neutral-alpha-800"
                          }  hover:text-neutral-alpha-1000 flex cursor-pointer items-center gap-2 px-2 py-[10px] text-start text-[14px] leading-[20px] tracking-[0.14px]`}
                        >
                          {menu.name}
                        </div>
                      </Link>
                    );
                  })}

                  {accessToken && (
                    <div
                      onClick={() => {
                        setIsShowSettings(true);
                      }}
                      className={`text-neutral-alpha-800 hover:text-neutral-alpha-1000 flex cursor-pointer items-center gap-2 px-2 py-[10px] text-start text-[14px] leading-[20px] tracking-[0.14px]`}
                    >
                      Settings
                    </div>
                  )}
                </div>
                <div className="w-full px-[16px] pb-[8px] pt-[10px] ">
                  <div className="text-white-300 p-2 text-[10px] leading-[16px]">
                    SOCIAL
                  </div>
                  <div className="flex gap-[10px]">
                    {SOCIAL_LINKS.map((menu, index) => {
                      return (
                        <a
                          key={index}
                          href={menu.path}
                          target="_blank"
                          className={`${
                            menu.path === pathname
                              ? "text-neutral-alpha-1000 rounded-[6px] font-semibold"
                              : "text-neutral-alpha-800"
                          }  hover:bg-neutral-alpha-50 menus-center border-white-150 flex cursor-pointer gap-2 rounded-[6px] border p-2 text-start text-[14px] leading-[20px] tracking-[0.14px]`}
                        >
                          <div className="text-[16px]">{menu.icon}</div>
                        </a>
                      );
                    })}
                  </div>
                </div>
              </div>

              <div className="mb-[16px] flex w-full justify-center gap-2 px-[16px]">
                {isExternalWallet || accessToken ? (
                  <div
                    onClick={onLogout}
                    className="text-white-1000 hover:bg-neutral-alpha-50 border-white-150 flex h-[32px] w-full cursor-pointer items-center justify-center gap-1 rounded-[8px] border py-[10px] text-[12px] leading-[16px] tracking-[0.14px]"
                  >
                    <LogoutIcon />
                    Log out
                  </div>
                ) : (
                  <div
                    onClick={onLogin}
                    className="text-black-900 hover:bg-white-800 bg-white-1000 flex h-[32px] w-full cursor-pointer cursor-pointer items-center justify-center gap-1 rounded-[8px] border text-[12px] leading-[16px] tracking-[0.14px]"
                  >
                    <Telegram className="h-[16px] w-[16px]" />
                    Connect to Sign In
                  </div>
                )}
              </div>
            </div>
          </div>
        </AppDrawer>
      </div>

      {isShowSettings && (
        <ModalSettingsOrder
          isOpen={isShowSettings}
          onClose={() => setIsShowSettings(false)}
        />
      )}
    </div>
  );
};

const TokenItem = ({ meme, rank }: { meme: TPair; rank: number }) => {
  const isUp = useMemo(() => {
    if (meme?.stats?.percent) {
      return meme?.stats?.percent["24h"] > 0;
    }
    return false;
  }, [meme?.stats?.percent]);
  const { currentNetwork } = useNetwork();

  return (
    <Link href={`/${currentNetwork}/${meme.slug}`}>
      <div className="hover:bg-neutral-alpha-100 flex cursor-pointer items-center gap-1 rounded-[4px] px-2 py-1 text-[12px] font-normal leading-[18px]">
        <div className="text-neutral-alpha-700">#{rank}</div>
        <div className="h-4 w-4">
          <AppAvatarToken
            size={16}
            className="h-4 w-4"
            image={
              meme?.tokenBase?.logoImageUrl || meme?.tokenBase?.iconUrl || ""
            }
          />
        </div>
        <div className="text-[12px] font-medium">
          {meme?.tokenBase?.symbol || "Unknown"}
        </div>
        <div className={isUp ? "text-green-500" : "text-red-500"}>
          {isUp ? "+" : ""}
          {formatNumber(meme?.stats?.percent["24h"], 2)}%
        </div>
      </div>
    </Link>
  );
};

export const Trending = () => {
  const trendingPairsMeme = useSelector(
    (state: RootState) => state.metadata.trendingPairsMeme
  );

  return (
    <div className="bg-neutral-alpha-50 flex h-max w-full flex-1 items-center gap-1 rounded-[4px] p-1">
      <Link href={ROUTE_PATH.TRENDING}>
        <div className="body-md-medium-14 hover:body-md-semibold-14 hover:text-neutral-alpha-1000 text-neutral-alpha-800 flex cursor-pointer items-center gap-1 pr-1">
          <img src={ITrendingIcon.src} alt="trending" />
          Trending
        </div>
      </Link>

      <Marquee
        pauseOnHover
        className="w-[calc(100vw-1400px)] max-w-full flex-1"
      >
        <div className="flex overflow-x-hidden">
          {trendingPairsMeme.map((item, index) => {
            return <TokenItem key={index} meme={item} rank={index + 1} />;
          })}
        </div>
      </Marquee>

      <Link href={ROUTE_PATH.TRENDING}>
        <div className="cursor-pointer">
          <CherronUpIcon className="rotate-[90deg] " />
        </div>
      </Link>
    </div>
  );
};

const Search = () => {
  const [isShowModalSearch, setIsShowModalSearch] = useState<boolean>(false);
  const isTablet = useMediaQuery({ maxWidth: 992 });
  return (
    <div>
      {!isTablet ? (
        <div
          onClick={() => setIsShowModalSearch(true)}
          className="hover:bg-neutral-alpha-50 flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-[4px]"
        >
          <SearchIcon />
        </div>
      ) : (
        <div className="">
          <div onClick={() => setIsShowModalSearch(true)}>
            <MobileSearchIcon />
          </div>
        </div>
      )}
      {isShowModalSearch && (
        <ModalSearch
          isOpen={isShowModalSearch}
          onClose={() => setIsShowModalSearch(false)}
        />
      )}
    </div>
  );
};

const DepositButton = () => {
  const [isShowModalDeposit, setIsShowModalDeposit] = useState(false);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  return (
    <>
      <AppButton
        onClick={() => setIsShowModalDeposit(true)}
        className="mr-2 h-[32px]"
        variant="buy"
      >
        Deposit
      </AppButton>

      {isShowModalDeposit && (
        <ModalDeposits
          isOpen={isShowModalDeposit}
          wallets={wallets}
          onClose={() => setIsShowModalDeposit(false)}
        />
      )}
    </>
  );
};

const WalletManager = () => {
  const [balanceSuiOnchain, setBalanceSuiOnchain] = useState<string>("");
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const balanceSui = useMemo(() => {
    return wallets.reduce((sum: number, w: any) => sum + w.balance, 0);
  }, [wallets]);

  const currentAccount = useCurrentAccount();

  useEffect(() => {
    if (!currentAccount?.address) return;
    getBalanceSui().then();
  }, [currentAccount?.address]);

  const getBalanceSui = useCallback(async () => {
    const res = await getSuiBalanceOnchain(currentAccount?.address || "");
    setBalanceSuiOnchain(convertMistToDec(res, SUI_DECIMALS));
  }, [currentAccount?.address]);
  const { currentNetwork } = useNetwork();

  if (isExternalWallet) {
    return (
      <div className="border-neutral-alpha-100 hover:bg-neutral-alpha-100 bg-neutral-alpha-50 flex h-[32px] cursor-pointer items-center gap-2 rounded-[8px] border p-2 text-[12px] font-normal leading-[16px]">
        <Image
          alt="wallet"
          src={IWalletIcon}
          height={16}
          width={16}
          unoptimized
        />

        <div className="flex items-center gap-1">
          <AppLogoNetwork network={currentNetwork} isBase />
          <AppNumber value={balanceSuiOnchain} className="body-sm-regular-12" />
        </div>
      </div>
    );
  }

  return (
    <Link
      href={ROUTE_PATH.WALLET_MANAGER}
      className="border-neutral-alpha-100 hover:bg-neutral-alpha-100 bg-neutral-alpha-50 flex h-[32px] cursor-pointer items-center gap-2 rounded-[8px] border p-2 text-[12px] font-normal leading-[16px]"
    >
      <Image
        alt="wallet"
        src={IWalletIcon}
        height={16}
        width={16}
        unoptimized
      />

      <div className="flex items-center gap-1">
        <AppLogoNetwork network={currentNetwork} isBase />
        <AppNumber value={balanceSui} className="body-sm-regular-12" />
      </div>
    </Link>
  );
};

export const Header = () => {
  const pathname = usePathname();
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );
  const isShowSidebarMenu = useSelector(
    (state: RootState) => state.metadata.isShowSidebarMenu
  );
  const isTablet = useMediaQuery({ maxWidth: 992 });
  const isLargeTablet = useMediaQuery({ maxWidth: 1200 });
  const [isClient, setIsClient] = useState(false);

  const dispatch = useDispatch<AppDispatch>();
  const { onLogin } = useLogin();

  const getLoginUrl = () => {
    const referralCode = Storage.getReferralCode();
    if (referralCode) {
      return `${config.link_telegram}?start=${referralCode}`;
    }
    return `${config.link_telegram}?start=login`;
  };

  const closeDrawer = () => {
    dispatch(setIsShowSidebarMenu({ isShow: false }));
  };

  const openDrawer = () => {
    dispatch(setIsShowSidebarMenu({ isShow: true }));
  };

  const getHomeLink = useCallback(() => {
    if (accessToken) {
      return ROUTE_PATH.NEW_PAIRS;
    }
    return ROUTE_PATH.HOME;
  }, [accessToken]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null;

  return (
    <>
      <div className="bg-neutral-beta-900 border-neutral-alpha-100 flex h-[auto] items-center justify-between gap-[24px] border-b px-4 py-2 md:h-[52px] md:px-6 md:py-0">
        <div className="flex flex-1 flex-col items-center gap-2 md:flex-row md:gap-4">
          <div className="tablet:gap-0 tablet:w-auto flex w-full items-center justify-between gap-2">
            <Link href={getHomeLink()}>
              <div className="cursor-pointer">
                <Logo />
              </div>
            </Link>
            {isTablet && (
              <MobileProfileLayout
                getLoginUrl={getLoginUrl}
                closeDrawer={closeDrawer}
                isOpenDrawer={isShowSidebarMenu}
                openDrawer={openDrawer}
              />
            )}
          </div>

          {!isLargeTablet && (
            <div className="flex w-max gap-2">
              {MENU.map((menu, index) => {
                if (menu.isPrivate && !accessToken) return;
                if (menu.type === "dropdown") {
                  const isActive = menu.dropdownItems.some((item) =>
                    pathname.includes(item)
                  );
                  return (
                    <div
                      key={index}
                      className={`${
                        isActive
                          ? "text-neutral-alpha-1000 border-neutral-alpha-1000 border-b font-semibold"
                          : "text-neutral-alpha-800 font-medium"
                      }  hover:text-neutral-alpha-1000 mb-[-15px] cursor-pointer px-1 pb-4 text-[14px] leading-[20px] tracking-[0.14px]`}
                    >
                      {menu.name}
                    </div>
                  );
                }
                return (
                  <Link
                    href={{
                      pathname: menu.path,
                    }}
                    key={index}
                    target={menu.target}
                  >
                    <div
                      className={`${
                        menu.path === pathname
                          ? "text-neutral-alpha-1000 border-neutral-alpha-1000 border-b font-semibold"
                          : "text-neutral-alpha-800 font-medium"
                      }  hover:text-neutral-alpha-1000 mb-[-15px] cursor-pointer px-1 pb-4 text-[14px] leading-[20px] tracking-[0.14px]`}
                    >
                      {menu.name}
                    </div>
                  </Link>
                );
              })}
            </div>
          )}
        </div>

        {!isTablet && (
          <div className="flex flex-shrink-0 items-center gap-2">
            <Search />
            <div className="flex gap-3">
              <NetworkDropdown />
              {accessToken && <DepositButton />}
              {/* <Notification /> */}
              {accessToken && <Alert />}

              <Link href={ROUTE_PATH.FAVOURITES}>
                <div className="hover:bg-neutral-alpha-50 flex cursor-pointer items-center rounded-[4px] p-2">
                  <StarIcon />
                </div>
              </Link>
            </div>

            {isExternalWallet || accessToken ? (
              <>
                <WalletManager />
                <Profile />
              </>
            ) : (
              <div
                onClick={onLogin}
                className="bg-neutral-alpha-1000 text-neutral-beta-900 flex w-max cursor-pointer items-center gap-2 rounded-[6px] p-2 !px-4 text-[12px] font-medium leading-[16px]"
              >
                Connect
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};

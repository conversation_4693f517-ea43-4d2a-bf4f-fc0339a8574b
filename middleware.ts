import { NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // <PERSON><PERSON>m tra nếu path bắt đầu với @ (ví dụ: /@hello)
  const pathSegments = pathname.split("/").filter(Boolean);

  if (pathSegments.length > 0 && pathSegments[0].startsWith("@")) {
    const username = pathSegments[0];
    const referralCode = username.replace("@", "");

    console.log("Referral code detected:", referralCode);

    // Tạo response redirect về trang chủ
    const response = NextResponse.redirect(new URL("/", request.url));

    // Lưu referral code vào cookie để client-side có thể đọc
    response.cookies.set("referral_code", referralCode, {
      maxAge: 60 * 60 * 24 * 30, // 30 ngày
      httpOnly: false, // Cho phép client-side đọc
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    });

    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};

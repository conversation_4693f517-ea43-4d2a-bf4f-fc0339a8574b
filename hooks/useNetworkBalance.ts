import { useCallback, useEffect, useState } from "react";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { usePrivy } from "@privy-io/react-auth";
import { useNetwork } from "@/context/network";
import { getBalanceForNetwork, convertToDecimal } from "@/utils/networkClient";
import {
  isSuiNetwork,
  isEthereumBasedNetwork,
} from "@/app/providers/networkChains";

export const useNetworkBalance = () => {
  const [balance, setBalance] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { currentNetwork } = useNetwork();

  const currentAccount = useCurrentAccount();

  const { user, authenticated } = usePrivy();

  const getWalletAddress = useCallback(() => {
    if (isSuiNetwork(currentNetwork)) {
      return currentAccount?.address;
    } else if (isEthereumBasedNetwork(currentNetwork)) {
      return user?.wallet?.address;
    }
    return null;
  }, [currentNetwork, currentAccount, user]);

  const fetchBalance = useCallback(async () => {
    const walletAddress = getWalletAddress();
    if (!walletAddress) {
      setBalance("");
      return;
    }

    setIsLoading(true);
    try {
      const rawBalance = await getBalanceForNetwork(
        currentNetwork,
        walletAddress
      );
      const formattedBalance = convertToDecimal(rawBalance, currentNetwork);
      setBalance(formattedBalance);
    } catch (error) {
      console.error("Error fetching balance:", error);
      setBalance("0");
    } finally {
      setIsLoading(false);
    }
  }, [currentNetwork, getWalletAddress]);

  useEffect(() => {
    fetchBalance();
  }, [fetchBalance]);

  return {
    balance,
    isLoading,
    refetch: fetchBalance,
    walletAddress: getWalletAddress(),
    isConnected: isSuiNetwork(currentNetwork)
      ? !!currentAccount?.address
      : isEthereumBasedNetwork(currentNetwork)
      ? authenticated
      : false,
  };
};

import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../store/index";
import rf from "@/services/RequestFactory";
import { getSettingsQuickOrder } from "@/store/user.store";

export const useSettingsOrder = () => {
  const dispatch = useDispatch<AppDispatch>();
  const network = useSelector((state: RootState) => state.user.network);
  const settingsQuickOrder = useSelector(
    (state: RootState) => state.user.settingsQuickOrder
  );
  const settingsLimitOrder = useSelector(
    (state: RootState) => state.user.settingsLimitOrder
  );

  const updateSettingsQuickOrder = useCallback(
    async (dataSettings: any, onSuccess?: () => void) => {
      try {
        await rf
          .getRequest("PresetSettingRequest")
          .updateQuickOrderSettings(network, {
            ...dataSettings,
          });
        dispatch(getSettingsQuickOrder({ network }));
        if (onSuccess) {
          onSuccess();
        }
      } catch (e: any) {
        console.error(e);
        throw Error(e);
      }
    },
    [network]
  );

  return {
    settingsQuickOrder,
    settingsLimitOrder,
    updateSettingsQuickOrder,
  };
};

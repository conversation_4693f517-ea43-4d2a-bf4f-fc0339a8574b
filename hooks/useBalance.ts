"use client";

import { TBalance } from "@/types/balance.type";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import { normalizeSuiAddress, normalizeStructTag } from "@mysten/sui/utils";
import { TWallet } from "@/types";
import {
  SUI_TOKEN_ADDRESS_SHORT,
  SUI_TOKEN_ADDRESS_FULL,
} from "@/utils/contants";
import rf from "@/services/RequestFactory";
import { setBalance } from "@/store/user.store";
import { useCallback, useEffect, useRef } from "react";
import { useNetwork } from "@/context";

export const useBalance = () => {
  const balances = useSelector((state: RootState) => state.user.balances);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const dispatch = useDispatch<AppDispatch>();
  const { currentNetwork } = useNetwork();

  const balancesRef = useRef<TBalance[]>([]);
  const walletsRef = useRef<TWallet[]>([]);

  useEffect(() => {
    balancesRef.current = balances;
  }, [balances]);

  useEffect(() => {
    walletsRef.current = wallets;
  }, [wallets]);

  const getWalletBalance = useCallback(
    (tokenAddress: string, walletAddress: string) => {
      if (!tokenAddress) return "0";

      if (
        tokenAddress === SUI_TOKEN_ADDRESS_FULL ||
        tokenAddress === SUI_TOKEN_ADDRESS_SHORT
      ) {
        return getSuiBalance(walletAddress);
      }
      const tokenBalance =
        balancesRef.current?.find((balance: TBalance) => {
          return (
            balance &&
            balance?.token?.address &&
            balance?.walletAddress &&
            tokenAddress &&
            walletAddress &&
            normalizeStructTag(balance?.token?.address) ===
              normalizeStructTag(tokenAddress) &&
            normalizeSuiAddress(balance?.walletAddress) ===
              normalizeSuiAddress(walletAddress)
          );
        }) || ({} as TBalance);

      return tokenBalance?.balance || "0";
    },
    [balances]
  );

  const getWalletBalanceAsync = async (
    tokenAddress: string,
    walletAddress: string
  ) => {
    if (!tokenAddress) return "0";

    if (
      tokenAddress === SUI_TOKEN_ADDRESS_FULL ||
      tokenAddress === SUI_TOKEN_ADDRESS_SHORT
    ) {
      return getSuiBalance(walletAddress);
    }
    const existingBalance = balancesRef.current?.find((balance: TBalance) => {
      return (
        balance?.token?.address &&
        tokenAddress &&
        walletAddress &&
        normalizeStructTag(balance?.token?.address) ===
          normalizeStructTag(tokenAddress) &&
        normalizeSuiAddress(balance?.walletAddress) ===
          normalizeSuiAddress(walletAddress)
      );
    });

    if (existingBalance) {
      return existingBalance.balance || "0";
    }

    let tokenBalance = "0";
    const res = await rf
      .getRequest("TokenRequest")
      .getBalanceOf(currentNetwork, tokenAddress);
    res.forEach((item: any) => {
      const balance = item?.balance || 0;
      dispatch(
        setBalance({
          network: currentNetwork,
          token: {
            address: tokenAddress,
          },
          walletAddress: item.walletAddress,
          balance: balance,
          balanceUsd: item.balanceUsd,
        })
      );
      if (
        normalizeSuiAddress(item.walletAddress) ===
        normalizeSuiAddress(walletAddress)
      ) {
        tokenBalance = balance;
      }
    });
    return tokenBalance;
  };

  const getSuiBalance = (walletAddress: string) => {
    const wallet =
      walletsRef.current?.find((wallet: TWallet) => {
        return (
          wallet?.address &&
          walletAddress &&
          normalizeSuiAddress(wallet?.address) ===
            normalizeSuiAddress(walletAddress)
        );
      }) || ({} as TWallet);

    return wallet?.balance || "0";
  };

  return {
    getWalletBalance,
    getWalletBalanceAsync,
  };
};

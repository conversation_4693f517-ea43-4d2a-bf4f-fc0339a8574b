"use client";

import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useEffect, useMemo, useState } from "react";
import { toStringBN } from "@/utils/helper";
import { TTradingWallet } from "@/types/balance.type";
import { useBalance } from "./useBalance";
import { useRaidenxWallet } from "./useRaidenxWallet";

export const useTradingWallet = (
  addressTokenBase: string,
  addressTokenQuoteSelected: string
) => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const balances = useSelector((state: RootState) => state.user.balances);
  const { activeWalletAddresses } = useRaidenxWallet();

  const [tradingWallets, setTradingWallets] = useState<TTradingWallet[]>([]);
  const { getWalletBalance, getWalletBalanceAsync } = useBalance();

  const activeTradingWallets = useMemo(() => {
    if (!activeWalletAddresses?.length) {
      return tradingWallets.slice(0, 1);
    }

    return tradingWallets.filter((item) =>
      activeWalletAddresses.includes(item.address)
    );
  }, [activeWalletAddresses, tradingWallets]) as TTradingWallet[];

  const tokenQuoteBalanceFunc = async (
    tokenAddress: string,
    walletAddress: string
  ) => {
    return await getWalletBalanceAsync(tokenAddress, walletAddress);
  };

  useEffect(() => {
    if (!wallets.length) return;

    (async () => {
      setTradingWallets(
        await Promise.all(
          wallets.map(async (wallet) => {
            const tokenBaseBalance = getWalletBalance(
              addressTokenBase,
              wallet.address
            );
            const tokenQuoteBalance = await tokenQuoteBalanceFunc(
              addressTokenQuoteSelected,
              wallet.address
            );
            return {
              ...wallet,
              suiBalance: toStringBN(wallet.balance),
              baseBalance: toStringBN(tokenBaseBalance),
              quoteBalance: toStringBN(tokenQuoteBalance),
            };
          })
        )
      );
    })();
  }, [balances, wallets, addressTokenBase, addressTokenQuoteSelected]);

  const activeTotalQuoteBalance = useMemo(
    () =>
      activeTradingWallets.reduce((sum, obj) => sum + +obj?.quoteBalance, 0),
    [activeTradingWallets]
  );

  const activeTotalBaseBalance = useMemo(
    () => activeTradingWallets.reduce((sum, obj) => sum + +obj?.baseBalance, 0),
    [activeTradingWallets]
  );

  const totalQuoteBalance = useMemo(
    () => tradingWallets.reduce((sum, obj) => sum + +obj?.quoteBalance, 0),
    [tradingWallets]
  );

  const totalBaseBalance = useMemo(
    () => tradingWallets.reduce((sum, obj) => sum + +obj?.baseBalance, 0),
    [tradingWallets]
  );

  return {
    tradingWallets,
    activeTradingWallets,
    activeTotalQuoteBalance,
    activeTotalBaseBalance,
    totalQuoteBalance,
    totalBaseBalance,
  };
};

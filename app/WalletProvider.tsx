import { ReactNode } from "react";
import "@mysten/dapp-kit/dist/index.css";
import { getFullnodeUrl } from "@mysten/sui/client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import { MultiNetworkWalletProvider } from "./providers/MultiNetworkWalletProvider";

const queryClient = new QueryClient();

const networks = {
  mainnet: { url: getFullnodeUrl("mainnet") },
  testnet: { url: getFullnodeUrl("testnet") },
  localnet: { url: getFullnodeUrl("localnet") },
  devnet: { url: getFullnodeUrl("devnet") },
};

const SuiWalletProvider = ({ children }: { children: ReactNode }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <MultiNetworkWalletProvider>{children}</MultiNetworkWalletProvider>
    </QueryClientProvider>
  );
};

export default SuiWalletProvider;

"use client";

import BigNumber from "bignumber.js";
import moment from "moment";
import React, { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { Virtuoso } from "react-virtuoso";
import { FlagIcon, SubFlagIcon, WarningCircleIcon } from "@/assets/icons";
import {
  AppCopy,
  AppLogoNetwork,
  AppNumber,
  AppUserAddress,
} from "@/components";
import { BaseModal } from "@/modals/BaseModal";
import { RootState } from "@/store";
import { TClaimHistory, TClaimRequest } from "@/types/referral.type";
import { formatNumber } from "@/utils/format";
import { multipliedBN } from "@/utils/helper";
import { SelectWallets } from "./select-wallets";
import { NETWORKS } from "@/utils/contants";
import { useNetwork } from "@/context";
export const MyReferral = ({
  referralLayers,
  claimRequests,
  loadMore,
  createClaimRequest,
}: {
  referralLayers: TClaimRequest[];
  claimRequests: TClaimHistory[];
  loadMore: () => void;
  createClaimRequest: any;
}) => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [isShowSelectWallets, setIsShowSelectWallets] =
    useState<boolean>(false);
  const [walletSelected, setWalletSelected] = useState<string>(
    wallets[0]?.address || ""
  );
  const { currentNetwork } = useNetwork();

  const suiPriceUsd = useSelector(
    (state: RootState) => state.metadata.suiPriceUsd
  );
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const claimableAmount = useMemo(() => {
    return referralLayers.reduce(
      (acc, layer) => acc + Number(layer.availableCommissions),
      0
    );
  }, [referralLayers]);

  const totalUsdClaimed = useMemo(() => {
    return referralLayers.reduce(
      (acc, layer) => acc + Number(layer.totalClaimed),
      0
    );
  }, [referralLayers]);

  const canClaim = useMemo(() => {
    return new BigNumber(claimableAmount).comparedTo(0) > 0;
  }, [claimableAmount]);

  return (
    <div>
      <div
        className="relative rounded-[8px_8px_0px_0px] bg-[url('/images/BgMyReferralMobile.png')] p-[8px] pb-0 md:bg-[url('/images/BgMyReferral.png')] md:p-[16px]"
        style={{
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <SubFlagIcon className="absolute left-[27px] top-[-3px] hidden md:block" />
        <FlagIcon className="absolute left-[22px] top-[-3px] h-[24px] w-[22px] md:left-[32px] md:h-auto md:w-auto" />
        <div className="mb-3 flex items-center gap-[24px] md:mb-4">
          <div className="min-w-[40px] md:min-w-[64px]"></div>
          <div className="flex w-[calc(100%-64px)] gap-[24px]">
            <div className="text-white-600 w-1/3 text-center text-[12px] leading-[18px]">
              REFERRAL COUNT
            </div>
            <div className="text-white-600 w-1/3 text-center text-[12px] leading-[18px]">
              CLAIMABLE VOLUME
            </div>
            <div className="text-white-600 w-1/3 text-center text-[12px] leading-[18px]">
              LIFETIME VOLUME
            </div>
          </div>
        </div>
        {referralLayers.map((layer) => (
          <div
            key={layer.level}
            className="flex items-center gap-[24px] pb-[16px]"
          >
            <div className="bg-black-300 text-white-800 rounded-8 border-white-50 inline-block min-w-[40px] border p-[10px] text-center text-[12px] leading-[18px] md:min-w-[64px]">
              {isMobile ? "L" : "Layer"} {layer.level}
            </div>
            <div className="flex w-[calc(100%-64px)] gap-[24px]">
              <div className="text-neutral-0 bg-black-900 rounded-8 border-white-50 w-1/3 border px-2 py-2 text-center font-medium md:px-4">
                {layer.totalReferrals}
              </div>
              <div className="bg-black-900 rounded-8 border-white-50 w-1/3 border px-2 py-2 text-end md:px-4">
                <span className="mr-[8px] text-[14px] font-medium leading-[20px] text-green-500">
                  {formatNumber(
                    new BigNumber(layer.tradingVolume)
                      .minus(layer.claimedTradingVolume)
                      .toString(),
                    8,
                    "0"
                  )}
                </span>
                <span className="text-white-600 text-[12px] leading-[18px]">
                  SUI
                </span>
              </div>
              <div className="bg-black-900 rounded-8 border-white-50 w-1/3 border px-2 py-2 text-end md:px-4">
                <span className="mr-[8px] text-[14px] font-medium leading-[20px] text-green-500">
                  {formatNumber(layer.lifeTimeTradingVolume, 8, "0")}
                </span>
                <span className="text-white-600 text-[12px] leading-[18px]">
                  SUI
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
      <div
        className="h-[16px] bg-[url('/images/DividerMyReferralMobile.png')] md:bg-[url('/images/DividerMyReferral.png')]"
        style={{
          backgroundSize: "100% 100%",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      />
      <div
        className="mb-[16px] flex gap-[8px] bg-[url('/images/BgMyReferralFooter.png')] p-[4px_8px_8px_8px] md:p-[8px_16px_16px_16px]"
        style={{
          backgroundSize: "100% 100%",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="border-white-50 bg-overlay-600 rounded-8 w-1/2 border px-[16px] py-[10px] md:w-2/5">
          <div className="text-white-500 mb-[10px] text-[10px]">
            Total claimed amount
          </div>
          <div className="flex gap-1 md:block">
            <div className="mb-[2px] flex gap-[4px]">
              <AppLogoNetwork
                network={currentNetwork}
                isBase
                className="h-[16px] w-[16px]"
              />
              {formatNumber(totalUsdClaimed, 8, "0")}
            </div>
            <div className="text-white-500 text-[12px]">
              {"≈"}
              <AppNumber
                value={multipliedBN(totalUsdClaimed, suiPriceUsd)}
                isForUSD
                className="body-sm-regular-12"
              />
            </div>
          </div>
        </div>
        <div className="border-white-400 bg-overlay-600 rounded-8 w-1/2 border px-[16px] py-[10px] md:w-3/5">
          <div className="text-white-500 mb-[10px] text-[10px]">
            Claimable amount
          </div>
          <div className="flex flex-col items-start justify-between md:flex-row md:items-center">
            <div className="flex gap-1 md:block">
              <div className="mb-[2px] flex gap-[4px]">
                <AppLogoNetwork
                  network={currentNetwork}
                  isBase
                  className="h-[16px] w-[16px]"
                />
                {formatNumber(claimableAmount, 8, "0")}
              </div>
              <div className="text-white-500 text-[12px]">
                {"≈"}
                <AppNumber
                  value={multipliedBN(claimableAmount, suiPriceUsd)}
                  isForUSD
                  className="body-sm-regular-12"
                />
              </div>
            </div>
            <button
              className={`text-black-900 bg-white-0 rounded-6 mt-[8px] whitespace-nowrap px-[5px] py-[8px] text-[12px] font-medium md:mt-0 md:px-[24px] ${
                !canClaim ? "cursor-not-allowed opacity-50" : ""
              }`}
              onClick={() => setIsShowSelectWallets(true)}
              disabled={!canClaim}
            >
              Create claim request
            </button>
            {isShowSelectWallets && (
              <BaseModal
                title="Choose a wallet to claim"
                description="Please select a wallet to claim your rewards."
                isOpen={isShowSelectWallets}
                onClose={() => setIsShowSelectWallets(false)}
              >
                <SelectWallets
                  walletAddressSelected={walletSelected}
                  wallets={wallets}
                  isOnlySelect
                  networkSelected={currentNetwork}
                  onSelect={setWalletSelected}
                  createClaimRequest={() => {
                    setIsShowSelectWallets(false);
                    createClaimRequest(walletSelected);
                  }}
                />
              </BaseModal>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-start gap-[8px]">
        <WarningCircleIcon className="text-orange-500" />
        <div>
          <div className="mb-[2px] text-[12px] font-medium leading-[18px] text-orange-500">
            Your earned will reset after{" "}
            {moment().utc().endOf("months").format("YYYY-MM-DD HH:mm:ss z")}.
            Please claim when possible!
          </div>
        </div>
      </div>
      <div className="hidden md:block">
        <div className="bg-white-100 my-[16px] h-[1px]" />
        <div className="text-white-0 mb-[16px] text-[14px] font-semibold leading-[20px]">
          Claim History
        </div>
        <div className="mt-4 text-[#8d93b7]">
          {/* You have not claimed anything yet. Refer your friends to get
                rewards! */}
          <div className="text-neutral-alpha-500 border-neutral-alpha-50 flex border-b text-[12px] font-normal leading-[1.5]">
            <div className="thead w-[40%]">Request ID</div>
            <div className="thead w-[15%]">Wallet</div>
            <div className="thead flex w-[30%] justify-center">Amount</div>
            <div className="thead flex w-[15%] justify-center">Status</div>
          </div>
          <Virtuoso
            className="customer-scroll"
            style={{ height: 300 }}
            data={claimRequests}
            endReached={loadMore}
            increaseViewportBy={100}
            itemContent={(index, entry) => {
              return (
                <div
                  key={entry.requestId}
                  className={`text-white-1000 flex text-center text-[12px] ${
                    index % 2 === 0 ? "bg-white-25" : ""
                  }`}
                >
                  <div className="td w-[40%] !py-1.5">{entry.requestId}</div>
                  <div className="td text-white-0 flex w-[15%] gap-1 !py-1.5 text-[12px] ">
                    <div className="flex items-center gap-1">
                      <AppUserAddress
                        network={currentNetwork}
                        address={entry.receiver}
                        className="text-white-1000"
                      />
                      <AppCopy
                        message={entry.receiver}
                        className="text-white-600 hover:text-white-1000 h-[14px] w-[14px]"
                      />
                    </div>
                  </div>
                  <div className="td flex w-[30%] justify-center gap-1 !py-1.5">
                    <div className="flex items-center gap-[4px]">
                      {formatNumber(entry.totalAmount, 4, "0")}
                      <AppLogoNetwork
                        network={currentNetwork}
                        isBase
                        className="h-[16px] w-[16px]"
                      />
                    </div>
                    <div className="text-white-500 flex text-[12px]">
                      <div className="text-white-500 flex items-center justify-center text-[16px] leading-[16px]">
                        {" "}
                        {" ≈ "}
                      </div>

                      <AppNumber
                        value={multipliedBN(entry.totalAmount, suiPriceUsd)}
                        isForUSD
                        className="body-sm-regular-12"
                      />
                    </div>
                  </div>
                  <div className="td text-white-0 flex w-[15%] justify-center gap-1 !py-1.5 text-[12px] capitalize">
                    <div className="rounded-4 border border-green-900 bg-green-900 px-1 text-[10px] font-[500] leading-[16px] text-green-500">
                      {entry.status}
                    </div>
                  </div>
                </div>
              );
            }}
          />
        </div>
      </div>
    </div>
  );
};

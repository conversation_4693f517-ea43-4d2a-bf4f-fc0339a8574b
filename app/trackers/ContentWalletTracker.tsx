"use client";
import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Close,
  FlashIcon,
  SearchIcon,
  SettingsIcon,
  Setting,
  LinkExternal,
} from "@/assets/icons";
import AppInput from "@/components/AppInput";
import { ModalLiveTrades } from "@/modals/ModalLiveTrades";
import { ModalQuickBuySettings } from "@/modals";
import WalletManager from "@/components/WalletTracker/WalletManager";
import LiveTrades from "@/components/WalletTracker/LiveTrades";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/index";
import { setIsShowModalWalletTracker } from "@/store/metadata.store";
import { setWalletTracker } from "@/store/user.store";
import { AppLogoNetwork } from "@/components/AppLogoNetwork";
import { NumericFormat } from "react-number-format";
import { debounce } from "lodash";
import { isZero } from "@/utils/helper";
import rf from "@/services/RequestFactory";
import { TGroup } from "@/types/wallet-tracker";
import Tooltip from "rc-tooltip";
import Link from "next/link";
import { AppButton } from "@/components";
import ModalAddWallet from "@/modals/ModalAddWallet";
import { useSettingsOrder } from "@/hooks";
import { useNetwork } from "@/context";

const MENU = [
  {
    name: "Wallet Manager",
    key: "wallet-manager",
  },
  {
    name: "Live Trades",
    key: "live-trades",
  },
];

export const ContentWalletTracker = ({
  size,
  isPage,
}: {
  size: any;
  isPage?: boolean;
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [group, setGroup] = useState<TGroup>();
  const [groups, setGroups] = useState<TGroup[]>([]);
  const [activeTab, setActiveTab] = useState<string>("wallet-manager");
  const [searchWallet, setSearchWallet] = useState<string>("");
  const [isSettingsOpen, setSettingsOpen] = useState<boolean>(false);
  const [isLiveTradesSetting, setLiveTradesSetting] = useState<boolean>(false);
  const [buyAmount, setBuyAmount] = useState<any>("");
  const [isAddWalletOpen, setAddWalletOpen] = useState<boolean>(false);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const walletTracker = useSelector(
    (state: RootState) => state.user.walletTracker
  );
  const { currentNetwork } = useNetwork();
  const { updateSettingsQuickOrder, settingsQuickOrder } = useSettingsOrder();

  const debounceUpdate = useCallback(
    debounce(
      (nextValue: string) =>
        updateSettingsQuickOrder({
          ...settingsQuickOrder,
          buyQuickAmount: +nextValue,
        }),
      1000
    ),
    [settingsQuickOrder]
  );

  useEffect(() => {
    if (!isZero(settingsQuickOrder?.buyQuickAmount || 0)) {
      setBuyAmount(settingsQuickOrder?.buyQuickAmount || 0);
    }
  }, [settingsQuickOrder]);

  const onClose = () => {
    dispatch(setIsShowModalWalletTracker({ isShow: false }));
  };

  const getGroups = async () => {
    try {
      const res = await rf.getRequest("WalletTrackerRequest").getGroups();
      if (res.length) {
        setGroups(res);
        // Group 1st
        setGroup(res[0]);
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    getGroups().then();
  }, []);

  const getWallets = async () => {
    if (!groups || !groups.length) return;
    try {
      let allWallets: any[] = [];
      for (const g of groups) {
        const res = await rf
          .getRequest("WalletTrackerRequest")
          .getWallets(g.id);
        if (Array.isArray(res)) {
          allWallets = allWallets.concat(res);
        }
      }
      dispatch(
        setWalletTracker({
          walletTracker: {
            ...walletTracker,
            wallets: allWallets,
          },
        })
      );
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <>
      <div className="relative flex h-[88px] flex-col items-center justify-center p-2 md:h-[48px] md:flex-row md:justify-between">
        <div className="mb-2 flex h-[32px] w-full items-center gap-1 p-1 md:mb-0 md:w-[274px]">
          {MENU.map((item) => (
            <div
              key={item.key}
              onClick={() => setActiveTab(item.key)}
              className={`${
                activeTab === item.key
                  ? "bg-white-100 text-white-1000"
                  : "text-white-500"
              } flex h-[24px] flex-1 cursor-pointer items-center justify-center rounded p-1 text-[12px] font-semibold leading-4`}
            >
              {item.name}
            </div>
          ))}
        </div>

        {!isPage && (
          <div className="drag-handle text-white-500 hover:text-white-1000 absolute left-1/2 top-1/3 hidden -translate-x-1/2 -translate-y-1/2 rotate-90 cursor-move md:block">
            ⠿
          </div>
        )}

        <div
          className={`border-white-100 flex w-full items-center ${
            activeTab === "wallet-manager" ? "justify-between" : "justify-end"
          } gap-[15px] border-b pb-2 md:w-auto  md:border-0`}
        >
          {activeTab === "wallet-manager" ? (
            <>
              <div>
                <AppInput
                  value={searchWallet}
                  onChange={(e) => setSearchWallet(e.target.value)}
                  icon={
                    <SearchIcon className="text-white-500 h-[16px] w-[16px]" />
                  }
                  placeholder="Search by name or address"
                  rootClassName="bg-transparent md:w-[160px] w-[264px] h-[32px] p-2 gap-1 border-white-100 border body-sm-regular-12"
                  className="placeholder:text-white-300 w-[124px] truncate placeholder:text-[12px] placeholder:font-normal placeholder:leading-[18px]"
                />
              </div>
              {isPage && (
                <>
                  <AppButton
                    onClick={() => setAddWalletOpen(true)}
                    variant="buy"
                    size="medium"
                    className="!py-2"
                  >
                    Add Wallet
                  </AppButton>

                  {isAddWalletOpen && (
                    <ModalAddWallet
                      isOpen={isAddWalletOpen}
                      group={group}
                      onClose={() => setAddWalletOpen(false)}
                      fetchGroups={getGroups}
                      onFetchData={getWallets}
                    />
                  )}
                </>
              )}
            </>
          ) : (
            <div className="flex items-center gap-2">
              <Tooltip overlay={`Live Trades Settings`} placement="bottom">
                <div
                  className="mr-2 cursor-pointer"
                  onClick={() => setLiveTradesSetting(true)}
                >
                  <Setting />
                </div>
              </Tooltip>
              <div
                className="cursor-pointer"
                onClick={() => setSettingsOpen(true)}
              >
                <SettingsIcon />
              </div>
              <div className="border-white-150 flex h-[32px] items-center justify-between rounded-[6px] border p-2">
                <FlashIcon />
                <div className="flex items-center gap-1">
                  <NumericFormat
                    disabled={!accessToken}
                    value={buyAmount}
                    onChange={(e) => {
                      setBuyAmount(e.target.value.replace(/,/g, ""));
                      if (!accessToken) return;
                      debounceUpdate(e.target.value.replace(/,/g, "") || "");
                    }}
                    thousandSeparator=","
                    valueIsNumericString
                    decimalScale={6}
                    className="action-sm-medium-12 text-white-1000 placeholder:text-white-300 w-[40px] bg-transparent text-right outline-none"
                  />
                  <AppLogoNetwork network={currentNetwork} isBase />
                </div>
              </div>
            </div>
          )}

          {!isPage && (
            <div className=" border-white-100 flex items-center gap-3 border-l pl-6">
              <Link href="/trackers" onClick={onClose}>
                <LinkExternal />
              </Link>

              <div className="cursor-pointer" onClick={onClose}>
                <Close />
              </div>
            </div>
          )}
        </div>
      </div>

      {activeTab === "wallet-manager" && (
        <WalletManager
          size={size}
          searchWallet={searchWallet}
          group={group}
          fetchGroups={getGroups}
          getWallets={getWallets}
          isPage={isPage}
        />
      )}
      {activeTab === "live-trades" && (
        <LiveTrades size={size} groups={groups} />
      )}

      {isSettingsOpen && (
        <ModalQuickBuySettings
          isOpen={isSettingsOpen}
          onClose={() => setSettingsOpen(false)}
        />
      )}

      {isLiveTradesSetting && (
        <ModalLiveTrades
          isOpen={isLiveTradesSetting}
          onClose={() => setLiveTradesSetting(false)}
        />
      )}
    </>
  );
};

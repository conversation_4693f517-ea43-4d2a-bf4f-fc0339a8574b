"use client";

import { ReactNode } from "react";
import { PrivyProvider } from "@privy-io/react-auth";
import { hypeMainnet<PERSON>hain } from "./networkChains";

interface HypeWalletProviderProps {
  children: ReactNode;
}

const HYPE_PRIVY_APP_ID =
  process.env.NEXT_PUBLIC_HYPE_PRIVY_APP_ID || "cmcvqsfzm0165jl0ms1i81h70";

export const HypeWalletProvider = ({ children }: HypeWalletProviderProps) => {
  return (
    <PrivyProvider
      appId={HYPE_PRIVY_APP_ID}
      config={{
        loginMethods: ["wallet", "email", "google", "twitter"],
        embeddedWallets: {
          ethereum: {
            createOnLogin: "users-without-wallets",
          },
        },
        appearance: {
          walletChainType: "ethereum-only",
          theme: "dark",
          accentColor: "#676FFF",
        },
        defaultChain: hypeMain<PERSON><PERSON>hain,
        supportedChains: [hypeMain<PERSON><PERSON>hai<PERSON>],
      }}
    >
      {children}
    </PrivyProvider>
  );
};

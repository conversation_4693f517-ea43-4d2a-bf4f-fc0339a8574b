import BigNumber from "bignumber.js";
import clsx from "clsx";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import {
  CheckboxIcon,
  CheckedIcon,
  ChevronDownIcon,
  EditIcon,
  ImportIcon,
  LinkExternalIcon,
  PlusIcon,
  SearchIcon,
  ThreeDotsIcon,
  TrashIcon,
} from "@/assets/icons";
import {
  AppAvatarToken,
  AppButton,
  AppCopy,
  AppLogoNetwork,
  AppNumber,
  AppPopover,
  AppToggle,
} from "@/components";
import { toastError, toastSuccess } from "@/libs/toast";
import { ModalConfirm, ModalDeposit, ModalImportWallet } from "@/modals";
import { ModalEditWallet } from "@/modals/ModalEditWallet";
import { ModalGenerateNewWallet } from "@/modals/ModalGenerateNewWallet";
import rf from "@/services/RequestFactory";
import { RootState, AppDispatch } from "@/store";
import { getWalletsUser } from "@/store/user.store";
import { TWallet } from "@/types";
import { TBalance } from "@/types/balance.type";
import { formatShortAddress } from "@/utils/format";
import { getLinkAddressExplorer, isZero } from "@/utils/helper";
import { useNetwork } from "@/context/network";

const TokensHolder = ({
  wallet,
  hideLowBalance,
}: {
  wallet: TWallet;
  hideLowBalance: boolean;
}) => {
  const [tokenBalances, setTokenBalances] = useState<TBalance[]>([]);
  const [tokensShow, setTokensShow] = useState<TBalance[]>([]);
  const isTablet = useMediaQuery({ query: "(max-width: 992px)" });

  const userId = useSelector((state: RootState) => state.user.userId);
  const balances = useSelector((state: RootState) => state.user.balances);

  const positions = useSelector((state: RootState) => state.user.positions);

  useEffect(() => {
    let dataTokens = tokenBalances;
    dataTokens = dataTokens?.map((item) => {
      const position = positions.find(
        (position) =>
          position.walletName === item.walletAddress &&
          item.token.address === position.token.address
      );
      if (position) {
        console.log(
          "item.token",
          item.token.symbol,
          position?.balance,
          position?.balanceToUsd,
          position
        );
        return {
          ...item,
          balanceToUsd: position?.balanceToUsd,
        };
      }
      return {
        ...item,
        balanceToUsd: item?.balanceUsd,
      };
    });

    if (hideLowBalance) {
      dataTokens = dataTokens.filter((item) => {
        return new BigNumber(item?.balanceToUsd || 0).comparedTo(1) > 0;
      });
    }

    setTokensShow(dataTokens);
  }, [hideLowBalance, tokenBalances, positions]);

  useEffect(() => {
    const tokenBalances = balances?.filter((balance) => {
      return balance.walletAddress === wallet.address;
    });

    setTokenBalances(tokenBalances);
  }, [userId, wallet, balances]);

  if (!tokensShow.length) {
    return (
      <div className="body-sm-regular-12 py-[12px] text-center">No data</div>
    );
  }

  return (
    <div className="tablet:pl-[32px] tablet:pr-0 px-4 pb-[12px]">
      <div className="flex flex-col gap-[8px]">
        {tokensShow.map((item, index: number) => {
          return (
            <div
              key={index}
              className={`grid grid-cols-${isTablet ? 3 : 4} py-[4px]`}
            >
              <div className="flex items-center gap-2">
                <AppAvatarToken
                  size={20}
                  image={
                    item?.token?.logoImageUrl || item?.token?.iconUrl || ""
                  }
                  className="h-[20px] w-[20px]"
                />
                <div className="body-xs-regular-10">{item?.token?.symbol}</div>
                <AppCopy message={item?.token?.address} />
              </div>

              <div className="text-white-500 body-sm-regular-12 tablet:justify-start flex items-center justify-end gap-1">
                <AppNumber value={item?.balance} className="text-white-500" />$
                {item?.token?.symbol}
              </div>

              <div className="text-white-500 body-sm-regular-12 tablet:justify-start flex items-center justify-end gap-1">
                {!isZero(item?.balanceToUsd) ? "~" : ""}
                <AppNumber
                  value={item?.balanceToUsd}
                  isForUSD
                  className="text-white-500"
                />
              </div>
              {/* {!isTablet && (
                <div className="flex gap-1 justify-center items-center body-sm-regular-12">
                  <Link
                    to="#"
                    className=" flex gap-1 items-center text-blue-500"
                  >
                    View chart <ExternalLink />
                  </Link>
                </div>
              )} */}
            </div>
          );
        })}
      </div>
    </div>
  );
};

const WalletItem = ({
  wallet,
  no,
  walletsSelected,
  setWalletsSelected,
  isRemove,
  hideLowBalance,
  position,
}: {
  wallet: TWallet;
  no: number;
  isRemove: boolean;
  hideLowBalance: boolean;
  walletsSelected: string[];
  setWalletsSelected: (wallets: string[]) => void;
  position?: "left" | "top" | "right" | "bottom" | "custom";
}) => {
  const [isOpenDepositModal, setIsOpenDepositModal] = useState<boolean>(false);
  const [isOpenEditNameModal, setIsOpenEditNameModal] =
    useState<boolean>(false);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [isOpenOptions, setIsOpenOptions] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const { currentNetwork } = useNetwork();

  const isSelected = walletsSelected.some((item) => item === wallet.address);
  const onSelectWallet = () => {
    if (isSelected) {
      const newWallets = walletsSelected.filter(
        (item) => item !== wallet.address
      );
      setWalletsSelected(newWallets);
      return;
    }
    setWalletsSelected([...walletsSelected, wallet.address]);
  };
  const OPTIONS = [
    {
      label: (
        <div
          className="flex h-full w-full items-center"
          onClick={() => setIsOpenDepositModal(true)}
        >
          Deposit
        </div>
      ),
      value: "deposit",
    },
    {
      label: (
        <div
          className="flex h-full w-full items-center"
          onClick={() => setIsOpenEditNameModal(true)}
        >
          Edit
        </div>
      ),
      value: "edit",
    },
  ];

  return (
    <div className="border-white-50 border-b border-dashed">
      <div className="hover:bg-white-50 max-tablet:pl-[8px] flex h-[42px] items-center justify-between pl-[12px]">
        <div className="flex min-w-[160px] items-center  gap-2">
          {isRemove && (
            <div className="cursor-pointer" onClick={onSelectWallet}>
              {isSelected ? <CheckedIcon /> : <CheckboxIcon />}
            </div>
          )}

          <div className="body-xs-regular-10 text-white-500">{no}.</div>
          <div className="body-sm-medium-12 border-white-100 bg-white-100 min-w-[46px] max-w-[100px] truncate rounded-[4px] border px-[4px]">
            {wallet.aliasName}
          </div>
          <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
            {formatShortAddress(wallet.address, 5, 3)}{" "}
            <AppCopy message={wallet.address} />
            <a
              href={getLinkAddressExplorer(currentNetwork, wallet?.address)}
              className="text-blue-500"
              target="_blank"
            >
              <LinkExternalIcon />
            </a>
          </div>
        </div>

        <div className="flex h-full items-center">
          <div className="tablet:px-4 body-sm-regular-12 text-white-800 border-white-50 flex h-full items-center gap-1 border-r px-2">
            <AppNumber value={wallet.balance} />
            <AppLogoNetwork
              network={currentNetwork}
              isBase
              className="text-white-500 h-[12px] w-[12px]"
            />
          </div>

          {isMobile ? (
            <div className="tablet:px-4 action-xs-medium-12 border-white-50 flex h-full cursor-pointer items-center border-r px-2">
              <AppPopover
                isOpen={isOpenOptions}
                onToggle={() => setIsOpenOptions(!isOpenOptions)}
                onClose={() => setIsOpenOptions(false)}
                trigger={
                  <div>
                    <ThreeDotsIcon />
                  </div>
                }
                content={
                  <div className="flex flex-col gap-[4px] rounded-[8px] bg-[#212224] p-[4px]">
                    {OPTIONS.map((item, index) => {
                      return (
                        <div
                          onClick={() => setIsOpenOptions(false)}
                          key={index}
                          className="hover:bg-white-50 hover:border-white-100 body-sm-regular-12 flex h-[36px] min-w-[100px]  cursor-pointer items-center rounded-[6px] px-[8px] py-[4px]"
                        >
                          {item.label}
                        </div>
                      );
                    })}
                  </div>
                }
                position={position}
                customPosition={
                  position === "custom" ? "top-[-94px] left-[-90px]" : ""
                }
              />
            </div>
          ) : (
            <>
              <div className="tablet:px-4 action-xs-medium-12 border-white-50 flex h-full cursor-pointer items-center border-r px-2">
                <div
                  className="cursor-pointer"
                  onClick={() => setIsOpenDepositModal(true)}
                >
                  <span className="tablet:block hidden">Deposit</span>
                  <div className="tablet:hidden block">
                    <PlusIcon className="h-4 w-4" />
                  </div>
                </div>
              </div>
              <div className="tablet:px-4 border-white-50 flex h-full items-center border-r px-2 text-[12px]">
                <EditIcon
                  className="tablet:w-5 tablet:h-5 h-4 w-4 cursor-pointer"
                  onClick={() => setIsOpenEditNameModal(true)}
                />
              </div>
            </>
          )}
          <div
            className="tablet:px-4 flex h-full items-center px-2"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <ChevronDownIcon
              className={`cursor-pointer transition-all duration-500 ${
                isExpanded ? "rotate-[-180deg]" : ""
              }`}
            />
          </div>
        </div>
      </div>

      {isExpanded && (
        <TokensHolder wallet={wallet} hideLowBalance={hideLowBalance} />
      )}

      {isOpenDepositModal && (
        <ModalDeposit
          isOpen={isOpenDepositModal}
          wallet={wallet}
          onClose={() => setIsOpenDepositModal(false)}
        />
      )}

      {isOpenEditNameModal && (
        <ModalEditWallet
          isOpen={isOpenEditNameModal}
          wallet={wallet}
          onClose={() => setIsOpenEditNameModal(false)}
        />
      )}
    </div>
  );
};

const ButtonGenerateWallet = () => {
  const [isOpenGenerateWalletModal, setIsOpenGenerateWalletModal] =
    useState<boolean>(false);
  const [newWallets, setNewWallets] = useState<TWallet[]>([]);
  const { currentNetwork } = useNetwork();

  const dispatch = useDispatch<AppDispatch>();
  const onGenerateWallet = async (numberWallets: number) => {
    try {
      const res = await rf
        .getRequest("WalletRequest")
        .getGenerateWallets(currentNetwork, {
          numberWallets,
        });
      setNewWallets(res);
      dispatch(getWalletsUser({ network: currentNetwork }));
      setIsOpenGenerateWalletModal(true);
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };
  return (
    <>
      <AppButton
        onClick={() => onGenerateWallet(1)}
        variant="buy"
        className="tablet:flex-1 flex items-center justify-center gap-1"
      >
        <PlusIcon />
        Generate new
      </AppButton>

      {isOpenGenerateWalletModal && (
        <ModalGenerateNewWallet
          wallets={newWallets}
          isOpen={isOpenGenerateWalletModal}
          onClose={() => setIsOpenGenerateWalletModal(false)}
        />
      )}
    </>
  );
};

const ButtonImportWallet = () => {
  const [isOpenModalImportWallet, setIsOpenModalImportWallet] =
    useState<boolean>(false);
  return (
    <>
      <AppButton
        onClick={() => setIsOpenModalImportWallet(true)}
        variant="outline"
        className="flex items-center justify-center gap-1"
      >
        <ImportIcon />
        Import
      </AppButton>

      {isOpenModalImportWallet && (
        <ModalImportWallet
          isOpen={isOpenModalImportWallet}
          onClose={() => setIsOpenModalImportWallet(false)}
        />
      )}
    </>
  );
};

const GroupButton = ({
  walletsSelected,
  setIsOpenConfirmDeleteWallet,
  isRemove,
  setIsRemove,
}: {
  walletsSelected: string[];
  setIsOpenConfirmDeleteWallet: (value: boolean) => void;
  isRemove: boolean;
  setIsRemove: (value: boolean) => void;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  return (
    <div className="tablet:justify-start tablet:w-auto flex w-full items-center justify-end gap-[8px]">
      {walletsSelected.length ? (
        <AppButton
          onClick={() => setIsOpenConfirmDeleteWallet(true)}
          variant="sell"
          className="flex items-center justify-center gap-1 px-[4px] py-[4px]"
        >
          <TrashIcon />
          {isMobile ? "" : "Remove"}
          <div className="body-xs-medium-10 rounded-[4px] border border-red-800 bg-red-800 px-[4px] text-red-400">
            {walletsSelected.length}
          </div>
        </AppButton>
      ) : (
        <AppButton variant="outline" onClick={() => setIsRemove(!isRemove)}>
          <TrashIcon />
        </AppButton>
      )}

      <ButtonImportWallet />

      <ButtonGenerateWallet />
    </div>
  );
};

export const SectionMain = () => {
  const [search, setSearch] = useState<string>("");
  const [hideLowBalance, setHideLowBalance] = useState<boolean>(true);
  const [isRemove, setIsRemove] = useState<boolean>(false);
  const [walletsSelected, setWalletsSelected] = useState<string[]>([]);
  const [isOpenConfirmDeleteWallet, setIsOpenConfirmDeleteWallet] =
    useState<boolean>(false);

  const [walletsShow, setWalletsShow] = useState<TWallet[]>([]);

  const dispatch = useDispatch<AppDispatch>();
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const { currentNetwork } = useNetwork();

  useEffect(() => {
    dispatch(getWalletsUser({ network: currentNetwork }));
  }, []);

  useEffect(() => {
    let dataWallet = wallets;

    if (!!search) {
      dataWallet = dataWallet.filter(
        (item) =>
          item.address?.toLowerCase().includes(search?.toLowerCase()) ||
          item.aliasName?.toLowerCase().includes(search?.toLowerCase())
      );
    }
    setWalletsShow(dataWallet);
  }, [search, wallets]);

  const onRemoveWallet = async () => {
    try {
      await rf
        .getRequest("WalletRequest")
        .inactiveWallet(currentNetwork, walletsSelected);
      dispatch(getWalletsUser({ network: currentNetwork }));
      setWalletsSelected([]);
      setIsOpenConfirmDeleteWallet(false);
      toastSuccess("Success", "Removed wallets successful!");
      setIsRemove(false);
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  const getPosition = useCallback(
    (index: number) => {
      if (walletsShow.length > 2) {
        if (
          index === walletsShow.length - 2 ||
          index === walletsShow.length - 1
        ) {
          return "custom";
        } else {
          return "left";
        }
      } else {
        return "left";
      }
    },
    [walletsShow]
  );

  return (
    <>
      <div className="max-tablet:h-[calc(100vh-94px-220px)] md:max-tablet:h-[calc(100vh-52px-220px)] h-full py-[12px]">
        <div className="tablet:flex-row tablet:gap-0 max-tablet:px-[8px] flex flex-col items-center justify-between gap-2 px-4">
          <div className="tablet:w-auto max-tablet:flex-col max-tablet:items-start max-tablet:gap-[8px] flex w-full items-center gap-[8px]">
            <div className="max-tablet:w-full flex items-center justify-between">
              <div className="action-sm-semibold-12">
                SUI Wallets ({wallets.length})
              </div>
              <div className="tablet:hidden">
                <GroupButton
                  walletsSelected={walletsSelected}
                  setIsOpenConfirmDeleteWallet={setIsOpenConfirmDeleteWallet}
                  isRemove={isRemove}
                  setIsRemove={setIsRemove}
                />
              </div>
            </div>

            <div
              className={clsx(
                "flex w-full items-center gap-[8px]",
                "tablet:w-auto max-tablet:items-center max-tablet:gap-[8px] max-tablet:mb-[12px]"
              )}
            >
              <div className="tablet:p-2 border-white-50 max-tablet:p-[8px] flex w-full flex-1 items-center gap-[8px] rounded-[4px] border p-1 md:gap-[4px]">
                <SearchIcon />
                <input
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="tablet:w-[124px] body-sm-regular-12 placeholder:text-white-300 w-full border-0 bg-transparent outline-none"
                  placeholder="Search"
                />
              </div>

              <div className="body-xs-regular-10 text-white-700 max-tablet:justify-end flex items-center gap-1">
                <AppToggle
                  value={hideLowBalance}
                  onChange={() => setHideLowBalance(!hideLowBalance)}
                />
                {`Hide Token < $1`}
              </div>
            </div>
          </div>
          <div className="max-tablet:hidden">
            <GroupButton
              walletsSelected={walletsSelected}
              setIsOpenConfirmDeleteWallet={setIsOpenConfirmDeleteWallet}
              isRemove={isRemove}
              setIsRemove={setIsRemove}
            />
          </div>
        </div>

        <div className="customer-scroll max-tablet:h-[calc(100%-114px)] mt-[8px] flex h-[calc(100%-40px)] flex-col gap-[8px] overflow-auto">
          {!!walletsShow.length ? (
            walletsShow.map((item, index) => {
              return (
                <WalletItem
                  hideLowBalance={hideLowBalance}
                  walletsSelected={walletsSelected}
                  setWalletsSelected={setWalletsSelected}
                  isRemove={isRemove}
                  wallet={item}
                  no={index + 1}
                  key={index}
                  position={getPosition(index)}
                />
              );
            })
          ) : (
            <div className="body-sm-regular-12 mt-[20px] text-center">
              No data
            </div>
          )}
        </div>

        {isOpenConfirmDeleteWallet && (
          <ModalConfirm
            title="Are you sure you want to remove the wallet(s) ?"
            isOpen={isOpenConfirmDeleteWallet}
            onClose={() => setIsOpenConfirmDeleteWallet(false)}
            onConfirm={onRemoveWallet}
            description={"All your funds in the wallet(s) will be cleared"}
          />
        )}
      </div>
    </>
  );
};

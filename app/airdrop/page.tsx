"use client";

import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  TLeaderboardInfo,
  TLeaderboardEntry,
  Mission,
  TActiveCampaigns,
} from "@/types";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess, toastWarning } from "@/libs/toast";
import { Virtuoso } from "react-virtuoso";
import { formatNumber } from "@/utils/format";
import * as _ from "lodash";
import BigNumber from "bignumber.js";
import moment from "moment";
import { ClipLoader } from "react-spinners";
import {
  DiscordIcon,
  FlashIcon,
  Telegram,
  RankGold,
  RankSiver,
  IconCheck,
  NotificationIcon,
  XIcon,
  IconRose,
  IconTrading,
  RankBronve,
} from "@/assets/icons";
import clsx from "clsx";
import { useMediaQuery } from "react-responsive";

const getDecimalPoint = (
  value: number | string | BigNumber,
  decimalToken = 4
) => {
  if (new BigNumber(value).gte(1000)) {
    return decimalToken;
  }

  return 0;
};

const TABS = [
  {
    value: "missions",
    label: "Missions",
  },
  {
    value: "leaderboard",
    label: "Leaderboard",
  },
];

export default function LeaderBoardPage() {
  const [campaignId, setCampaignId] = useState<string | null>(null);
  const [leaderboardInfo, setLeaderboardInfo] = useState<TLeaderboardInfo>();
  const [leaderboardEntries, setLeaderboardEntries] = useState<
    TLeaderboardEntry[]
  >([]);
  const [missions, setMissions] = useState<Mission[]>([]);
  const [resolvedMissionIds, setResolvedMissionIds] = useState<string[]>([]);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const entriesPerPage = 10;
  const [activeTab, setActiveTab] = useState<string>("missions");
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  useEffect(() => {
    getCampaign().then((campaignId) => {
      if (campaignId) {
        Promise.all([
          getLeaderBoardData(campaignId),
          getMissionData(campaignId),
        ]);
      }
    });
  }, []);

  useEffect(() => {
    if (!accessToken || !campaignId) return;
    getResolvedMissions().then();
  }, [accessToken, campaignId]);

  useEffect(() => {
    if (!campaignId || !accessToken) return;
    getUserAirdropStats(campaignId).then();
  }, [campaignId, accessToken]);

  const getCampaign = async (): Promise<any> => {
    try {
      const { campaign } = (await rf
        .getRequest("CampaignsRequest")
        .getActiveCampaigns()) as {
        campaign: TActiveCampaigns;
      };
      if (campaign) {
        setCampaignId(campaign.campaignId);
      }
      return campaign.campaignId;
    } catch (error: any) {
      setCampaignId(null);
    }
  };

  const getLeaderBoardData = async (campaignId: string): Promise<void> => {
    try {
      const leaderboardRes = await rf
        .getRequest("CampaignsRequest")
        .getLeaderboard(campaignId, { limit: 100 });
      if (leaderboardRes.docs) {
        setLeaderboardEntries(leaderboardRes.docs as TLeaderboardEntry[]);
      }
    } catch (error) {
      console.error("Error fetching leaderboard entries:", error);
    }
  };

  const getUserAirdropStats = async (campaignId: string): Promise<void> => {
    try {
      const leaderboardInfoRes = await rf
        .getRequest("CampaignsRequest")
        .getUserAirdropStats(campaignId);
      if (leaderboardInfoRes) {
        setLeaderboardInfo(leaderboardInfoRes as TLeaderboardInfo);
      }
    } catch (error) {
      console.error("Error fetching user airdrop stats:", error);
    }
  };

  const getMissionData = async (campaignId: string): Promise<void> => {
    try {
      const missionsRes = await rf
        .getRequest("CampaignsRequest")
        .getMissions(campaignId);
      if (missionsRes) {
        setMissions(missionsRes as Mission[]);
      }
    } catch (error) {
      console.error("Error fetching mission data:", error);
    }
  };

  const getResolvedMissions = async () => {
    if (!campaignId) return;
    try {
      const resolvedMissionsRes = await rf
        .getRequest("CampaignsRequest")
        .getResolvedMissions(campaignId);
      if (resolvedMissionsRes) {
        setResolvedMissionIds(_.map(resolvedMissionsRes, "missionId"));
      }
    } catch (error) {
      console.error("Error fetching resolved missions:", error);
    }
  };

  const handleResolveMission = async (
    missionId: string,
    isAutoResolve: boolean
  ) => {
    try {
      const campaignsRequest = rf.getRequest("CampaignsRequest");
      await campaignsRequest.resolveMission(missionId);
      if (!isAutoResolve) {
        toastSuccess("Claim Success", "Mission resolved successfully");
      }
      if (campaignId) {
        await getMissionData(campaignId);
        await getUserAirdropStats(campaignId);
      }
    } catch (error: any) {
      if (!isAutoResolve) {
        toastError("Claim Failed", error?.message);
      }
    }
  };

  const _renderTabContent = () => {
    if (activeTab === "missions") {
      return (
        <LeaderboardSidebar
          missions={missions}
          resolvedMissionIds={resolvedMissionIds}
          handleResolveMission={handleResolveMission}
          getResolvedMissions={getResolvedMissions}
        />
      );
    }

    return (
      <LeaderboardTable
        entries={leaderboardEntries}
        entriesPerPage={entriesPerPage}
        leaderboardInfo={leaderboardInfo}
      />
    );
  };

  const _renderContent = () => {
    if (isMobile) {
      return (
        <div className="mx-auto my-2 w-[96%] overflow-auto">
          <div className="border-white-50 flex items-center justify-between border-b">
            <div className="flex items-center">
              {TABS.map((tab) => {
                const isActive = tab?.value === activeTab;
                return (
                  <div
                    key={tab.value}
                    onClick={() => setActiveTab(tab?.value)}
                    className={clsx(
                      "min-h-[24px] cursor-pointer px-[4px] pb-[5px] pt-[3px] text-[12px] md:min-h-[40px] md:p-[10px] md:text-[14px]",
                      isActive
                        ? "text-white-1000 border-white-500 border-b font-semibold"
                        : "text-white-500"
                    )}
                  >
                    {tab?.label} {isActive}
                  </div>
                );
              })}
            </div>
          </div>
          {_renderTabContent()}
        </div>
      );
    }
    return (
      <div className="bg-black-900 mx-4 flex h-full justify-center gap-6 py-8 md:flex-row">
        <div className="border-r-1 border-white-50 desktop:max-w-[816px] w-full gap-4 pr-6">
          <LeaderboardSidebar
            missions={missions}
            resolvedMissionIds={resolvedMissionIds}
            handleResolveMission={handleResolveMission}
            getResolvedMissions={getResolvedMissions}
          />
        </div>
        <div className="desktop:max-w-[360px] w-full">
          <LeaderboardTable
            entries={leaderboardEntries}
            entriesPerPage={entriesPerPage}
            leaderboardInfo={leaderboardInfo}
          />
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="relative mx-auto w-full max-w-full md:mb-4">
        <div className="relative flex aspect-[375/164] h-full min-h-[164px] flex-col items-center justify-center md:aspect-[auto] md:min-h-[348px]">
          <div className="max-w-screen absolute !z-0 h-full w-full bg-[url('/images/BannerAirdropMobile.png')] bg-cover bg-center bg-no-repeat md:bg-[url('/images/BannerAirdrop.png')]" />
          <div className="relative z-[99] md:absolute md:top-[29%] md:pb-7">
            <h1 className="text-white-900 flex items-center justify-center text-[20px] font-semibold leading-[24px] md:ml-14 md:h-[82px] md:text-[52px] md:leading-[62.4px]">
              Airdrop Points
            </h1>
            <p className="text-white-700 flex items-center justify-center text-[10px] font-normal leading-[16px] md:ml-14 md:gap-[10px] md:text-[14px] md:leading-[20px]">
              Earn points by trading and completing missions.
            </p>
            {accessToken && (
              <div className="gap-[10px] md:ml-14 md:mt-[54px]">
                <div className="mb-[10px] mt-3 flex items-center justify-center gap-5 md:my-5 md:gap-4">
                  <StatsItem
                    label="Your ranking"
                    value={new BigNumber(
                      leaderboardInfo?.rank || "0"
                    ).toFormat()}
                    tooltip="Your current ranking on the airdrop"
                  />
                  <StatsItem
                    label="Total points"
                    value={formatNumber(
                      leaderboardInfo?.totalPoints || 0,
                      getDecimalPoint(leaderboardInfo?.totalPoints || 0),
                      "0"
                    )}
                    tooltip="Your total accumulated points"
                  />
                </div>

                <div className="text-white-500 flex items-center justify-center text-[10px] font-normal leading-[16px]">
                  Last updated:{"  "}
                  {leaderboardInfo?.lastUpdatedAt ? (
                    moment(leaderboardInfo?.lastUpdatedAt)
                      .utc()
                      .format("YYYY-MM-DD HH:mm:ss z")
                  ) : (
                    <div className="ml-2">
                      <ClipLoader color="#8d93b7" size={15} />
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
        {_renderContent()}
      </div>
    </>
  );
}

interface LeaderboardTableProps {
  entries: TLeaderboardEntry[];
  entriesPerPage: number;
  leaderboardInfo?: TLeaderboardInfo;
}

const LeaderboardTable: React.FC<LeaderboardTableProps> = ({
  entries,
  leaderboardInfo,
}) => {
  const isUserInLeaderboard =
    leaderboardInfo && Number(leaderboardInfo?.rank) <= 100;

  const renderRankingIcon = (rank: number) => {
    if (rank === 1) return <RankGold />;
    if (rank === 2) return <RankSiver />;
    if (rank === 3) return <RankBronve />;
    return rank;
  };

  return (
    <div className="overflow-auto">
      <h2 className="text-white-1000 mb-4 hidden rounded-[2px] text-[16px] font-semibold leading-[16px] md:block">
        Leaderboard Top 100
      </h2>

      <div className="text-white-800 mb-2 mt-4 block rounded-[2px] text-[10px] font-medium leading-[16px] md:hidden">
        Top 100 Participants
      </div>

      <div>
        <div className="text-white-500 border-white-50 flex border-b border-r border-t text-[12px] font-normal leading-[18px]">
          <div className="thead border-white-50 box-border min-h-[36px] w-[20%] border-l">
            RANKING
          </div>
          <div className="thead border-white-50 min-h-[36px] w-[40%] border-l">
            USER
          </div>
          <div className="thead border-white-50 min-h-[36px] w-[40%] border-l">
            TOTAL POINTS
          </div>
        </div>
        <Virtuoso
          className="customer-scroll min-h-[276px]"
          data={entries}
          increaseViewportBy={100}
          itemContent={(index, entry) => {
            return (
              <div
                key={entry.userId}
                className={`text-white-1000 border-white-50 flex items-center border-b text-center text-[12px] font-normal leading-[18px]
                ${index % 2 === 0 ? "bg-neutral-alpha-100" : "bg-white-25"}
              `}
              >
                <div className="td box-border flex min-h-[46px] w-[20%] items-center justify-center">
                  {renderRankingIcon(entry.rank)}
                </div>
                <div className="td border-white-50 min-h-[46px] w-[40%] border-l text-center">
                  {entry.userId.slice(0, 4)}********
                </div>
                <div className="td border-white-50 min-h-[46px] w-[40%] border-l text-center">
                  {formatNumber(
                    entry.totalPoints,
                    getDecimalPoint(entry?.totalPoints)
                  )}
                </div>
              </div>
            );
          }}
        />
      </div>
      {!isUserInLeaderboard && (
        <div className="text-white-1000 border-white-50 flex min-h-[46px] items-center justify-center border bg-[#27D9710F] px-2 py-[10px] text-[10px] font-normal leading-[16px]">
          You&apos;re not on the leaderboard yet. Earn more points and climb up!
        </div>
      )}
    </div>
  );
};

interface StatsItemProps {
  label: string;
  isRank?: boolean;
  value: number | string;
  tooltip: string;
}

const StatsItem: React.FC<StatsItemProps> = ({ label, value }) => (
  <div
    className="z-[99] flex h-[48px] items-center bg-[url('/images/button_banner_mobile.png')] bg-contain bg-left bg-no-repeat md:mt-0
      md:h-[32px] md:min-w-[133px]
      md:bg-[url('/images/button_banner.png')]"
  >
    <div className="ml-[24px] flex flex-col md:flex-row md:items-center md:gap-2">
      <div className="md:body-md-regular-14 body-sm-regular-12 text-white-700">
        {label}
      </div>
      <div className="md:body-md-semibold-14 body-sm-semibold-12 text-white-900">
        {value}
      </div>
    </div>
  </div>
);

const MISSIONS = {
  MAKE_FIRST_TRADE: "Make your first trade",
  JOIN_TELEGRAM_ANNOUNCEMENT: "Join RaidenX Announcement Telegram",
  FOLLOW_X: "Follow RaidenX on X",
  JOIN_DISCORD: "Join RaidenX Discord",
  JOIN_TELEGRAM: "Join RaidenX Telegram",
  REFER_FRIEND: "Refer a friend and make a first trade",
  TRADING_VOLUME: "Trading volume points",
};

const LeaderboardSidebar: React.FC<{
  missions: Mission[];
  resolvedMissionIds: string[];
  handleResolveMission: (
    missionId: string,
    isAutoResolve: boolean
  ) => Promise<void>;
  getResolvedMissions: () => Promise<void>;
}> = ({
  missions,
  resolvedMissionIds,
  getResolvedMissions,
  handleResolveMission,
}) => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);

  useEffect(() => {
    if (!accessToken) return;

    const missionsToResolve = missions.filter(
      (mission) =>
        mission.isNeedVerify && !resolvedMissionIds.includes(mission.missionId)
    );

    Promise.all(
      missionsToResolve.map((mission) =>
        handleResolveMission(mission.missionId, true)
      )
    ).then(() => {
      getResolvedMissions().then();
    });
  }, [accessToken]);

  const openLinkMission = async (mission: Mission) => {
    if (!accessToken)
      return toastWarning("Warning", "Please login to continue");

    if (!mission.link) return;

    if (resolvedMissionIds.includes(mission.missionId)) return;

    window.open(mission.link, "_blank");
    if (mission.link) {
      await handleResolveMission(mission.missionId, false);
    }
    getResolvedMissions().then();
  };

  const iconMap: { [key: string]: React.ReactNode } = {
    [MISSIONS.MAKE_FIRST_TRADE]: (
      <FlashIcon className="h-[16px] w-[16px] md:h-[20px] md:w-[20px]" />
    ),
    [MISSIONS.JOIN_TELEGRAM_ANNOUNCEMENT]: (
      <NotificationIcon className="h-[16px] w-[16px] md:h-[20px] md:w-[20px]" />
    ),
    [MISSIONS.FOLLOW_X]: (
      <XIcon className="h-[16px] w-[16px] md:h-[20px] md:w-[20px]" />
    ),
    [MISSIONS.JOIN_DISCORD]: (
      <DiscordIcon className="h-[16px] w-[16px] md:h-[20px] md:w-[20px]" />
    ),
    [MISSIONS.JOIN_TELEGRAM]: (
      <Telegram className="h-[16px] w-[16px] md:h-[20px] md:w-[20px]" />
    ),
    [MISSIONS.REFER_FRIEND]: (
      <IconRose className="h-[16px] w-[16px] md:h-[20px] md:w-[20px]" />
    ),
    [MISSIONS.TRADING_VOLUME]: (
      <IconTrading className="h-[16px] w-[16px] md:h-[20px] md:w-[20px]" />
    ),
  };

  const getMissionPointsText = (mission: Mission): string => {
    switch (mission.name) {
      case MISSIONS.TRADING_VOLUME:
        return "+1 point/$1";
      case MISSIONS.REFER_FRIEND:
        return "+5 points/friend";
      default:
        return `+${mission.points} points`;
    }
  };

  return (
    <div className="bg-black-900 h-full">
      <div className="text-white-1000 mb-2 hidden items-center rounded-[2px] px-[12px] text-[16px] font-semibold md:flex">
        Missions
      </div>
      <div className="mt-2 flex flex-col">
        {missions.map((mission, index) => (
          <div
            key={index}
            className="border-white-50 hover:bg-white-50 flex min-h-[50px] items-center justify-between gap-2 border-b p-2 md:h-full md:max-h-[62px] md:gap-3 md:px-3 md:py-4"
          >
            <div className="flex items-center gap-2 md:gap-3 md:whitespace-nowrap">
              <div className="bg-white-50 h-[32px] w-[32px] flex-shrink-0 rounded-[8px] p-2 md:h-full md:max-h-[36px] md:w-full md:max-w-[36px]">
                {iconMap[mission.name]}
              </div>
              <div>
                <p className="text-white-1000 text-[12px] font-medium leading-[18px] md:text-[14px] md:font-semibold md:leading-[20px]">
                  {mission.name}
                </p>
                <p className="text-white-700 text-[10px] font-normal leading-[16px] md:text-[12px] md:leading-[18px]">
                  {mission.description}
                </p>
              </div>
            </div>

            {resolvedMissionIds.includes(mission.missionId) ? (
              <div className="border-white-50 shadow-button-primary flex h-[30px] gap-1 rounded-[40px] border px-4 py-[6px] text-right md:h-[34px] md:py-2">
                <button className="text-white-700 whitespace-nowrap text-[12px] font-medium leading-[18px] md:font-semibold">
                  Earned
                </button>
                <IconCheck />
              </div>
            ) : (
              <div className="border-brand-900 shadow-button-secondary h-[30px] rounded-[40px] border px-4 py-[6px] text-right md:h-[34px] md:py-2">
                <button
                  onClick={() => openLinkMission(mission)}
                  className="text-brand-600 whitespace-nowrap text-[12px] font-medium leading-[18px] md:font-semibold"
                >
                  {getMissionPointsText(mission)}
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

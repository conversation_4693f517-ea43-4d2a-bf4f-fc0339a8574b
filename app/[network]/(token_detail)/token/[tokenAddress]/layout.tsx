import { TPair } from "@/types";
import { Metadata } from "next";
import { headers } from "next/headers";
import config from "@/config";
import { RootPairProvider } from "../../provider";
import { formatNumberWithSubscriptZeros } from "@/utils/format";
import { normalizeStructTag } from "@mysten/sui/utils";
import { getMarketApiEndpoint } from "@/utils/network";

export const revalidate = 15;

interface LayoutProps {
  desktop: React.ReactNode;
  mobile: React.ReactNode;
  params: Promise<{ network: string; tokenAddress: string }>;
}

async function getTokenDetail(network: string, tokenAddress: string) {
  let standardTokenAddress = tokenAddress;
  try {
    standardTokenAddress = normalizeStructTag(decodeURIComponent(tokenAddress));
  } catch (error) {
    console.warn(
      `standardTokenAddress ${decodeURIComponent(
        tokenAddress
      )} throw error: ${error}, use original token address`
    );
  }
  const url = `${getMarketApiEndpoint(
    network
  )}/${network}/tokens/${standardTokenAddress}/top-pair`;
  const res = await fetch(url, {
    next: { revalidate: 5 }, // Clear cache after 5 seconds
  });
  const pair: TPair = await res.json();
  return pair;
}

export async function generateMetadata({
  params,
}: LayoutProps): Promise<Metadata> {
  const { network, tokenAddress } = await params;
  const pair = await getTokenDetail(network, tokenAddress);

  const networkName = network.toUpperCase();
  const description = `Fastest trading & sniping bot on ${networkName}, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX`;
  const title = `${pair?.tokenBase?.symbol?.toUpperCase()} $${formatNumberWithSubscriptZeros(
    pair?.tokenBase?.priceUsd
  )} ${pair?.tokenBase?.name} / ${pair?.tokenQuote?.symbol} on ${
    pair?.dex?.name
  } - Raidenx`;
  return {
    metadataBase: new URL(config.appUrl),
    title,
    description,
    openGraph: {
      title,
      description,
      images: [
        pair?.tokenBase?.bannerImageUrl ||
          pair?.tokenBase?.logoImageUrl ||
          "/open-graph.png",
      ],
    },
    keywords: [
      "RaidenX",
      "RaidenXTradingTerminal",
      "RaidenXTradingBot",
      "RaidenXTGBot",
      "RaidenXTelegramBot",
      "RaidenXSniperBot",
      `${networkName}TradingTerminal`,
      `${networkName}TradingBot`,
      `${networkName}MemeCoin`,
      `${networkName}TGBot`,
      `${networkName}TelegramBot`,
      `${networkName}SniperBot`,
      `${networkName}CopyTrade`,
    ],
  };
}

export default async function PairLayout({
  desktop,
  mobile,
  params,
}: LayoutProps) {
  const headersList = await headers();
  const isMobile = (headersList.get("user-agent") || "")?.includes("Mobile");

  const { network, tokenAddress } = await params;
  const pair = await getTokenDetail(network, tokenAddress);

  return (
    <RootPairProvider externalPair={pair}>
      {isMobile ? mobile : desktop}
    </RootPairProvider>
  );
}

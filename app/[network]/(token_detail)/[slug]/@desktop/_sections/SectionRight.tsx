"use client";
import React, {
  useCallback,
  useMemo,
  useState,
  useContext,
  useEffect,
} from "react";
import { OrderForm, WatchList, OrderFormExternalWallet } from "@/components";
import { RootState } from "@/store";
import { useSelector } from "react-redux";
import { PoolRelated } from "@/components/Pair/PoolRelated";
import { TPair } from "@/types/pair.type";
import CurveProcessing from "@/components/Pair/CurveProcessing";
import { RootPairContext } from "@/app/[network]/(token_detail)/provider";

const TITLE_TABS = {
  WATCH_LIST: "Watchlist",
  OHTER_POOLS: "Other pools",
};

export const SectionRight = () => {
  const [tab, setTab] = useState(TITLE_TABS.WATCH_LIST);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );
  const { pair } = useContext(RootPairContext) as { pair: TPair };
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const TABS = useMemo(
    () => [
      {
        name: TITLE_TABS.WATCH_LIST,
        content: <WatchList />,
        isPrivate: true,
      },
      {
        name: TITLE_TABS.OHTER_POOLS,
        content: (
          <PoolRelated hideTitle customWrapperClass="py-0" pair={pair} />
        ),
        isPrivate: false,
      },
    ],
    [pair?.slug]
  );

  const renderTabContent = useCallback(() => {
    const tabFound = TABS.find((item) => item.name === tab);
    return tabFound?.content;
  }, [tab]);

  if (!isClient) return <></>;
  return (
    <div className="border-neutral-alpha-50 bg-neutral-alpha-50 relative h-full w-[325px] border-l">
      <div className="flex h-full w-[324px] flex-col">
        <div>
          {isExternalWallet ? (
            <OrderFormExternalWallet />
          ) : (
            <OrderForm pair={pair} />
          )}

          <CurveProcessing />
        </div>
        <div className="flex flex-1 flex-col px-[8px] py-[12px] pb-0">
          <div className="border-neutral-alpha-50 flex items-center justify-between border-b">
            <div className="text-neutral-alpha-1000 action-sm-semibold-12 hide-scroll flex items-center gap-2 overflow-x-scroll">
              <div className="flex">
                {TABS.map((item) => {
                  if (item.isPrivate && !accessToken) {
                    return null;
                  }
                  return (
                    <div
                      onClick={() => setTab(item.name)}
                      key={item.name}
                      className={`hover:text-neutral-alpha-1000 active-tab mr-[2px] flex w-max cursor-pointer items-center gap-1 px-[4px] py-[3px] text-[12px] leading-[16px] hover:font-semibold ${
                        tab === item.name
                          ? "text-neutral-alpha-1000 border-neutral-alpha-500 border-b font-semibold"
                          : "text-neutral-alpha-800 border-0 font-normal"
                      }`}
                    >
                      {item.name}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <div className="flex-1">{renderTabContent()}</div>
        </div>
      </div>
    </div>
  );
};

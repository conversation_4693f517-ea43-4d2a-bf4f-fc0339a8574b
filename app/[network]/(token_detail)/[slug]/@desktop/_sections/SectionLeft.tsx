"use client";

import React, { useContext, useEffect, useState } from "react";
import {
  ChevronLeftIcon,
  FlashIconBoost,
  StarIcon,
  IWalletIcon,
} from "@/assets/icons";
import config from "@/config";
import Storage from "@/libs/storage";
import moment from "moment";
import { getTimeFormatBoots, multipliedBN, sleep } from "@/utils/helper";
import { RootPairContext } from "@/app/[network]/(token_detail)/provider";
import { TPair, TPairPrice, TPairStats, TPairToken } from "@/types";
import { TokenInfos } from "@/components/Pair/TokenInfos";
import { PairTradingStats } from "@/components/Pair/PairTradingStats";
import { AppAvatarToken, AppButton, AppCopy, AppNumber } from "@/components";
import { PairAuditCheck } from "@/components/Pair/AuditCheck";
import { formatNumber } from "@/utils/format";
import { useFavouritePairs } from "@/hooks/useFavouritePairs";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { getCirculatingSupply, overridePairStatIfNeed } from "@/utils/pair";
import { isEmpty } from "lodash";
import ModalBoost from "@/modals/ModalBoost";
import { usePairBoost } from "@/app/[network]/(token_detail)/_hooks/usePairBoost";
import { Advertisement } from "@/components/Advertisement";
import { setIsShowModalWalletTracker } from "@/store/metadata.store";
import Image from "next/image";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/index";

type Props = {
  isHideLeftPanelState: boolean;
  setIsHideLeftPanelState: (isHideLeftPanelState: boolean) => void;
};

const TABS_CHANGE = [
  {
    value: "5m",
    name: "5M",
  },
  {
    value: "1h",
    name: "1H",
  },
  {
    value: "6h",
    name: "6H",
  },
  {
    value: "24h",
    name: "24H",
  },
];

const CollapseContent = ({
  isHideLeftPanelState,
  setIsHideLeftPanelState,
}: Props) => {
  const [tokenBase, setTokenBase] = useState<TPairToken | any>({});
  const { pair, pairPrice } = useContext(RootPairContext) as {
    pair: TPair;
    pairPrice: TPairPrice;
  };
  const totalSupply = pair?.tokenBase?.totalSupply;
  const { handleFavourite, isFavourite } = useFavouritePairs();
  const [liquidityUsd, setLiquidityUsd] = useState("0");
  const [capUsd, setCapUsd] = useState("0");
  const [stats, setStats] = useState<TPairStats>();
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const handleWhenPairStatsChange = async (event: TBroadcastEvent) => {
    let data: any = event.detail;
    if (data.pairId !== pair.pairId) {
      return;
    }
    setIsUpdating(true);

    setLiquidityUsd(data?.liquidityUsd);
    setCapUsd(multipliedBN(data?.priceUsd, getCirculatingSupply(pair)));
    data = overridePairStatIfNeed(data);
    setStats(data);
    await sleep(300);
    setIsUpdating(false);
  };

  useEffect(() => {
    if (isEmpty(pair) || !pairPrice) return;
    setLiquidityUsd(pair.liquidityUsd);
    setCapUsd(multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair)));
  }, [pair?.pairId, pairPrice]);

  useEffect(() => {
    setTokenBase(pair?.tokenBase);
  }, [pair?.pairId]);
  useEffect(() => {
    if (isEmpty(pair) || !pair?.stats) return;
    const pairStatsRes = overridePairStatIfNeed(pair.stats);
    setStats(pairStatsRes);
  }, [pair?.stats]);

  const handleWhenSocialsChange = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    if (data.tokenAddress !== pair?.tokenBase?.address) {
      return;
    }

    setTokenBase({
      ...tokenBase,
      logoImageUrl: data.logo_image_url,
      bannerImageUrl: data.banner_image_url,
      socials: {
        ...tokenBase.socials,
        websites: data.websites,
        socials: data.socials,
      },
    });
  };

  useEffect(() => {
    if (isEmpty(pair)) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
      handleWhenSocialsChange
    );

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
        handleWhenSocialsChange
      );
    };
  }, [pair.pairId]);
  useEffect(() => {
    if (isEmpty(pair)) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.PAIR_STATS_UPDATED,
      handleWhenPairStatsChange
    );

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.PAIR_STATS_UPDATED,
        handleWhenPairStatsChange
      );
    };
  }, [pair?.slug]);
  const ItemMetadata: React.FC<any> = ({
    title,
    content,
    decimals,
    isUSD,
    isNeedUpdate,
  }) => {
    return (
      <div className="flex flex-col items-start pb-2 pl-2">
        <div className="text-neutral-alpha-500 mb-[2px] text-[10px] uppercase leading-[1.6]">
          {title}
        </div>

        <div
          className={`flex w-max items-center gap-1 font-semibold leading-[20px] transition-all duration-300 ${
            isNeedUpdate && isUpdating
              ? "bg-neutral-alpha-600"
              : "bg-transparent"
          }`}
        >
          <AppNumber
            className="text-[12px] font-semibold leading-[18px]"
            value={content}
            decimals={decimals || 8}
            isForUSD={isUSD}
          />
        </div>
      </div>
    );
  };
  return (
    <div className="transition-all duration-300 ease-linear">
      <div className="mb-[16px]">
        <div className="flex items-center justify-between">
          <div className="mx-[6px] mt-[12px] flex w-full flex-col items-center gap-2">
            <div className="max-tablet:hidden">
              <AppAvatarToken
                size={40}
                className="h-10 w-10"
                image={tokenBase?.logoImageUrl || tokenBase?.iconUrl}
              />
            </div>
            <div>
              <div className="mb-[2px] flex items-center gap-1 text-[16px] font-medium leading-[24px]">
                <div className="max-w-[64px] truncate">
                  {pair?.tokenBase?.symbol ? pair?.tokenBase?.name : "Unknown"}
                </div>
                <AppCopy
                  message={pair?.tokenBase?.address}
                  className="h-[14px] w-[14px]"
                />
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center">
          <div
            className="bg-neutral-alpha-100 hover:bg-neutral-alpha-50 max-tablet:hidden flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-[4px]"
            onClick={() => handleFavourite(pair.network, pair)}
          >
            <StarIcon
              className={`h-[14px] w-[14px] ${
                isFavourite(pair?.pairId) ? "text-yellow-500" : ""
              }`}
            />
          </div>
        </div>
      </div>
      <div className="mb-4 flex items-center justify-center">
        <div className="flex flex-1 flex-col gap-2">
          <ItemMetadata
            title="FDV"
            content={
              Number(totalSupply || 0) * Number(pairPrice?.priceUsd || 0)
            }
            decimals={pair?.tokenBase?.decimals}
            isUSD
          />
          <ItemMetadata
            title="MC"
            content={capUsd}
            decimals={2}
            isUSD={!!+capUsd}
          />
          <ItemMetadata
            title="LIQ"
            content={liquidityUsd}
            decimals={pair?.tokenBase?.decimals}
            isUSD={!!+multipliedBN(pair?.liquidity, pair?.tokenQuote?.priceUsd)}
          />
          <ItemMetadata
            title="PRICE USD"
            content={pairPrice?.priceUsd}
            decimals={pair?.tokenBase?.decimals}
            isUSD={!!+pairPrice?.priceUsd}
            isNeedUpdate
          />
          <ItemMetadata
            isShowLogo
            title={`PRICE ${pair?.tokenQuote?.symbol || ""}`}
            content={pairPrice?.price}
            decimals={pair?.tokenBase?.decimals}
            isNeedUpdate
          />
        </div>
        <div
          className="border-neutral-alpha-50 bg-neutral-alpha-50 flex h-[104px] w-[14px] cursor-pointer items-center justify-center rounded-[4px] border"
          onClick={() => setIsHideLeftPanelState(!isHideLeftPanelState)}
        >
          <ChevronLeftIcon className="rotate-180 transform" />
        </div>
      </div>
      <div className="border-neutral-alpha-50 flex flex-col border-b">
        {TABS_CHANGE.map((tab, index) => {
          return (
            <div
              key={index}
              className={`hover:bg-white-100 hover:border-white-500 flex cursor-pointer flex-col items-center justify-center p-2 hover:border-b`}
            >
              <div className="text-neutral-alpha-500 text-[12px] font-normal uppercase leading-[1.5]">
                {tab.name}
              </div>

              <div
                className={`${
                  (stats?.percent[tab.value] ?? 0) > 0
                    ? "text-green-500"
                    : "text-red-500"
                } text-[12px] font-semibold leading-[18px]`}
              >
                {stats?.percent[tab.value]
                  ? `${formatNumber(stats?.percent[tab.value], 2)}%`
                  : "-"}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const Footer = () => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const dispatch = useDispatch<AppDispatch>();
  const [isNewTrade, setIsNewTrade] = useState<boolean>(false);

  const handleWhenMakerTrade = (event: TBroadcastEvent) => {
    setIsNewTrade(true);
  };

  useEffect(() => {
    AppBroadcast.on(
      BROADCAST_EVENTS.MAKER_TRADE_SUCCEEDED,
      handleWhenMakerTrade
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.MAKER_TRADE_SUCCEEDED,
        handleWhenMakerTrade
      );
    };
  }, []);

  return (
    <div
      className="border-neutral-alpha-50 absolute bottom-0 flex w-full items-center justify-between border-t px-[12px] py-[10px]"
      style={{
        backdropFilter: "blur(7.5px)",
        background:
          "linear-gradient(0deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%)",
      }}
    >
      {accessToken && (
        <div
          onClick={() => {
            dispatch(setIsShowModalWalletTracker({ isShow: true }));
            setIsNewTrade(false);
          }}
          className="body-md-regular-14 flex cursor-pointer items-center gap-2"
        >
          <div className="relative">
            <Image
              alt="wallet"
              src={IWalletIcon}
              height={16}
              width={16}
              unoptimized
            />
            <span
              className={`bg-brand-500 absolute right-0 top-0.5 z-10 h-2 w-2 rounded-full ${
                !isNewTrade ? "hidden" : "flex"
              }`}
            >
              <span className="bg-brand-500 absolute inline-flex h-full w-full animate-ping rounded-full opacity-75" />
            </span>
          </div>
          Wallet Tracker
        </div>
      )}

      <div className="flex items-center gap-3">
        <a
          href={config.linkSocial.twitter}
          target="_blank"
          className="text-neutral-alpha-800 hover:text-neutral-alpha-1000 "
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
          >
            <path
              d="M14.5069 3.4165H16.9102L11.6598 9.41736L17.8364 17.5832H13.0002L9.21222 12.6306L4.87795 17.5832H2.47324L8.08906 11.1646L2.16376 3.4165H7.12282L10.5468 7.9433L14.5069 3.4165ZM13.6635 16.1447H14.9951L6.39923 4.77941H4.97021L13.6635 16.1447Z"
              fill="white"
              fillOpacity="0.8"
            />
          </svg>
        </a>
        <a
          href={config.linkSocial.telegram}
          target="_blank"
          className="text-neutral-alpha-800 hover:text-neutral-alpha-1000 "
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="14"
            viewBox="0 0 16 14"
            fill="none"
          >
            <path
              d="M1.41369 6.49673L14.2376 0.954551C14.8406 0.693923 15.4941 1.20108 15.3912 1.84995L13.6379 12.9133C13.5296 13.5966 12.6838 13.858 12.2088 13.3549L9.33142 10.3069C8.76713 9.70922 8.72291 8.78933 9.22726 8.14023L11.2589 5.52545C11.3751 5.37597 11.1917 5.17933 11.0345 5.28479L6.99503 7.99464C6.30955 8.4545 5.47782 8.64366 4.66088 8.52551L1.625 8.08643C0.793347 7.96615 0.642337 6.83009 1.41369 6.49673Z"
              fill="white"
              fillOpacity="0.8"
            />
          </svg>
        </a>
        <a
          href={config.customerSupportUrl}
          target="_blank"
          className="text-white-1000 cursor-pointer text-[12px] leading-[18px] hover:underline"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
          >
            <path
              d="M6.03678 13.73C6.01303 13.4917 5.90733 13.2677 5.73649 13.0968C5.54114 12.9015 5.27627 12.7917 5 12.7917H3.125V16.3333C3.125 16.6096 3.23483 16.8745 3.43018 17.0698C3.62553 17.2652 3.8904 17.375 4.16667 17.375H5C5.27627 17.375 5.54114 17.2652 5.73649 17.0698C5.90733 16.899 6.01303 16.675 6.03678 16.4367L6.04167 16.3333V13.8333L6.03678 13.73ZM12.7083 16.3333V13.8333C12.7083 13.2255 12.9499 12.6428 13.3797 12.2131C13.8095 11.7833 14.3922 11.5417 15 11.5417H16.875V10.5C16.875 8.67664 16.151 6.92766 14.8617 5.63835C13.5723 4.34903 11.8234 3.625 10 3.625C8.17664 3.625 6.42766 4.34903 5.13835 5.63835C3.84903 6.92766 3.125 8.67664 3.125 10.5V11.5417H5C5.60779 11.5417 6.19051 11.7833 6.62028 12.2131C7.05005 12.6428 7.29167 13.2255 7.29167 13.8333V16.3333C7.29167 16.9411 7.05005 17.5238 6.62028 17.9536C6.19051 18.3834 5.60779 18.625 5 18.625H4.16667C3.55888 18.625 2.97616 18.3834 2.54639 17.9536C2.11662 17.5238 1.875 16.9411 1.875 16.3333V10.5C1.875 8.34512 2.73082 6.27829 4.25456 4.75456C5.77829 3.23082 7.84512 2.375 10 2.375C12.1549 2.375 14.2217 3.23082 15.7454 4.75456C17.2692 6.27829 18.125 8.34512 18.125 10.5V16.3333C18.125 16.9411 17.8834 17.5238 17.4536 17.9536C17.0238 18.3834 16.4411 18.625 15.8333 18.625H15C14.3922 18.625 13.8095 18.3834 13.3797 17.9536C13.0036 17.5775 12.7718 17.0843 12.7197 16.5596L12.7083 16.3333ZM13.9583 16.3333L13.9632 16.4367C13.987 16.675 14.0927 16.899 14.2635 17.0698C14.4589 17.2652 14.7237 17.375 15 17.375H15.8333C16.1096 17.375 16.3745 17.2652 16.5698 17.0698C16.7652 16.8745 16.875 16.6096 16.875 16.3333V12.7917H15C14.7237 12.7917 14.4589 12.9015 14.2635 13.0968C14.0682 13.2922 13.9583 13.5571 13.9583 13.8333V16.3333Z"
              fill="white"
            />
          </svg>
        </a>
      </div>
    </div>
  );
};

export const SectionLeft = () => {
  const isHideLeftPanel = Storage.getIsHideLeftPanel();
  const [isHideLeftPanelState, setIsHideLeftPanelState] =
    useState(isHideLeftPanel);
  useEffect(() => {
    Storage.setIsHideLeftPanel(isHideLeftPanelState);
  }, [isHideLeftPanelState]);
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };
  const { isShowBoostModal, setIsShowBoostModal } = usePairBoost();

  console.log("pair===================", pair);
  return (
    <>
      <div className="h-full">
        <div
          className={`border-neutral-alpha-50 bg-neutral-alpha-50 relative h-full border-r ${
            isHideLeftPanelState ? "w-[90px]" : "w-[324px]"
          } linear transition-[width_0.5s]`}
        >
          {!isHideLeftPanelState ? (
            <div className={`part-info customer-scroll h-full overflow-auto`}>
              <TokenInfos pair={pair} />
              <PairTradingStats
                pair={pair}
                setIsHideLeftPanelState={setIsHideLeftPanelState}
                isHideLeftPanelState={isHideLeftPanelState}
              />

              <div className="gap-2 p-2">
                <AppButton
                  size="large"
                  variant="boost"
                  onClick={() => setIsShowBoostModal(true)}
                >
                  <div className="flex items-center gap-1">
                    <FlashIconBoost />
                    <span>Boost</span>

                    {pair?.tokenBase?.boostFactor &&
                      moment(
                        pair?.tokenBase?.isBoostedUntil,
                        "YYYY-MM-DD H:mm:ss.S Z"
                      ).valueOf() > moment().valueOf() && (
                        <span className="body-xs-medium-10 inline-flex h-[20px] items-center justify-center rounded border border-orange-900 bg-orange-900 px-1">
                          {getTimeFormatBoots(pair?.tokenBase?.boostFactor)}
                        </span>
                      )}
                  </div>
                </AppButton>
              </div>

              <Advertisement />

              <PairAuditCheck />
              <Footer />
            </div>
          ) : (
            <div
              className={`border-white-50 bg-white-50 h-full border border-r`}
            >
              <CollapseContent
                setIsHideLeftPanelState={setIsHideLeftPanelState}
                isHideLeftPanelState={isHideLeftPanelState}
              />
            </div>
          )}
        </div>
      </div>
      {isShowBoostModal && (
        <ModalBoost
          isOpen={isShowBoostModal}
          onClose={() => setIsShowBoostModal(false)}
          pair={pair}
        />
      )}
    </>
  );
};

"use client";

import { useMediaQuery } from "react-responsive";
import { SectionLeft } from "./SectionLeft";
import { SectionCenter } from "./SectionCenter";
import { SectionRight } from "./SectionRight";
import MobileNavigationTabs from "@/app/[network]/(token_detail)/[slug]/@mobile/_tabs";

export const NavigationSections = () => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  if (isMobile) {
    return <MobileNavigationTabs />;
  }
  return (
    <div className="tablet:flex hidden h-[calc(100vh-52px)] overflow-hidden">
      <SectionLeft />
      <div className="flex h-full w-[calc(100vw-648px)] flex-1 flex-col">
        <SectionCenter />
      </div>
      <SectionRight />
    </div>
  );
};

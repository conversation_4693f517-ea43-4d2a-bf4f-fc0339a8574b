import { headers } from "next/headers";
import { RootPairProvider } from "../provider";
import { TPair } from "@/types";
import { Metadata } from "next";
import { formatNumberWithSubscriptZeros } from "@/utils/format";
import { getMarketApiEndpoint } from "@/utils/network";
export const revalidate = 15;

interface LayoutProps {
  desktop: React.ReactNode;
  mobile: React.ReactNode;
  params: Promise<{ network: string; slug: string }>;
}

async function getTokenDetail(network: string, slug: string) {
  const url = `${getMarketApiEndpoint(
    network
  )}/${network}/pairs/${decodeURIComponent(slug)}`;
  console.log("url", url);
  const res = await fetch(url, {
    next: { revalidate: 5 }, // Clear cache after 5 seconds
  });
  const pair: TPair = await res.json();
  console.log("pair", pair);
  return pair;
}

export async function generateMetadata({
  params,
}: LayoutProps): Promise<Metadata> {
  const { network, slug } = await params;
  const pair = await getTokenDetail(network, slug);

  const networkName = network.toUpperCase();
  const description = `Fastest trading & sniping bot on ${networkName}, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX`;
  const title = `${pair?.tokenBase?.symbol?.toUpperCase()} $${formatNumberWithSubscriptZeros(
    pair?.tokenBase?.priceUsd
  )} ${pair?.tokenBase?.name} / ${pair?.tokenQuote?.symbol} on ${
    pair?.dex?.name
  } - RaidenX | The best trading terminal on ${networkName}`;

  const imageUrl =
    pair?.tokenBase?.bannerImageUrl ||
    pair?.tokenBase?.logoImageUrl ||
    "/open-graph.png";
  return {
    title,
    description,
    icons: {
      icon: "/favicon.ico",
      apple: "/logo192.png",
    },
    openGraph: {
      title,
      description,
      images: [imageUrl],
      url: `https://raidenx.io/${network}/${slug}`,
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [imageUrl],
    },
    other: {
      "msapplication-TileColor": "#da532c",
      canonical: `https://raidenx.io/${network}/${slug}`,
    },
    keywords: [
      "RaidenX",
      "RaidenXTradingTerminal",
      "RaidenXTradingBot",
      "RaidenXTGBot",
      "RaidenXTelegramBot",
      "RaidenXSniperBot",
      `${networkName}TradingTerminal`,
      `${networkName}TradingBot`,
      `${networkName}MemeCoin`,
      `${networkName}TGBot`,
      `${networkName}TelegramBot`,
      `${networkName}SniperBot`,
      `${networkName}CopyTrade`,
    ],
  };
}

export default async function Layout({ desktop, mobile, params }: LayoutProps) {
  const headersList = await headers();
  const isMobile = (headersList.get("user-agent") || "")?.includes("Mobile");
  const { network, slug } = await params;
  const pair = await getTokenDetail(network, slug);
  return (
    <RootPairProvider externalPair={pair}>
      {isMobile ? mobile : desktop}
    </RootPairProvider>
  );
}

import { OrderForm } from "@/components/OrderForm";
import { TradingView } from "@/components/TradingView";
import { TPair } from "@/types";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { OrderFormExternalWallet } from "@/components";

export const TabTrade = ({ pair }: { pair: TPair }) => {
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );
  return (
    <div className="mx-auto flex h-full flex-col">
      <div className="flex-1">
        <>{pair?.pairId && <TradingView pair={pair} />}</>
      </div>
      <div className="h-max">
        {isExternalWallet ? (
          <OrderFormExternalWallet />
        ) : (
          <OrderForm pair={pair} />
        )}
      </div>
    </div>
  );
};
